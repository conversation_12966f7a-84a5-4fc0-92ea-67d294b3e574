// Base types used across the application

export type ObjectId = string

export interface BaseDocument {
  _id: ObjectId
  createdAt: Date
  updatedAt: Date
}

export interface Location {
  city?: string
  state?: string
  country?: string
  coordinates?: [number, number] // [longitude, latitude]
  remote?: boolean
}

export interface SalaryRange {
  min: number
  max: number
  currency: string
  period: 'hourly' | 'monthly' | 'yearly'
}

export interface ContactInfo {
  email?: string
  phone?: string
  website?: string
  linkedin?: string
}

export interface SocialLinks {
  linkedin?: string
  twitter?: string
  facebook?: string
  instagram?: string
  github?: string
  website?: string
}

export interface FileUpload {
  id: ObjectId
  filename: string
  originalName: string
  contentType: string
  size: number
  url: string
  uploadDate: Date
}

export interface PaginationParams {
  page: number
  limit: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface SearchFilters {
  q?: string
  location?: string
  dateFrom?: Date
  dateTo?: Date
  isActive?: boolean
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    field?: string
    details?: Record<string, any>
  }
  meta?: {
    pagination?: PaginationMeta
    timestamp: string
    requestId: string
    executionTime?: number
  }
}

export interface Skill {
  name: string
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  yearsOfExperience?: number
  verified?: boolean
}

export interface Address {
  street?: string
  city: string
  state?: string
  country: string
  postalCode?: string
  coordinates?: [number, number]
}

export interface TimeRange {
  start: Date
  end?: Date
}

export interface Rating {
  score: number
  maxScore: number
  reviewCount?: number
}

export interface Notification {
  id: ObjectId
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  read: boolean
  createdAt: Date
  expiresAt?: Date
  actionUrl?: string
}

export interface AuditLog {
  id: ObjectId
  action: string
  entityType: string
  entityId: ObjectId
  userId: ObjectId
  changes?: Record<string, any>
  metadata?: Record<string, any>
  timestamp: Date
  ipAddress?: string
  userAgent?: string
}
