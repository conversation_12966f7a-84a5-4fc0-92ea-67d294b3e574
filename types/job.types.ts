import { BaseDocument, Location, SalaryRange, Skill, ObjectId } from './base.types'

export type JobStatus = 'draft' | 'published' | 'paused' | 'expired' | 'filled' | 'cancelled'

export type EmploymentType = 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship' | 'temporary'

export type ExperienceLevel = 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'executive'

export type WorkArrangement = 'onsite' | 'remote' | 'hybrid'

export interface JobLocation extends Location {
  workArrangement: WorkArrangement
  timezone?: string
  relocationAssistance?: boolean
}

export interface JobRequirements {
  education?: {
    level: 'high_school' | 'associate' | 'bachelor' | 'master' | 'phd'
    field?: string
    required: boolean
  }
  experience: {
    minimum: number
    preferred?: number
    unit: 'months' | 'years'
  }
  skills: {
    required: Skill[]
    preferred: Skill[]
  }
  languages?: {
    language: string
    proficiency: 'basic' | 'conversational' | 'fluent' | 'native'
    required: boolean
  }[]
  certifications?: string[]
  other?: string[]
}

export interface JobBenefits {
  health: string[]
  financial: string[]
  timeOff: string[]
  professional: string[]
  lifestyle: string[]
  other: string[]
}

export interface JobApplicationSettings {
  applicationDeadline?: Date
  maxApplications?: number
  requireCoverLetter: boolean
  requireResume: boolean
  requirePortfolio: boolean
  customQuestions?: {
    id: string
    question: string
    type: 'text' | 'textarea' | 'select' | 'multiselect' | 'boolean' | 'file'
    options?: string[]
    required: boolean
  }[]
  autoReject?: {
    enabled: boolean
    criteria: {
      minExperience?: number
      requiredSkills?: string[]
      location?: string[]
    }
  }
}

export interface Job extends BaseDocument {
  title: string
  slug: string
  company: ObjectId
  postedBy: ObjectId
  department?: string
  description: string
  responsibilities: string[]
  requirements: JobRequirements
  benefits: JobBenefits
  salary: SalaryRange
  location: JobLocation
  employment: EmploymentType
  experience: ExperienceLevel
  category: string
  tags: string[]
  status: JobStatus
  featured: boolean
  urgent: boolean
  confidential: boolean
  applicationSettings: JobApplicationSettings
  views: number
  applications: number
  saves: number
  shares: number
  publishedAt?: Date
  expiresAt?: Date
  lastUpdated: Date
  seoTitle?: string
  seoDescription?: string
  aiScore?: number
  aiTags?: string[]
}

export interface CreateJobRequest {
  title: string
  department?: string
  description: string
  responsibilities: string[]
  requirements: Partial<JobRequirements>
  benefits?: Partial<JobBenefits>
  salary: SalaryRange
  location: JobLocation
  employment: EmploymentType
  experience: ExperienceLevel
  category: string
  tags?: string[]
  featured?: boolean
  urgent?: boolean
  confidential?: boolean
  applicationSettings?: Partial<JobApplicationSettings>
  publishedAt?: Date
  expiresAt?: Date
}

export interface UpdateJobRequest {
  title?: string
  department?: string
  description?: string
  responsibilities?: string[]
  requirements?: Partial<JobRequirements>
  benefits?: Partial<JobBenefits>
  salary?: SalaryRange
  location?: JobLocation
  employment?: EmploymentType
  experience?: ExperienceLevel
  category?: string
  tags?: string[]
  status?: JobStatus
  featured?: boolean
  urgent?: boolean
  confidential?: boolean
  applicationSettings?: Partial<JobApplicationSettings>
  expiresAt?: Date
}

export interface JobSearchQuery {
  q?: string
  title?: string
  company?: string
  location?: string
  remote?: boolean
  employment?: EmploymentType[]
  experience?: ExperienceLevel[]
  category?: string[]
  skills?: string[]
  salaryMin?: number
  salaryMax?: number
  currency?: string
  datePosted?: 'today' | 'week' | 'month' | 'all'
  featured?: boolean
  urgent?: boolean
  status?: JobStatus[]
  page?: number
  limit?: number
  sort?: 'relevance' | 'date' | 'salary_high' | 'salary_low' | 'title' | 'company'
  order?: 'asc' | 'desc'
}

export interface JobSearchResult {
  jobs: Job[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  aggregations: {
    locations: { name: string; count: number }[]
    companies: { name: string; count: number }[]
    skills: { name: string; count: number }[]
    categories: { name: string; count: number }[]
    employment: { type: EmploymentType; count: number }[]
    experience: { level: ExperienceLevel; count: number }[]
    salaryRanges: { range: string; count: number }[]
  }
  searchTime: number
}

export interface JobRecommendation {
  job: Job
  matchScore: number
  matchReasons: string[]
  skillsMatch: {
    matched: string[]
    missing: string[]
    percentage: number
  }
  salaryMatch: boolean
  locationMatch: boolean
  experienceMatch: boolean
}

export interface SavedJob {
  id: ObjectId
  user: ObjectId
  job: ObjectId
  savedAt: Date
  notes?: string
  tags?: string[]
}

export interface JobAlert {
  id: ObjectId
  user: ObjectId
  name: string
  query: JobSearchQuery
  frequency: 'immediate' | 'daily' | 'weekly'
  isActive: boolean
  lastSent?: Date
  createdAt: Date
  updatedAt: Date
}

export interface JobView {
  id: ObjectId
  job: ObjectId
  user?: ObjectId
  ipAddress: string
  userAgent: string
  referrer?: string
  viewedAt: Date
  timeSpent?: number
}

export interface JobShare {
  id: ObjectId
  job: ObjectId
  user?: ObjectId
  platform: 'email' | 'linkedin' | 'twitter' | 'facebook' | 'whatsapp' | 'copy_link'
  sharedAt: Date
}

export interface JobStats {
  views: {
    total: number
    unique: number
    today: number
    thisWeek: number
    thisMonth: number
  }
  applications: {
    total: number
    today: number
    thisWeek: number
    thisMonth: number
  }
  saves: number
  shares: number
  conversionRate: number
  averageTimeOnPage: number
  topReferrers: { source: string; count: number }[]
  candidateLocations: { location: string; count: number }[]
  candidateExperience: { level: ExperienceLevel; count: number }[]
}

export interface JobAnalytics {
  jobId: ObjectId
  period: 'day' | 'week' | 'month'
  startDate: Date
  endDate: Date
  metrics: {
    impressions: number
    views: number
    applications: number
    saves: number
    shares: number
    clickThroughRate: number
    applicationRate: number
    qualityScore: number
  }
  demographics: {
    experience: { level: ExperienceLevel; count: number }[]
    locations: { location: string; count: number }[]
    skills: { skill: string; count: number }[]
    education: { level: string; count: number }[]
  }
}
