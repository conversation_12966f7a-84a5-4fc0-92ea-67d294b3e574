export type UserRole = 'job_seeker' | 'company' | 'admin'

export interface BaseUser {
  id: string
  email: string
  role: UserRole
  isVerified: boolean
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  profileCompleteness: number
  preferences: UserPreferences
}

export interface UserPreferences {
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
    jobAlerts: boolean
    applicationUpdates: boolean
    marketingEmails: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'private' | 'connections_only'
    showSalaryExpectations: boolean
    showContactInfo: boolean
    allowRecruiterContact: boolean
  }
  jobPreferences?: JobSeekerPreferences
  companyPreferences?: CompanyPreferences
}

export interface JobSeekerPreferences {
  jobTypes: string[]
  workModels: string[]
  industries: string[]
  experienceLevels: string[]
  salaryRange: {
    min: number
    max: number
    currency: string
  }
  locations: string[]
  remoteOnly: boolean
  willingToRelocate: boolean
  availabilityDate: string
}

export interface CompanyPreferences {
  industryFocus: string[]
  companySize: string[]
  hiringVolume: 'low' | 'medium' | 'high'
  budgetRange: {
    min: number
    max: number
    currency: string
  }
  preferredCandidateLocations: string[]
  remoteHiring: boolean
  internationalHiring: boolean
}

// Job Seeker specific fields
export interface JobSeekerProfile extends BaseUser {
  role: 'job_seeker'
  profile: {
    // Personal Information
    firstName: string
    lastName: string
    displayName?: string
    avatar?: string
    phone?: string
    dateOfBirth?: string
    gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
    
    // Location
    location: {
      city: string
      region: string
      country: string
      continent: string
      coordinates?: {
        latitude: number
        longitude: number
      }
    }
    
    // Professional Information
    currentTitle?: string
    experience: {
      level: 'entry' | 'mid' | 'senior' | 'executive'
      yearsOfExperience: number
      industries: string[]
    }
    
    // Education
    education: {
      level: 'high_school' | 'associate' | 'bachelor' | 'master' | 'phd' | 'other'
      field: string
      institution?: string
      graduationYear?: number
      certifications: string[]
    }
    
    // Skills & Expertise
    skills: {
      technical: string[]
      soft: string[]
      languages: Array<{
        language: string
        proficiency: 'basic' | 'intermediate' | 'advanced' | 'native'
      }>
    }
    
    // Work Preferences
    workPreferences: {
      jobTypes: string[]
      workModels: string[]
      industries: string[]
      salaryExpectation: {
        min: number
        max: number
        currency: string
        period: 'hour' | 'month' | 'year'
      }
      availabilityDate: string
      willingToRelocate: boolean
      maxCommuteDistance: number
      remoteOnly: boolean
    }
    
    // Professional Links
    links: {
      portfolio?: string
      linkedin?: string
      github?: string
      website?: string
      resume?: string
    }
    
    // Bio & Summary
    bio?: string
    summary?: string
    achievements: string[]
    
    // Work Authorization
    workAuthorization: {
      status: 'citizen' | 'permanent_resident' | 'work_visa' | 'student_visa' | 'requires_sponsorship'
      country: string
      visaType?: string
      expirationDate?: string
    }
  }
}

// Company specific fields
export interface CompanyProfile extends BaseUser {
  role: 'company'
  profile: {
    // Company Information
    companyName: string
    displayName?: string
    logo?: string
    website?: string
    foundedYear?: number
    
    // Company Details
    industry: string
    subIndustries: string[]
    companySize: '1-10' | '11-50' | '51-200' | '201-500' | '501-1000' | '1000+'
    companyType: 'startup' | 'small_business' | 'corporation' | 'non_profit' | 'government' | 'agency'
    
    // Location & Presence
    headquarters: {
      city: string
      region: string
      country: string
      continent: string
      address?: string
      coordinates?: {
        latitude: number
        longitude: number
      }
    }
    locations: Array<{
      city: string
      region: string
      country: string
      type: 'headquarters' | 'office' | 'remote_hub'
      employeeCount?: number
    }>
    
    // Company Culture & Values
    description: string
    mission?: string
    values: string[]
    culture: {
      workEnvironment: string[]
      benefits: string[]
      perks: string[]
      diversityCommitment?: string
    }
    
    // Hiring Information
    hiring: {
      currentOpenings: number
      hiringVolume: 'low' | 'medium' | 'high'
      averageTimeToHire: number
      preferredCandidateLocations: string[]
      remoteHiring: boolean
      internationalHiring: boolean
      visaSponsorship: boolean
      internshipPrograms: boolean
    }
    
    // Contact Information
    contacts: {
      hr?: {
        name: string
        email: string
        phone?: string
      }
      recruiting?: {
        name: string
        email: string
        phone?: string
      }
      general?: {
        email: string
        phone?: string
      }
    }
    
    // Verification & Credentials
    verification: {
      isVerified: boolean
      verificationDate?: string
      businessLicense?: string
      taxId?: string
      linkedinCompanyPage?: string
    }
    
    // Social Media & Links
    socialMedia: {
      linkedin?: string
      twitter?: string
      facebook?: string
      instagram?: string
      youtube?: string
    }
    
    // Company Metrics
    metrics: {
      employeeRating?: number
      glassdoorRating?: number
      employeeRetentionRate?: number
      diversityScore?: number
    }
    
    // Subscription & Plan
    subscription: {
      plan: 'free' | 'basic' | 'premium' | 'enterprise'
      features: string[]
      jobPostingLimit: number
      candidateSearchLimit: number
      expirationDate?: string
    }
  }
}

export type User = JobSeekerProfile | CompanyProfile

// Registration form data types
export interface JobSeekerRegistrationData {
  // Basic Info
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  phone?: string
  
  // Location
  city: string
  region: string
  country: string
  
  // Professional Info
  currentTitle?: string
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive'
  yearsOfExperience: number
  industry: string
  
  // Education
  educationLevel: 'high_school' | 'associate' | 'bachelor' | 'master' | 'phd' | 'other'
  fieldOfStudy: string
  
  // Work Preferences
  jobTypes: string[]
  workModels: string[]
  salaryMin: number
  salaryMax: number
  salaryCurrency: string
  availabilityDate: string
  willingToRelocate: boolean
  
  // Work Authorization
  workAuthorizationStatus: 'citizen' | 'permanent_resident' | 'work_visa' | 'student_visa' | 'requires_sponsorship'
  
  // Skills
  skills: string[]
  
  // Agreements
  termsAccepted: boolean
  privacyAccepted: boolean
  marketingEmails: boolean
}

export interface CompanyRegistrationData {
  // Basic Info
  email: string
  password: string
  confirmPassword: string
  companyName: string
  website?: string
  
  // Company Details
  industry: string
  companySize: '1-10' | '11-50' | '51-200' | '201-500' | '501-1000' | '1000+'
  companyType: 'startup' | 'small_business' | 'corporation' | 'non_profit' | 'government' | 'agency'
  foundedYear?: number
  
  // Location
  city: string
  region: string
  country: string
  address?: string
  
  // Contact Person
  contactPersonName: string
  contactPersonTitle: string
  contactPersonPhone?: string
  
  // Company Description
  description: string
  
  // Hiring Info
  currentOpenings: number
  hiringVolume: 'low' | 'medium' | 'high'
  remoteHiring: boolean
  internationalHiring: boolean
  visaSponsorship: boolean
  
  // Verification
  businessLicense?: string
  taxId?: string
  linkedinCompanyPage?: string
  
  // Agreements
  termsAccepted: boolean
  privacyAccepted: boolean
  marketingEmails: boolean
}
