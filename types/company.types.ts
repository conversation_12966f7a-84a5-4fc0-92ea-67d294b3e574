import { BaseDocument, Location, SocialLinks, FileUpload, ObjectId, Rating } from './base.types'

export type CompanySize = 'startup' | 'small' | 'medium' | 'large' | 'enterprise'

export type CompanySubscriptionPlan = 'starter' | 'professional' | 'enterprise'

export type CompanySubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'trial'

export interface CompanyLocation extends Location {
  isHeadquarters: boolean
  officeType: 'headquarters' | 'branch' | 'remote' | 'coworking'
  capacity?: number
}

export interface CompanyCulture {
  values?: string[]
  benefits?: string[]
  workEnvironment?: string
  diversity?: string
  perks?: string[]
  workLifeBalance?: string
  dresscode?: 'formal' | 'business_casual' | 'casual' | 'flexible'
  workingHours?: 'standard' | 'flexible' | 'remote_friendly'
}

export interface CompanySubscription {
  plan: CompanySubscriptionPlan
  status: CompanySubscriptionStatus
  stripeCustomerId?: string
  stripeSubscriptionId?: string
  currentPeriodStart?: Date
  currentPeriodEnd?: Date
  jobPostingLimit: number
  jobPostingsUsed: number
  featuredJobsLimit: number
  featuredJobsUsed: number
  candidateSearchLimit: number
  candidateSearchUsed: number
  teamMemberLimit: number
  cancelAtPeriodEnd?: boolean
  trialEnd?: Date
}

export interface CompanyStats {
  totalJobs: number
  activeJobs: number
  totalApplications: number
  totalHires: number
  averageTimeToHire?: number
  responseRate?: number
  profileViews: number
  followerCount: number
  averageRating?: number
  reviewCount: number
}

export interface CompanyVerification {
  isVerified: boolean
  verifiedAt?: Date
  verifiedBy?: ObjectId
  documents?: FileUpload[]
  verificationNotes?: string
  businessRegistrationNumber?: string
  taxId?: string
}

export interface CompanyTeamMember {
  userId: ObjectId
  role: 'admin' | 'recruiter' | 'hiring_manager'
  permissions: string[]
  addedAt: Date
  addedBy: ObjectId
  isActive: boolean
}

export interface Company extends BaseDocument {
  name: string
  slug: string
  description: string
  logo?: string
  coverImage?: string
  website?: string
  industry: string[]
  size: CompanySize
  founded?: number
  locations: CompanyLocation[]
  culture: CompanyCulture
  socialLinks: SocialLinks
  subscription: CompanySubscription
  admins: ObjectId[]
  recruiters: ObjectId[]
  teamMembers: CompanyTeamMember[]
  stats: CompanyStats
  verification: CompanyVerification
  rating: Rating
  isActive: boolean
  isFeatured: boolean
  tags: string[]
  specialties: string[]
  awards?: string[]
  certifications?: string[]
  funding?: {
    stage: 'pre_seed' | 'seed' | 'series_a' | 'series_b' | 'series_c' | 'ipo' | 'acquired'
    amount?: number
    currency?: string
    date?: Date
  }
}

export interface CreateCompanyRequest {
  name: string
  description: string
  website?: string
  industry: string[]
  size: CompanySize
  founded?: number
  location: Omit<CompanyLocation, 'isHeadquarters'>
  logo?: string
}

export interface UpdateCompanyRequest {
  name?: string
  description?: string
  website?: string
  industry?: string[]
  size?: CompanySize
  founded?: number
  locations?: CompanyLocation[]
  culture?: Partial<CompanyCulture>
  socialLinks?: Partial<SocialLinks>
  logo?: string
  coverImage?: string
  tags?: string[]
  specialties?: string[]
}

export interface CompanySearchQuery {
  q?: string
  industry?: string[]
  size?: CompanySize[]
  location?: string
  isVerified?: boolean
  isActive?: boolean
  isFeatured?: boolean
  founded?: {
    min?: number
    max?: number
  }
  rating?: {
    min?: number
    max?: number
  }
  page?: number
  limit?: number
  sort?: 'name' | 'createdAt' | 'rating' | 'followerCount' | 'totalJobs'
  order?: 'asc' | 'desc'
}

export interface CompanyListResponse {
  companies: Company[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  aggregations?: {
    industries: { name: string; count: number }[]
    sizes: { size: CompanySize; count: number }[]
    locations: { location: string; count: number }[]
  }
}

export interface CompanyReview {
  id: ObjectId
  company: ObjectId
  reviewer: ObjectId
  rating: number
  title: string
  review: string
  pros?: string
  cons?: string
  advice?: string
  position?: string
  employment: 'current' | 'former'
  workLifeBalance: number
  culture: number
  careerOpportunities: number
  compensation: number
  management: number
  isAnonymous: boolean
  isVerified: boolean
  helpful: number
  notHelpful: number
  createdAt: Date
  updatedAt: Date
}

export interface CreateCompanyReviewRequest {
  rating: number
  title: string
  review: string
  pros?: string
  cons?: string
  advice?: string
  position?: string
  employment: 'current' | 'former'
  workLifeBalance: number
  culture: number
  careerOpportunities: number
  compensation: number
  management: number
  isAnonymous: boolean
}

export interface CompanyFollower {
  id: ObjectId
  company: ObjectId
  user: ObjectId
  followedAt: Date
  notifications: boolean
}

export interface CompanyAnalytics {
  period: 'day' | 'week' | 'month' | 'quarter' | 'year'
  startDate: Date
  endDate: Date
  metrics: {
    profileViews: number
    jobViews: number
    applications: number
    hires: number
    followers: number
    reviews: number
    averageRating: number
    topJobTitles: { title: string; count: number }[]
    topSkills: { skill: string; count: number }[]
    applicationSources: { source: string; count: number }[]
    candidateLocations: { location: string; count: number }[]
  }
}

export interface CompanyJobStats {
  totalJobs: number
  activeJobs: number
  draftJobs: number
  expiredJobs: number
  totalApplications: number
  averageApplicationsPerJob: number
  topPerformingJobs: {
    jobId: ObjectId
    title: string
    applications: number
    views: number
  }[]
  applicationsByStatus: {
    status: string
    count: number
  }[]
}
