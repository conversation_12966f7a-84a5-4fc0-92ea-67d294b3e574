import { BaseDocument, FileUpload, ObjectId } from './base.types'

export type ApplicationStatus = 
  | 'submitted' 
  | 'under_review' 
  | 'screening' 
  | 'interview_scheduled' 
  | 'interviewed' 
  | 'reference_check' 
  | 'offer_pending' 
  | 'offer_extended' 
  | 'offer_accepted' 
  | 'offer_declined' 
  | 'rejected' 
  | 'withdrawn'

export type InterviewType = 'phone' | 'video' | 'onsite' | 'technical' | 'behavioral' | 'panel' | 'group'

export type InterviewStatus = 'scheduled' | 'completed' | 'cancelled' | 'rescheduled' | 'no_show'

export interface ApplicationAnswer {
  questionId: string
  question: string
  answer: string | string[] | boolean | FileUpload
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'boolean' | 'file'
}

export interface ApplicationTimeline {
  id: string
  status: ApplicationStatus
  timestamp: Date
  updatedBy: ObjectId
  notes?: string
  automated: boolean
  emailSent?: boolean
}

export interface ApplicationFeedback {
  id: string
  reviewer: ObjectId
  rating: number
  comments: string
  categories: {
    technical: number
    communication: number
    experience: number
    cultural_fit: number
    overall: number
  }
  recommendation: 'strong_yes' | 'yes' | 'maybe' | 'no' | 'strong_no'
  createdAt: Date
}

export interface Application extends BaseDocument {
  job: ObjectId
  candidate: ObjectId
  company: ObjectId
  status: ApplicationStatus
  source: 'direct' | 'linkedin' | 'indeed' | 'glassdoor' | 'referral' | 'company_website' | 'other'
  coverLetter?: string
  resume: FileUpload
  portfolio?: FileUpload[]
  answers?: ApplicationAnswer[]
  timeline: ApplicationTimeline[]
  feedback: ApplicationFeedback[]
  rating?: number
  notes?: string
  tags?: string[]
  salary?: {
    expected: number
    negotiable: boolean
    currency: string
  }
  availability?: {
    startDate: Date
    noticePeriod: string
    flexible: boolean
  }
  referral?: {
    referredBy: ObjectId
    referralCode?: string
    referralBonus?: number
  }
  screening?: {
    passed: boolean
    score?: number
    notes?: string
    completedAt?: Date
  }
  isArchived: boolean
  archivedAt?: Date
  archivedBy?: ObjectId
}

export interface CreateApplicationRequest {
  jobId: ObjectId
  coverLetter?: string
  resume: FileUpload
  portfolio?: FileUpload[]
  answers?: Omit<ApplicationAnswer, 'questionId'>[]
  salary?: {
    expected: number
    negotiable: boolean
    currency: string
  }
  availability?: {
    startDate: Date
    noticePeriod: string
    flexible: boolean
  }
  referralCode?: string
}

export interface UpdateApplicationRequest {
  status?: ApplicationStatus
  notes?: string
  tags?: string[]
  rating?: number
  feedback?: Omit<ApplicationFeedback, 'id' | 'createdAt'>
}

export interface ApplicationSearchQuery {
  jobId?: ObjectId
  candidateId?: ObjectId
  companyId?: ObjectId
  status?: ApplicationStatus[]
  source?: string[]
  dateFrom?: Date
  dateTo?: Date
  rating?: {
    min?: number
    max?: number
  }
  hasReferral?: boolean
  isArchived?: boolean
  page?: number
  limit?: number
  sort?: 'createdAt' | 'updatedAt' | 'rating' | 'status'
  order?: 'asc' | 'desc'
}

export interface ApplicationListResponse {
  applications: Application[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  aggregations?: {
    statuses: { status: ApplicationStatus; count: number }[]
    sources: { source: string; count: number }[]
    ratings: { rating: number; count: number }[]
  }
}

export interface Interview {
  id: ObjectId
  application: ObjectId
  job: ObjectId
  candidate: ObjectId
  company: ObjectId
  interviewer: ObjectId[]
  type: InterviewType
  status: InterviewStatus
  scheduledAt: Date
  duration: number // in minutes
  location?: string
  meetingLink?: string
  notes?: string
  feedback?: {
    interviewer: ObjectId
    rating: number
    comments: string
    recommendation: 'hire' | 'no_hire' | 'maybe'
    strengths: string[]
    weaknesses: string[]
    createdAt: Date
  }[]
  recording?: FileUpload
  transcript?: string
  createdAt: Date
  updatedAt: Date
  createdBy: ObjectId
}

export interface CreateInterviewRequest {
  applicationId: ObjectId
  interviewers: ObjectId[]
  type: InterviewType
  scheduledAt: Date
  duration: number
  location?: string
  meetingLink?: string
  notes?: string
}

export interface UpdateInterviewRequest {
  scheduledAt?: Date
  duration?: number
  location?: string
  meetingLink?: string
  notes?: string
  status?: InterviewStatus
}

export interface InterviewFeedbackRequest {
  rating: number
  comments: string
  recommendation: 'hire' | 'no_hire' | 'maybe'
  strengths: string[]
  weaknesses: string[]
}

export interface ApplicationStats {
  total: number
  byStatus: { status: ApplicationStatus; count: number }[]
  bySource: { source: string; count: number }[]
  byRating: { rating: number; count: number }[]
  averageRating: number
  conversionRate: number
  averageTimeToHire: number
  topSkills: { skill: string; count: number }[]
  topLocations: { location: string; count: number }[]
}

export interface ApplicationAnalytics {
  period: 'day' | 'week' | 'month' | 'quarter' | 'year'
  startDate: Date
  endDate: Date
  metrics: {
    totalApplications: number
    uniqueCandidates: number
    averageApplicationsPerJob: number
    conversionRates: {
      submittedToScreening: number
      screeningToInterview: number
      interviewToOffer: number
      offerToHire: number
    }
    timeToHire: {
      average: number
      median: number
      fastest: number
      slowest: number
    }
    rejectionReasons: { reason: string; count: number }[]
    sourceEffectiveness: { source: string; hireRate: number }[]
  }
}

export interface BulkApplicationAction {
  applicationIds: ObjectId[]
  action: 'update_status' | 'add_tags' | 'archive' | 'delete'
  data: {
    status?: ApplicationStatus
    tags?: string[]
    notes?: string
  }
}

export interface ApplicationExport {
  format: 'csv' | 'excel' | 'pdf'
  filters: ApplicationSearchQuery
  fields: string[]
  includePersonalData: boolean
}

export interface CandidateShortlist {
  id: ObjectId
  job: ObjectId
  name: string
  applications: ObjectId[]
  createdBy: ObjectId
  createdAt: Date
  updatedAt: Date
  shared: boolean
  sharedWith: ObjectId[]
}
