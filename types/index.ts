// Unified Types Export - Job Portal Application
// This file exports all types used throughout the application

// Base Types
export * from './base.types'

// User & Authentication Types
export * from './user.types'

// Company Types
export * from './company.types'

// Job Types
export * from './job.types'

// Application Types
export * from './application.types'

// Search Types
export * from './search.types'

// Payment & Billing Types
export * from './payment.types'

// Admin & Management Types
export * from './admin.types'

// Note: Individual types are available through their respective module imports
// Example: import { User, Company, Job } from '@/types/user.types'
// Or use the wildcard imports above for all types

// Type Guards for Runtime Type Checking
export const isUser = (obj: any): obj is User => {
  return obj && typeof obj === 'object' && 'email' && 'role' in obj
}

export const isCompany = (obj: any): obj is Company => {
  return obj && typeof obj === 'object' && 'name' && 'slug' in obj
}

export const isJob = (obj: any): obj is Job => {
  return obj && typeof obj === 'object' && 'title' && 'company' && 'status' in obj
}

export const isApplication = (obj: any): obj is Application => {
  return obj && typeof obj === 'object' && 'job' && 'candidate' && 'status' in obj
}

// Utility Types
export type EntityType = 'user' | 'company' | 'job' | 'application' | 'interview'

export type SortOrder = 'asc' | 'desc'

export type DateRange = {
  start: Date
  end: Date
}

export type Coordinates = [number, number] // [longitude, latitude]

export type Currency = 'USD' | 'EUR' | 'GBP' | 'CAD' | 'AUD' | 'JPY' | 'INR'

export type Language = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'zh' | 'ja' | 'ko'

export type Timezone = string // IANA timezone identifier

export type FileType = 'pdf' | 'doc' | 'docx' | 'jpg' | 'jpeg' | 'png' | 'gif'

export type NotificationType = 'email' | 'push' | 'sms' | 'in_app'

// API Request/Response Types
export type CreateRequest<T> = Omit<T, '_id' | 'createdAt' | 'updatedAt'>

export type UpdateRequest<T> = Partial<Omit<T, '_id' | 'createdAt' | 'updatedAt'>>

export type ListRequest = {
  page?: number
  limit?: number
  sort?: string
  order?: SortOrder
  search?: string
  filters?: Record<string, any>
}

export type ListResponse<T> = {
  data: T[]
  pagination: PaginationMeta
  aggregations?: Record<string, any>
}

// Error Types
export type ErrorResponse = {
  success: false
  error: {
    code: string
    message: string
    field?: string
    details?: Record<string, any>
  }
  meta: {
    timestamp: string
    requestId: string
  }
}

// Success Response Types
export type SuccessResponse<T = any> = {
  success: true
  data: T
  meta: {
    timestamp: string
    requestId: string
    executionTime?: number
  }
}

// Webhook Types
export type WebhookEventType = 
  | 'user.created'
  | 'user.updated'
  | 'user.deleted'
  | 'company.created'
  | 'company.updated'
  | 'company.verified'
  | 'job.created'
  | 'job.updated'
  | 'job.published'
  | 'job.expired'
  | 'application.created'
  | 'application.updated'
  | 'payment.succeeded'
  | 'payment.failed'
  | 'subscription.created'
  | 'subscription.updated'
  | 'subscription.cancelled'

export type WebhookPayload<T = any> = {
  id: string
  type: WebhookEventType
  data: T
  timestamp: Date
  version: string
}

// Configuration Types
export type DatabaseConfig = {
  uri: string
  options: {
    maxPoolSize: number
    serverSelectionTimeoutMS: number
    socketTimeoutMS: number
  }
}

export type RedisConfig = {
  host: string
  port: number
  password?: string
  db: number
}

export type EmailConfig = {
  provider: 'sendgrid' | 'mailgun' | 'ses'
  apiKey: string
  fromEmail: string
  fromName: string
}

export type StorageConfig = {
  provider: 'local' | 's3' | 'gcs' | 'azure'
  bucket?: string
  region?: string
  accessKey?: string
  secretKey?: string
}

export type AppConfig = {
  env: 'development' | 'staging' | 'production'
  port: number
  database: DatabaseConfig
  redis?: RedisConfig
  email: EmailConfig
  storage: StorageConfig
  jwt: {
    secret: string
    refreshSecret: string
    expiresIn: string
    refreshExpiresIn: string
  }
  stripe: {
    secretKey: string
    webhookSecret: string
  }
  openai?: {
    apiKey: string
  }
}

// Validation Types
export type ValidationRule = {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
}

export type ValidationSchema<T> = {
  [K in keyof T]?: ValidationRule
}

// Permission Types
export type Permission = 
  | 'users.read'
  | 'users.write'
  | 'users.delete'
  | 'companies.read'
  | 'companies.write'
  | 'companies.delete'
  | 'jobs.read'
  | 'jobs.write'
  | 'jobs.delete'
  | 'applications.read'
  | 'applications.write'
  | 'applications.delete'
  | 'admin.read'
  | 'admin.write'
  | 'billing.read'
  | 'billing.write'

export type RolePermissions = {
  [role in UserRole]: Permission[]
}
