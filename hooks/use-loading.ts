'use client'

import { useState, useCallback } from 'react'

export interface LoadingState {
  isLoading: boolean
  error: string | null
  success: boolean
}

export function useLoading(initialState: boolean = false) {
  const [state, setState] = useState<LoadingState>({
    isLoading: initialState,
    error: null,
    success: false
  })

  const startLoading = useCallback(() => {
    setState({
      isLoading: true,
      error: null,
      success: false
    })
  }, [])

  const stopLoading = useCallback((success: boolean = true, error?: string) => {
    setState({
      isLoading: false,
      error: error || null,
      success: success && !error
    })
  }, [])

  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      success: false
    })
  }, [])

  return {
    ...state,
    startLoading,
    stopLoading,
    reset
  }
}

// Async operation wrapper with loading state
export function useAsyncOperation<T extends any[], R>(
  operation: (...args: T) => Promise<R>
) {
  const { isLoading, error, success, startLoading, stopLoading, reset } = useLoading()

  const execute = useCallback(async (...args: T): Promise<R | null> => {
    try {
      startLoading()
      const result = await operation(...args)
      stopLoading(true)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      stopLoading(false, errorMessage)
      return null
    }
  }, [operation, startLoading, stopLoading])

  return {
    execute,
    isLoading,
    error,
    success,
    reset
  }
}
