import mongoose, { Schema, Document } from 'mongoose'

export interface IApplication extends Document {
  client: mongoose.Types.ObjectId
  job: mongoose.Types.ObjectId
  status: 'applied' | 'under_review' | 'interview_scheduled' | 'offer_received' | 'rejected' | 'withdrawn'
  coverLetter?: string
  resumeId?: string
  additionalDocuments: Array<{
    type: 'resume' | 'cover_letter' | 'portfolio' | 'other'
    filename: string
    url: string
    uploadDate: Date
  }>
  customAnswers: Record<string, string>
  timeline: Array<{
    status: string
    date: Date
    notes?: string
    updatedBy?: mongoose.Types.ObjectId
  }>
  interviewDate?: Date
  interviewType?: 'phone' | 'video' | 'in-person'
  interviewLocation?: string
  interviewNotes?: string
  feedback?: string
  nextSteps?: string
  notes?: string
  withdrawalReason?: string
  offerDetails?: {
    salary?: number
    benefits?: string[]
    startDate?: Date
    deadline?: Date
    negotiable?: boolean
  }
  contactPerson?: {
    name: string
    email: string
    role: string
    phone?: string
  }
  source?: string
  referral?: {
    referrerName: string
    referrerEmail: string
    relationship: string
  }
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

const applicationSchema = new Schema<IApplication>({
  client: {
    type: Schema.Types.ObjectId,
    ref: 'Client',
    required: true
  },
  job: {
    type: Schema.Types.ObjectId,
    ref: 'Job',
    required: true
  },
  status: {
    type: String,
    enum: ['applied', 'under_review', 'interview_scheduled', 'offer_received', 'rejected', 'withdrawn'],
    default: 'applied'
  },
  coverLetter: {
    type: String,
    maxlength: 5000
  },
  resumeId: {
    type: String
  },
  additionalDocuments: [{
    type: {
      type: String,
      enum: ['resume', 'cover_letter', 'portfolio', 'other'],
      required: true
    },
    filename: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  customAnswers: {
    type: Map,
    of: String,
    default: new Map()
  },
  timeline: [{
    status: {
      type: String,
      required: true
    },
    date: {
      type: Date,
      required: true,
      default: Date.now
    },
    notes: String,
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  interviewDate: Date,
  interviewType: {
    type: String,
    enum: ['phone', 'video', 'in-person']
  },
  interviewLocation: String,
  interviewNotes: String,
  feedback: String,
  nextSteps: String,
  notes: String,
  withdrawalReason: String,
  offerDetails: {
    salary: Number,
    benefits: [String],
    startDate: Date,
    deadline: Date,
    negotiable: {
      type: Boolean,
      default: false
    }
  },
  contactPerson: {
    name: String,
    email: String,
    role: String,
    phone: String
  },
  source: String,
  referral: {
    referrerName: String,
    referrerEmail: String,
    relationship: String
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for better query performance
applicationSchema.index({ client: 1, status: 1 })
applicationSchema.index({ job: 1, client: 1 }, { unique: true }) // Prevent duplicate applications
applicationSchema.index({ createdAt: -1 })
applicationSchema.index({ updatedAt: -1 })
applicationSchema.index({ interviewDate: 1 })

// Virtual for application age
applicationSchema.virtual('applicationAge').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24))
})

// Virtual for current status duration
applicationSchema.virtual('statusDuration').get(function() {
  const lastStatusChange = this.timeline[this.timeline.length - 1]
  if (!lastStatusChange) return 0
  return Math.floor((Date.now() - lastStatusChange.date.getTime()) / (1000 * 60 * 60 * 24))
})

// Pre-save middleware to update timeline
applicationSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.timeline.push({
      status: this.status,
      date: new Date(),
      notes: `Status changed to ${this.status}`
    })
  }
  next()
})

// Static method to get application stats for a client
applicationSchema.statics.getClientStats = async function(clientId: mongoose.Types.ObjectId) {
  const stats = await this.aggregate([
    { $match: { client: clientId, isActive: true } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ])

  const result = {
    total: 0,
    applied: 0,
    under_review: 0,
    interview_scheduled: 0,
    offer_received: 0,
    rejected: 0,
    withdrawn: 0
  }

  stats.forEach(stat => {
    result[stat._id as keyof typeof result] = stat.count
    result.total += stat.count
  })

  return result
}

// Static method to get recent applications
applicationSchema.statics.getRecentApplications = async function(
  clientId: mongoose.Types.ObjectId,
  limit: number = 5
) {
  return this.find({ client: clientId, isActive: true })
    .populate('job', 'title company location salary')
    .sort({ createdAt: -1 })
    .limit(limit)
}

export const Application = mongoose.models.Application || mongoose.model<IApplication>('Application', applicationSchema)
