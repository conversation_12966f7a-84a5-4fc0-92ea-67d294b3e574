// Frontend Error Service
// Centralized error handling and reporting

export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: Date
  userId?: string
  context?: string
}

export type ErrorLevel = 'low' | 'medium' | 'high' | 'critical'

export class ErrorService {
  private static instance: ErrorService
  private errors: AppError[] = []
  private maxErrors = 100 // Keep last 100 errors

  private constructor() {}

  static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService()
    }
    return ErrorService.instance
  }

  // Log error to service
  logError(error: Error | AppError, context?: string, level: ErrorLevel = 'medium') {
    const appError: AppError = this.normalizeError(error, context)
    
    // Add to local storage
    this.errors.push(appError)
    if (this.errors.length > this.maxErrors) {
      this.errors.shift()
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${level.toUpperCase()}] ${appError.code}:`, appError)
    }

    // Send to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalService(appError, level)
    }

    return appError
  }

  // Normalize different error types
  private normalizeError(error: Error | AppError, context?: string): AppError {
    if (this.isAppError(error)) {
      return {
        ...error,
        context: context || error.context
      }
    }

    // Handle different error types
    if (error instanceof TypeError) {
      return {
        code: 'TYPE_ERROR',
        message: error.message,
        details: { stack: error.stack },
        timestamp: new Date(),
        context
      }
    }

    if (error instanceof ReferenceError) {
      return {
        code: 'REFERENCE_ERROR',
        message: error.message,
        details: { stack: error.stack },
        timestamp: new Date(),
        context
      }
    }

    // Network errors
    if (error.message.includes('fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network request failed',
        details: { originalMessage: error.message },
        timestamp: new Date(),
        context
      }
    }

    // Generic error
    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || 'An unknown error occurred',
      details: { stack: error.stack },
      timestamp: new Date(),
      context
    }
  }

  private isAppError(error: any): error is AppError {
    return error && typeof error.code === 'string' && typeof error.message === 'string'
  }

  // Send error to external logging service
  private async sendToExternalService(error: AppError, level: ErrorLevel) {
    try {
      // This would be your actual error reporting service
      // Examples: Sentry, LogRocket, Bugsnag, etc.
      
      // For now, we'll just store in localStorage as a fallback
      const storedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]')
      storedErrors.push({ ...error, level })
      
      // Keep only last 50 errors in localStorage
      if (storedErrors.length > 50) {
        storedErrors.splice(0, storedErrors.length - 50)
      }
      
      localStorage.setItem('app_errors', JSON.stringify(storedErrors))
    } catch (e) {
      console.error('Failed to send error to external service:', e)
    }
  }

  // Get recent errors
  getRecentErrors(limit = 10): AppError[] {
    return this.errors.slice(-limit)
  }

  // Clear errors
  clearErrors() {
    this.errors = []
    localStorage.removeItem('app_errors')
  }

  // Handle API errors
  handleApiError(response: Response, context?: string): AppError {
    const error: AppError = {
      code: `HTTP_${response.status}`,
      message: this.getHttpErrorMessage(response.status),
      details: {
        status: response.status,
        statusText: response.statusText,
        url: response.url
      },
      timestamp: new Date(),
      context
    }

    return this.logError(error, context, this.getErrorLevel(response.status))
  }

  private getHttpErrorMessage(status: number): string {
    switch (status) {
      case 400:
        return 'Bad request - please check your input'
      case 401:
        return 'Authentication required'
      case 403:
        return 'Access denied'
      case 404:
        return 'Resource not found'
      case 429:
        return 'Too many requests - please try again later'
      case 500:
        return 'Server error - please try again'
      case 502:
        return 'Service temporarily unavailable'
      case 503:
        return 'Service unavailable'
      default:
        return `Request failed with status ${status}`
    }
  }

  private getErrorLevel(status: number): ErrorLevel {
    if (status >= 500) return 'critical'
    if (status >= 400) return 'high'
    return 'medium'
  }

  // Handle form validation errors
  handleValidationErrors(errors: Record<string, string[]>): AppError {
    const errorMessages = Object.entries(errors)
      .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
      .join('; ')

    const error: AppError = {
      code: 'VALIDATION_ERROR',
      message: 'Form validation failed',
      details: { errors, formattedMessage: errorMessages },
      timestamp: new Date(),
      context: 'form_validation'
    }

    return this.logError(error, 'form_validation', 'low')
  }

  // Create user-friendly error messages
  getUserFriendlyMessage(error: AppError): string {
    switch (error.code) {
      case 'NETWORK_ERROR':
        return 'Unable to connect to the server. Please check your internet connection.'
      case 'HTTP_401':
        return 'Please sign in to continue.'
      case 'HTTP_403':
        return 'You do not have permission to perform this action.'
      case 'HTTP_404':
        return 'The requested resource was not found.'
      case 'HTTP_429':
        return 'Too many requests. Please wait a moment and try again.'
      case 'HTTP_500':
      case 'HTTP_502':
      case 'HTTP_503':
        return 'Server is temporarily unavailable. Please try again later.'
      case 'VALIDATION_ERROR':
        return 'Please correct the highlighted fields and try again.'
      default:
        return 'An unexpected error occurred. Please try again.'
    }
  }
}

// Global error handler
export const errorService = ErrorService.getInstance()

// Utility functions
export function handleAsyncError<T>(
  promise: Promise<T>,
  context?: string
): Promise<[T | null, AppError | null]> {
  return promise
    .then<[T, null]>((data: T) => [data, null])
    .catch<[null, AppError]>((error: Error) => [
      null,
      errorService.logError(error, context)
    ])
}

// React error boundary helper
export function logReactError(error: Error, errorInfo: React.ErrorInfo) {
  const appError: AppError = {
    code: 'REACT_ERROR',
    message: error.message,
    details: {
      stack: error.stack,
      componentStack: errorInfo.componentStack
    },
    timestamp: new Date(),
    context: 'react_component'
  }

  errorService.logError(appError, 'react_component', 'high')
}
