import { NextRequest } from 'next/server'
import jwt from 'jsonwebtoken'
import { User, IUser } from '@/lib/models/user.model'
import { errorService } from '@/lib/errors/error-service'
import { AppError, ErrorCode } from '@/lib/errors/error-types'

export interface JWTPayload {
  userId: string
  email: string
  role: string
  iat: number
  exp: number
}

export interface AuthenticatedRequest extends NextRequest {
  user: IUser
}

export async function authenticateRequest(request: NextRequest): Promise<IUser> {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authorization header is required'
    )
  }
  
  const token = authHeader.startsWith('Bearer ') 
    ? authHeader.substring(7) 
    : authHeader
  
  if (!token) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Access token is required'
    )
  }
  
  try {
    // Verify the JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload
    
    // Find the user in the database
    const user = await User.findById(decoded.userId).select('+password')
    
    if (!user) {
      throw errorService.createError(
        ErrorCode.UNAUTHORIZED,
        'User not found'
      )
    }
    
    if (!user.isActive) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'Account is deactivated'
      )
    }
    
    // Remove password from user object before returning
    user.password = undefined
    
    return user
    
  } catch (error: unknown) {
    if (error instanceof Error && error.name === 'TokenExpiredError') {
      throw errorService.createError(
        ErrorCode.TOKEN_EXPIRED,
        'Token has expired'
      )
    }

    if (error instanceof Error && error.name === 'JsonWebTokenError') {
      throw errorService.createError(
        ErrorCode.TOKEN_INVALID,
        'Invalid token'
      )
    }

    // Re-throw AppError instances
    if (error instanceof AppError) {
      throw error
    }

    throw errorService.createError(
      ErrorCode.TOKEN_INVALID,
      'Token validation failed'
    )
  }
}

export function requireRole(...allowedRoles: string[]) {
  return async (request: NextRequest): Promise<IUser> => {
    const user = await authenticateRequest(request)
    
    if (!allowedRoles.includes(user.role)) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        `Access denied. Required roles: ${allowedRoles.join(', ')}`
      )
    }
    
    return user
  }
}

export async function requireCompanyAccess(request: NextRequest, companyId: string): Promise<IUser> {
  const user = await authenticateRequest(request)
  
  // Admin can access any company
  if (user.role === 'admin') {
    return user
  }
  
  // Company admin and recruiters can only access their own company
  if (user.role === 'company_admin' || user.role === 'recruiter') {
    if (!user.companyId || user.companyId.toString() !== companyId) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'Access denied to this company'
      )
    }
  } else {
    throw errorService.createError(
      ErrorCode.FORBIDDEN,
      'Company access not allowed for this role'
    )
  }
  
  return user
}

export async function requireEmailVerification(request: NextRequest): Promise<IUser> {
  const user = await authenticateRequest(request)
  
  if (!user.isEmailVerified) {
    throw errorService.createError(
      ErrorCode.EMAIL_NOT_VERIFIED,
      'Email verification is required'
    )
  }
  
  return user
}

export async function requireSubscription(request: NextRequest, requiredPlan?: string): Promise<IUser> {
  const user = await authenticateRequest(request)
  
  if (!user.subscription || user.subscription.status !== 'active') {
    throw errorService.createError(
      ErrorCode.SUBSCRIPTION_REQUIRED,
      'Active subscription is required'
    )
  }
  
  if (requiredPlan) {
    const planHierarchy = ['free', 'premium', 'enterprise']
    const userPlanIndex = planHierarchy.indexOf(user.subscription.plan)
    const requiredPlanIndex = planHierarchy.indexOf(requiredPlan)
    
    if (userPlanIndex < requiredPlanIndex) {
      throw errorService.createError(
        ErrorCode.SUBSCRIPTION_REQUIRED,
        `${requiredPlan} subscription or higher is required`
      )
    }
  }
  
  return user
}

// Optional authentication - doesn't throw if no token provided
export async function optionalAuth(request: NextRequest): Promise<IUser | null> {
  try {
    return await authenticateRequest(request)
  } catch (error) {
    // Return null if authentication fails, don't throw
    return null
  }
}

// Generate JWT token
export function generateTokens(user: IUser): { accessToken: string; refreshToken: string } {
  const payload = {
    userId: user._id.toString(),
    email: user.email,
    role: user.role
  }

  const accessToken = jwt.sign(
    payload,
    process.env.JWT_SECRET!,
    { expiresIn: process.env.JWT_EXPIRES_IN || '1h' } as jwt.SignOptions
  )

  const refreshToken = jwt.sign(
    payload,
    process.env.JWT_REFRESH_SECRET!,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' } as jwt.SignOptions
  )

  return { accessToken, refreshToken }
}

// Verify refresh token
export async function verifyRefreshToken(token: string): Promise<IUser> {
  try {
    const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET!) as JWTPayload
    
    const user = await User.findById(decoded.userId)
    
    if (!user || !user.isActive) {
      throw errorService.createError(
        ErrorCode.UNAUTHORIZED,
        'Invalid refresh token'
      )
    }
    
    return user
    
  } catch (error: unknown) {
    if (error instanceof Error && error.name === 'TokenExpiredError') {
      throw errorService.createError(
        ErrorCode.TOKEN_EXPIRED,
        'Refresh token has expired'
      )
    }

    if (error instanceof Error && error.name === 'JsonWebTokenError') {
      throw errorService.createError(
        ErrorCode.TOKEN_INVALID,
        'Invalid refresh token'
      )
    }

    throw error
  }
}

// Extract user ID from token without full validation (for logging, etc.)
export function extractUserIdFromToken(request: NextRequest): string | null {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) return null
    
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader
    
    const decoded = jwt.decode(token) as JWTPayload
    return decoded?.userId || null
  } catch {
    return null
  }
}
