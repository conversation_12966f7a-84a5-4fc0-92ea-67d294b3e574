import { NextRequest, NextResponse } from 'next/server'

export function validate<PERSON>eth<PERSON>(request: NextRequest, allowedMethods: string[]) {
  const method = request.method
  if (!allowedMethods.includes(method)) {
    throw new Error(`Method ${method} not allowed`)
  }
}

export async function validateR<PERSON>questBody(request: NextRequest, validator?: (data: any) => boolean) {
  try {
    const body = await request.json()
    if (validator && !validator(body)) {
      throw new Error('Invalid request body')
    }
    return body
  } catch (error) {
    throw new Error('Invalid JSON in request body')
  }
}

export function createSuccessResponse(data: any, message?: string, status: number = 200) {
  return NextResponse.json({
    success: true,
    message: message || 'Success',
    data
  }, { status })
}

export function createErrorResponse(error: string, status: number = 400) {
  return NextResponse.json({
    success: false,
    error
  }, { status })
}

export function createValidationErrorResponse(errors: Record<string, string>) {
  return NextResponse.json({
    success: false,
    error: 'Validation failed',
    errors
  }, { status: 400 })
}
