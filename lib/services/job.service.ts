import BaseService from './base.service'
import { Job } from '@/lib/models/job.model'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { cacheService } from './cache.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export interface JobProfile {
  id: string
  title: string
  description: string
  company: {
    id: string
    name: string
    logo?: string
  }
  location: {
    city?: string
    state?: string
    country?: string
    remote?: boolean
  }
  salary?: {
    min?: number
    max?: number
    currency?: string
    period?: 'hourly' | 'monthly' | 'yearly'
  }
  requirements: string[]
  benefits?: string[]
  type: 'full-time' | 'part-time' | 'contract' | 'internship'
  level: 'entry' | 'mid' | 'senior' | 'executive'
  category: string
  tags: string[]
  isActive: boolean
  isRemote: boolean
  applicationDeadline?: Date
  createdAt: Date
  updatedAt: Date
}

export interface CreateJobRequest {
  title: string
  description: string
  companyId: string
  location: {
    city?: string
    state?: string
    country?: string
    remote?: boolean
  }
  salary?: {
    min?: number
    max?: number
    currency?: string
    period?: 'hourly' | 'monthly' | 'yearly'
  }
  requirements: string[]
  benefits?: string[]
  type: 'full-time' | 'part-time' | 'contract' | 'internship'
  level: 'entry' | 'mid' | 'senior' | 'executive'
  category: string
  tags: string[]
  applicationDeadline?: Date
}

export interface UpdateJobRequest {
  title?: string
  description?: string
  location?: {
    city?: string
    state?: string
    country?: string
    remote?: boolean
  }
  salary?: {
    min?: number
    max?: number
    currency?: string
    period?: 'hourly' | 'monthly' | 'yearly'
  }
  requirements?: string[]
  benefits?: string[]
  type?: 'full-time' | 'part-time' | 'contract' | 'internship'
  level?: 'entry' | 'mid' | 'senior' | 'executive'
  category?: string
  tags?: string[]
  isActive?: boolean
  applicationDeadline?: Date
}

export interface JobSearchFilters {
  location?: string
  remote?: boolean
  type?: string
  level?: string
  category?: string
  salaryMin?: number
  salaryMax?: number
  companyId?: string
  tags?: string[]
}

export class JobService extends BaseService {
  /**
   * Create a new job posting
   */
  async createJob(jobData: CreateJobRequest, userId: string): Promise<JobProfile> {
    try {
      // Validate required fields
      this.validateRequiredFields(jobData, ['title', 'description', 'companyId', 'type', 'level', 'category'])

      // Validate ObjectId format for companyId
      this.validateObjectId(jobData.companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      // Verify company exists and user has permission
      const company = await Company.findById(jobData.companyId)
      if (!company) {
        this.createNotFoundError('Company', jobData.companyId)
      }

      // Verify user is company admin for this company
      const user = await User.findById(userId)
      if (!user || user.companyId?.toString() !== jobData.companyId || user.role !== 'company_admin') {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'You do not have permission to create jobs for this company',
          'permission'
        )
      }

      // Create job
      const job = new Job({
        ...jobData,
        createdBy: userId,
        isActive: true,
        isRemote: jobData.location.remote || false
      })

      await job.save()

      // Populate company data
      await job.populate('companyId', 'name logo')

      // Clear related caches
      await this.clearJobCaches(jobData.companyId)

      const result = this.formatJobProfile(job)
      this.logOperation('createJob', { jobId: result.id, title: result.title })

      return result

    } catch (error) {
      this.handleDatabaseError(error, 'createJob')
    }
  }

  /**
   * Get job by ID
   */
  async getJobById(jobId: string, incrementViews: boolean = false): Promise<JobProfile> {
    this.validateObjectId(jobId, 'jobId')

    // Try to get from cache first
    const cacheKey = cacheService.keys.job(jobId)
    let job = await cacheService.get<JobProfile>(cacheKey)

    if (!job) {
      // Get from database
      const jobDoc = await Job.findById(jobId).populate('companyId', 'name logo')

      if (!jobDoc) {
        this.createNotFoundError('Job', jobId)
      }

      job = this.formatJobProfile(jobDoc)

      // Cache for 15 minutes
      await cacheService.set(cacheKey, job, 15 * 60)
    }

    // Increment views if requested (don't wait for it)
    if (incrementViews) {
      Job.findByIdAndUpdate(jobId, { $inc: { viewsCount: 1 } }).exec().catch(console.error)
    }

    return job
  }

  /**
   * Update job posting
   */
  async updateJob(jobId: string, updateData: UpdateJobRequest, userId: string): Promise<JobProfile> {
    this.validateObjectId(jobId, 'jobId')
    this.validateObjectId(userId, 'userId')

    // Get existing job
    const job = await Job.findById(jobId).populate('companyId', 'name logo')
    if (!job) {
      this.createNotFoundError('Job', jobId)
    }

    // Verify user has permission to update this job
    const user = await User.findById(userId)
    if (!user || (user.companyId?.toString() !== job.companyId._id.toString() && !['admin', 'super_admin'].includes(user.role))) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to update this job',
        'permission'
      )
    }

    // Update fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof UpdateJobRequest] !== undefined) {
        if (key === 'location' && updateData.location) {
          job.location = { ...job.location, ...updateData.location }
        } else if (key === 'salary' && updateData.salary) {
          job.salary = { ...job.salary, ...updateData.salary }
        } else {
          (job as any)[key] = updateData[key as keyof UpdateJobRequest]
        }
      }
    })

    // Update isRemote based on location
    if (updateData.location?.remote !== undefined) {
      job.isRemote = updateData.location.remote
    }

    await job.save()

    // Clear caches
    await this.clearJobCaches(job.companyId._id.toString(), jobId)

    const result = this.formatJobProfile(job)
    this.logOperation('updateJob', { jobId, userId })

    return result
  }

  /**
   * Search jobs with filters and pagination
   */
  async searchJobs(
    searchTerm?: string,
    filters?: JobSearchFilters,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    jobs: JobProfile[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
      hasNext: boolean
      hasPrev: boolean
    }
  }> {
    const { page: validPage, limit: validLimit } = this.validatePaginationParams(page, limit)

    // Try cache first
    const filtersString = JSON.stringify(filters || {})
    const cacheKey = cacheService.keys.searchJobs(searchTerm || '', filtersString, validPage, validLimit)
    const cached = await cacheService.get(cacheKey)

    if (cached) {
      return cached
    }

    // Build query
    const query: any = { isActive: true }

    // Add expiration filter
    query.$or = [
      { applicationDeadline: { $gte: new Date() } },
      { applicationDeadline: null }
    ]

    // Apply filters
    if (filters) {
      if (filters.location) {
        query.$and = query.$and || []
        query.$and.push({
          $or: [
            { 'location.city': { $regex: filters.location, $options: 'i' } },
            { 'location.state': { $regex: filters.location, $options: 'i' } },
            { 'location.country': { $regex: filters.location, $options: 'i' } }
          ]
        })
      }

      if (filters.remote !== undefined) query.isRemote = filters.remote
      if (filters.type) query.type = filters.type
      if (filters.level) query.level = filters.level
      if (filters.category) query.category = { $regex: filters.category, $options: 'i' }
      if (filters.companyId) query.companyId = filters.companyId
      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags }
      }

      // Salary range filter
      if (filters.salaryMin || filters.salaryMax) {
        query['salary.min'] = query['salary.min'] || {}
        if (filters.salaryMin) query['salary.min'].$gte = filters.salaryMin
        if (filters.salaryMax) query['salary.max'] = { $lte: filters.salaryMax }
      }
    }

    const skip = (validPage - 1) * validLimit

    let jobsQuery = Job.find(query).populate('companyId', 'name logo')

    // Add text search if provided
    if (searchTerm) {
      jobsQuery = Job.find({
        ...query,
        $text: { $search: searchTerm }
      }).populate('companyId', 'name logo').sort({ score: { $meta: 'textScore' } })
    } else {
      jobsQuery = jobsQuery.sort({ createdAt: -1 })
    }

    const [jobs, total] = await Promise.all([
      jobsQuery.skip(skip).limit(validLimit),
      Job.countDocuments(query)
    ])

    const result = {
      jobs: jobs.map(job => this.formatJobProfile(job)),
      pagination: this.createPaginationMeta(validPage, validLimit, total)
    }

    // Cache for 5 minutes
    await cacheService.set(cacheKey, result, 5 * 60)

    return result
  }

  /**
   * Get jobs by company
   */
  async getJobsByCompany(companyId: string, page: number = 1, limit: number = 10): Promise<{
    jobs: JobProfile[]
    pagination: any
  }> {
    this.validateObjectId(companyId, 'companyId')
    const { page: validPage, limit: validLimit } = this.validatePaginationParams(page, limit)

    // Try cache first
    const cacheKey = cacheService.keys.jobsByCompany(companyId, validPage, validLimit)
    const cached = await cacheService.get(cacheKey)

    if (cached) {
      return cached
    }

    // Verify company exists
    const company = await Company.findById(companyId)
    if (!company) {
      this.createNotFoundError('Company', companyId)
    }

    const skip = (validPage - 1) * validLimit

    const [jobs, total] = await Promise.all([
      Job.find({ companyId, isActive: true })
        .populate('companyId', 'name logo')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(validLimit),
      Job.countDocuments({ companyId, isActive: true })
    ])

    const result = {
      jobs: jobs.map(job => this.formatJobProfile(job)),
      pagination: this.createPaginationMeta(validPage, validLimit, total)
    }

    // Cache for 10 minutes
    await cacheService.set(cacheKey, result, 10 * 60)

    return result
  }

  /**
   * Deactivate job posting
   */
  async deactivateJob(jobId: string, userId: string): Promise<void> {
    this.validateObjectId(jobId, 'jobId')
    this.validateObjectId(userId, 'userId')

    // Get job and verify permission
    const job = await Job.findById(jobId)
    if (!job) {
      this.createNotFoundError('Job', jobId)
    }

    // Verify user has permission
    const user = await User.findById(userId)
    if (!user || (user.companyId?.toString() !== job.companyId.toString() && !['admin', 'super_admin'].includes(user.role))) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to deactivate this job',
        'permission'
      )
    }

    // Deactivate job
    job.isActive = false
    await job.save()

    // Clear caches
    await this.clearJobCaches(job.companyId.toString(), jobId)

    this.logOperation('deactivateJob', { jobId, userId })
  }

  /**
   * Get job statistics
   */
  async getJobStats(companyId?: string): Promise<{
    totalJobs: number
    activeJobs: number
    totalApplications: number
    totalViews: number
  }> {
    const query: any = companyId ? { companyId } : {}

    const [totalJobs, activeJobs, applicationStats, viewStats] = await Promise.all([
      Job.countDocuments(query),
      Job.countDocuments({ ...query, isActive: true }),
      Job.aggregate([
        { $match: query },
        { $group: { _id: null, total: { $sum: '$applicationsCount' } } }
      ]),
      Job.aggregate([
        { $match: query },
        { $group: { _id: null, total: { $sum: '$viewsCount' } } }
      ])
    ])

    return {
      totalJobs,
      activeJobs,
      totalApplications: applicationStats[0]?.total || 0,
      totalViews: viewStats[0]?.total || 0
    }
  }

  /**
   * Clear job-related caches
   */
  private async clearJobCaches(companyId: string, jobId?: string): Promise<void> {
    const patterns = [
      `search:jobs:*`,
      `jobs:company:${companyId}:*`
    ]

    if (jobId) {
      patterns.push(cacheService.keys.job(jobId))
    }

    for (const pattern of patterns) {
      const keys = await cacheService.keys(pattern)
      for (const key of keys) {
        await cacheService.del(key)
      }
    }
  }

  /**
   * Format job data for API response (private helper)
   */
  private formatJobProfile(job: any): JobProfile {
    return {
      id: job._id.toString(),
      title: job.title,
      description: job.description,
      company: {
        id: job.companyId._id.toString(),
        name: job.companyId.name,
        logo: job.companyId.logo
      },
      location: job.location,
      salary: job.salary,
      requirements: job.requirements,
      benefits: job.benefits,
      type: job.type,
      level: job.level,
      category: job.category,
      tags: job.tags,
      isActive: job.isActive,
      isRemote: job.isRemote,
      applicationDeadline: job.applicationDeadline,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt
    }
  }
}

export const jobService = new JobService()
