import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { LoginRequest, RegisterRequest } from './auth.service'
import { normalizeWebsiteUrl, validateWebsiteUrl } from '@/lib/utils'

export class ValidationService {
  /**
   * Validate login request data
   */
  validateLoginRequest(data: any): LoginRequest {
    const errors: string[] = []
    
    if (!data.email) errors.push('Email is required')
    if (!data.password) errors.push('Password is required')
    
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Invalid email format')
    }
    
    if (errors.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Validation failed: ${errors.join(', ')}`,
        undefined,
        { validationErrors: errors }
      )
    }
    
    return {
      email: data.email.toLowerCase().trim(),
      password: data.password,
      rememberMe: <PERSON><PERSON>an(data.rememberMe)
    }
  }

  /**
   * Validate registration request data
   */
  validateRegisterRequest(data: any): RegisterRequest {
    const errors: string[] = []
    
    // Required fields validation
    if (!data.email) errors.push('Email is required')
    if (!data.password) errors.push('Password is required')
    if (!data.firstName) errors.push('First name is required')
    if (!data.lastName) errors.push('Last name is required')
    
    // Email format validation
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Invalid email format')
    }
    
    // Password strength validation
    if (data.password && data.password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    
    // Role validation
    if (data.role && !['job_seeker', 'company_admin'].includes(data.role)) {
      errors.push('Invalid role. Must be job_seeker or company_admin')
    }
    
    // Phone validation (if provided)
    if (data.phone && !/^\+?[\d\s\-\(\)]+$/.test(data.phone)) {
      errors.push('Invalid phone number format')
    }
    
    if (errors.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Validation failed: ${errors.join(', ')}`,
        undefined,
        { validationErrors: errors }
      )
    }
    
    return {
      email: data.email.toLowerCase().trim(),
      password: data.password,
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      role: data.role || 'job_seeker',
      phone: data.phone?.trim(),
      location: data.location
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validate phone number format
   */
  validatePhone(phone: string): boolean {
    return /^\+?[\d\s\-\(\)]+$/.test(phone)
  }

  /**
   * Sanitize string input
   */
  sanitizeString(input: string): string {
    return input.trim().replace(/[<>]/g, '')
  }

  /**
   * Validate required fields
   */
  validateRequiredFields(data: Record<string, any>, requiredFields: string[]): string[] {
    const errors: string[] = []
    
    requiredFields.forEach(field => {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        errors.push(`${field} is required`)
      }
    })
    
    return errors
  }
}

export const validationService = new ValidationService()
