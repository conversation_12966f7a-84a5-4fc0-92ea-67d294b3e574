/**
 * In-Memory Cache Service
 * Provides Redis-like functionality using in-memory storage
 * Note: This is for development/testing. Use actual Redis in production.
 */

interface CacheItem {
  value: any
  expiresAt?: number
  createdAt: number
}

export class CacheService {
  private cache = new Map<string, CacheItem>()
  private cleanupInterval: NodeJS.Timeout

  constructor() {
    // Clean up expired items every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000)
  }

  /**
   * Set a value in cache with optional TTL (time to live) in seconds
   */
  async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    const now = Date.now()
    const item: CacheItem = {
      value: JSON.parse(JSON.stringify(value)), // Deep clone to prevent mutations
      createdAt: now,
      expiresAt: ttlSeconds ? now + (ttlSeconds * 1000) : undefined
    }
    
    this.cache.set(key, item)
  }

  /**
   * Get a value from cache
   */
  async get<T = any>(key: string): Promise<T | null> {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }
    
    // Check if expired
    if (item.expiresAt && Date.now() > item.expiresAt) {
      this.cache.delete(key)
      return null
    }
    
    return JSON.parse(JSON.stringify(item.value)) // Deep clone to prevent mutations
  }

  /**
   * Delete a key from cache
   */
  async del(key: string): Promise<boolean> {
    return this.cache.delete(key)
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    const item = this.cache.get(key)
    
    if (!item) {
      return false
    }
    
    // Check if expired
    if (item.expiresAt && Date.now() > item.expiresAt) {
      this.cache.delete(key)
      return false
    }
    
    return true
  }

  /**
   * Set expiration time for a key
   */
  async expire(key: string, ttlSeconds: number): Promise<boolean> {
    const item = this.cache.get(key)
    
    if (!item) {
      return false
    }
    
    item.expiresAt = Date.now() + (ttlSeconds * 1000)
    return true
  }

  /**
   * Get time to live for a key
   */
  async ttl(key: string): Promise<number> {
    const item = this.cache.get(key)
    
    if (!item || !item.expiresAt) {
      return -1 // No expiration
    }
    
    const remaining = item.expiresAt - Date.now()
    return remaining > 0 ? Math.ceil(remaining / 1000) : -2 // -2 means expired
  }

  /**
   * Increment a numeric value
   */
  async incr(key: string): Promise<number> {
    const current = await this.get<number>(key) || 0
    const newValue = current + 1
    await this.set(key, newValue)
    return newValue
  }

  /**
   * Increment by a specific amount
   */
  async incrBy(key: string, amount: number): Promise<number> {
    const current = await this.get<number>(key) || 0
    const newValue = current + amount
    await this.set(key, newValue)
    return newValue
  }

  /**
   * Get multiple keys at once
   */
  async mget(keys: string[]): Promise<(any | null)[]> {
    return Promise.all(keys.map(key => this.get(key)))
  }

  /**
   * Set multiple key-value pairs
   */
  async mset(keyValuePairs: Record<string, any>): Promise<void> {
    const promises = Object.entries(keyValuePairs).map(([key, value]) => 
      this.set(key, value)
    )
    await Promise.all(promises)
  }

  /**
   * Get all keys matching a pattern (simple wildcard support)
   */
  async keys(pattern: string): Promise<string[]> {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    return Array.from(this.cache.keys()).filter(key => regex.test(key))
  }

  /**
   * Clear all cache
   */
  async flushAll(): Promise<void> {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  async info(): Promise<{
    totalKeys: number
    expiredKeys: number
    memoryUsage: string
  }> {
    let expiredCount = 0
    const now = Date.now()
    
    for (const [key, item] of this.cache.entries()) {
      if (item.expiresAt && now > item.expiresAt) {
        expiredCount++
      }
    }
    
    return {
      totalKeys: this.cache.size,
      expiredKeys: expiredCount,
      memoryUsage: `${Math.round(JSON.stringify(Array.from(this.cache.entries())).length / 1024)}KB`
    }
  }

  /**
   * Clean up expired items
   */
  private cleanup(): void {
    const now = Date.now()
    let cleanedCount = 0
    
    for (const [key, item] of this.cache.entries()) {
      if (item.expiresAt && now > item.expiresAt) {
        this.cache.delete(key)
        cleanedCount++
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`[CacheService] Cleaned up ${cleanedCount} expired cache items`)
    }
  }

  /**
   * Destroy the cache service and cleanup intervals
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.cache.clear()
  }

  /**
   * Cache key generators for common patterns
   */
  static keys = {
    user: (id: string) => `user:${id}`,
    userProfile: (id: string) => `user:profile:${id}`,
    company: (id: string) => `company:${id}`,
    job: (id: string) => `job:${id}`,
    jobsByCompany: (companyId: string, page: number, limit: number) => 
      `jobs:company:${companyId}:${page}:${limit}`,
    searchJobs: (searchTerm: string, filters: string, page: number, limit: number) => 
      `search:jobs:${Buffer.from(searchTerm + filters).toString('base64')}:${page}:${limit}`,
    userApplications: (userId: string, page: number, limit: number) => 
      `applications:user:${userId}:${page}:${limit}`,
    jobApplications: (jobId: string, page: number, limit: number) => 
      `applications:job:${jobId}:${page}:${limit}`,
    notifications: (userId: string) => `notifications:${userId}`,
    rateLimitLogin: (ip: string) => `ratelimit:login:${ip}`,
    rateLimitRegister: (ip: string) => `ratelimit:register:${ip}`
  }
}

// Singleton instance
export const cacheService = new CacheService()

// Graceful shutdown
process.on('SIGTERM', () => {
  cacheService.destroy()
})

process.on('SIGINT', () => {
  cacheService.destroy()
})
