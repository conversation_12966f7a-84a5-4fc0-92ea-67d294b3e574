# Full-Scale Job Portal Backend Implementation & Frontend Integration Plan

## Executive Summary

Transform the existing frontend-only job portal into a **enterprise-grade, full-stack application** using Next.js 15 API routes, comprehensive error handling, custom search utilities, and advanced features that companies will pay premium prices for.

## Architecture Overview

### Technology Stack
- **Backend**: Next.js 15 API Route Handlers + TypeScript
- **Database**: MongoDB with Mongoose ODM + GridFS for file storage
- **State Management**: Zustand stores with error handling
- **Authentication**: JWT + Refresh Tokens + OAuth
- **Real-time**: Server-Sent Events (SSE) for live updates
- **File Storage**: MongoDB GridFS for resumes/documents
- **Email**: SendGrid / AWS SES
- **Payment**: Stripe for subscriptions
- **Search**: Custom MongoDB-based advanced search service
- **AI/ML**: OpenAI API for matching & recommendations
- **Error Handling**: Comprehensive error services for backend and frontend

### Application Architecture Pattern

```
Frontend Components → Frontend Services → Zustand Stores → Next.js API Routes → Backend Services → MongoDB
                  ↓
            Error Components ← Error Services ← Error Handlers ← API Error Responses
```

## Core User Roles & Permissions

### 1. Super Admin
- **Platform Management**: Full system control
- **Analytics Dashboard**: Revenue, user metrics, job statistics
- **Content Moderation**: Approve/reject jobs, manage disputes
- **Subscription Management**: Pricing plans, billing oversight

### 2. Company Admin
- **Company Profile**: Branding, description, culture
- **Team Management**: Add/remove recruiters, set permissions
- **Job Management**: Post, edit, archive jobs
- **Candidate Pipeline**: Review applications, schedule interviews
- **Analytics**: Job performance, candidate insights
- **Billing**: Subscription management, usage tracking

### 3. Recruiter (Company Employee)
- **Job Posting**: Create and manage job listings
- **Candidate Review**: Screen applications, shortlist candidates
- **Communication**: Message candidates, schedule interviews
- **Pipeline Management**: Track hiring progress

### 4. Job Seeker
- **Profile Management**: Resume, skills, experience, portfolio
- **Job Search**: Advanced filtering, saved searches, alerts
- **Applications**: Apply to jobs, track status
- **AI Matching**: Receive personalized recommendations
- **Career Tools**: Salary insights, skill assessments
- **Networking**: Connect with recruiters, company follows

## Database Schema Design

### User Management
```typescript
// users collection
interface User {
  _id: ObjectId
  email: string
  password: string (hashed)
  role: 'admin' | 'company_admin' | 'recruiter' | 'job_seeker'
  profile: {
    firstName: string
    lastName: string
    avatar?: string
    phone?: string
    location: Location
  }
  preferences: UserPreferences
  subscription?: SubscriptionDetails
  createdAt: Date
  updatedAt: Date
  lastLogin: Date
  isActive: boolean
  emailVerified: boolean
}

// companies collection
interface Company {
  _id: ObjectId
  name: string
  slug: string
  description: string
  logo: string
  website: string
  industry: string[]
  size: CompanySize
  location: Location[]
  culture: CompanyCulture
  benefits: string[]
  socialLinks: SocialLinks
  subscription: CompanySubscription
  admins: ObjectId[] // User IDs
  recruiters: ObjectId[] // User IDs
  stats: CompanyStats
  createdAt: Date
  updatedAt: Date
  isVerified: boolean
  isActive: boolean
}

// jobs collection
interface Job {
  _id: ObjectId
  title: string
  slug: string
  company: ObjectId
  postedBy: ObjectId // User ID
  description: string
  requirements: string[]
  responsibilities: string[]
  skills: Skill[]
  experience: ExperienceLevel
  employment: EmploymentType
  location: JobLocation
  salary: SalaryRange
  benefits: string[]
  applicationDeadline?: Date
  status: JobStatus
  featured: boolean
  urgent: boolean
  views: number
  applications: number
  tags: string[]
  aiScore?: number
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}

// applications collection
interface Application {
  _id: ObjectId
  job: ObjectId
  candidate: ObjectId
  company: ObjectId
  status: ApplicationStatus
  coverLetter?: string
  resume: string // S3 URL
  portfolio?: string[]
  answers?: ApplicationAnswer[]
  timeline: ApplicationTimeline[]
  feedback?: string
  rating?: number
  createdAt: Date
  updatedAt: Date
}

// candidate_profiles collection
interface CandidateProfile {
  _id: ObjectId
  user: ObjectId
  headline: string
  summary: string
  experience: WorkExperience[]
  education: Education[]
  skills: Skill[]
  certifications: Certification[]
  portfolio: PortfolioItem[]
  resume: ResumeFile[]
  preferences: JobPreferences
  availability: AvailabilityStatus
  salaryExpectation: SalaryRange
  aiProfile: AIProfileData
  stats: CandidateStats
  createdAt: Date
  updatedAt: Date
  isPublic: boolean
}
```

## Backend Error Handling System

### Comprehensive Error Service

```typescript
// lib/errors/error-types.ts
export enum ErrorCode {
  // Authentication Errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',

  // Validation Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  REQUIRED_FIELD_MISSING = 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT = 'INVALID_FORMAT',

  // Database Errors
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',

  // Business Logic Errors
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  SUBSCRIPTION_REQUIRED = 'SUBSCRIPTION_REQUIRED',

  // File Upload Errors
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  UPLOAD_FAILED = 'UPLOAD_FAILED',

  // External Service Errors
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  EMAIL_SEND_FAILED = 'EMAIL_SEND_FAILED',
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',

  // Generic Errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}

export interface ErrorDetails {
  code: ErrorCode
  message: string
  statusCode: number
  field?: string
  details?: Record<string, any>
  timestamp: Date
  requestId?: string
}

export class AppError extends Error {
  public readonly code: ErrorCode
  public readonly statusCode: number
  public readonly field?: string
  public readonly details?: Record<string, any>
  public readonly timestamp: Date
  public readonly requestId?: string

  constructor(errorDetails: Omit<ErrorDetails, 'timestamp'>) {
    super(errorDetails.message)
    this.code = errorDetails.code
    this.statusCode = errorDetails.statusCode
    this.field = errorDetails.field
    this.details = errorDetails.details
    this.timestamp = new Date()
    this.requestId = errorDetails.requestId

    Error.captureStackTrace(this, this.constructor)
  }
}
```

### Backend Error Service

```typescript
// lib/errors/error-service.ts
import { AppError, ErrorCode, ErrorDetails } from './error-types'
import { NextRequest } from 'next/server'

class BackendErrorService {
  private static instance: BackendErrorService

  static getInstance(): BackendErrorService {
    if (!BackendErrorService.instance) {
      BackendErrorService.instance = new BackendErrorService()
    }
    return BackendErrorService.instance
  }

  createError(code: ErrorCode, message?: string, field?: string, details?: Record<string, any>): AppError {
    const errorConfig = this.getErrorConfig(code)

    return new AppError({
      code,
      message: message || errorConfig.defaultMessage,
      statusCode: errorConfig.statusCode,
      field,
      details
    })
  }

  handleDatabaseError(error: any): AppError {
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0]
      return this.createError(
        ErrorCode.DUPLICATE_ENTRY,
        `${field} already exists`,
        field
      )
    }

    if (error.name === 'ValidationError') {
      const field = Object.keys(error.errors)[0]
      return this.createError(
        ErrorCode.VALIDATION_ERROR,
        error.errors[field].message,
        field
      )
    }

    if (error.name === 'CastError') {
      return this.createError(
        ErrorCode.INVALID_FORMAT,
        'Invalid ID format',
        error.path
      )
    }

    return this.createError(ErrorCode.DATABASE_CONNECTION_ERROR)
  }

  handleFileUploadError(error: any): AppError {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return this.createError(ErrorCode.FILE_TOO_LARGE)
    }

    if (error.code === 'INVALID_FILE_TYPE') {
      return this.createError(ErrorCode.INVALID_FILE_TYPE)
    }

    return this.createError(ErrorCode.UPLOAD_FAILED)
  }

  logError(error: AppError, request?: NextRequest): void {
    const logData = {
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      field: error.field,
      details: error.details,
      timestamp: error.timestamp,
      requestId: error.requestId,
      url: request?.url,
      method: request?.method,
      userAgent: request?.headers.get('user-agent'),
      stack: error.stack
    }

    // Log to console in development, external service in production
    if (process.env.NODE_ENV === 'development') {
      console.error('API Error:', logData)
    } else {
      // Send to logging service (e.g., Sentry, LogRocket, etc.)
      this.sendToLoggingService(logData)
    }
  }

  private sendToLoggingService(logData: any): void {
    // Implementation for external logging service
    // e.g., Sentry.captureException(logData)
  }

  private getErrorConfig(code: ErrorCode): { statusCode: number; defaultMessage: string } {
    const configs = {
      [ErrorCode.INVALID_CREDENTIALS]: { statusCode: 401, defaultMessage: 'Invalid email or password' },
      [ErrorCode.TOKEN_EXPIRED]: { statusCode: 401, defaultMessage: 'Token has expired' },
      [ErrorCode.UNAUTHORIZED]: { statusCode: 401, defaultMessage: 'Authentication required' },
      [ErrorCode.FORBIDDEN]: { statusCode: 403, defaultMessage: 'Access denied' },
      [ErrorCode.VALIDATION_ERROR]: { statusCode: 400, defaultMessage: 'Validation failed' },
      [ErrorCode.REQUIRED_FIELD_MISSING]: { statusCode: 400, defaultMessage: 'Required field is missing' },
      [ErrorCode.INVALID_FORMAT]: { statusCode: 400, defaultMessage: 'Invalid format' },
      [ErrorCode.DUPLICATE_ENTRY]: { statusCode: 409, defaultMessage: 'Resource already exists' },
      [ErrorCode.RESOURCE_NOT_FOUND]: { statusCode: 404, defaultMessage: 'Resource not found' },
      [ErrorCode.DATABASE_CONNECTION_ERROR]: { statusCode: 500, defaultMessage: 'Database connection failed' },
      [ErrorCode.INSUFFICIENT_PERMISSIONS]: { statusCode: 403, defaultMessage: 'Insufficient permissions' },
      [ErrorCode.QUOTA_EXCEEDED]: { statusCode: 429, defaultMessage: 'Quota exceeded' },
      [ErrorCode.SUBSCRIPTION_REQUIRED]: { statusCode: 402, defaultMessage: 'Subscription required' },
      [ErrorCode.FILE_TOO_LARGE]: { statusCode: 413, defaultMessage: 'File size too large' },
      [ErrorCode.INVALID_FILE_TYPE]: { statusCode: 400, defaultMessage: 'Invalid file type' },
      [ErrorCode.UPLOAD_FAILED]: { statusCode: 500, defaultMessage: 'File upload failed' },
      [ErrorCode.PAYMENT_FAILED]: { statusCode: 402, defaultMessage: 'Payment processing failed' },
      [ErrorCode.EMAIL_SEND_FAILED]: { statusCode: 500, defaultMessage: 'Email sending failed' },
      [ErrorCode.AI_SERVICE_ERROR]: { statusCode: 503, defaultMessage: 'AI service unavailable' },
      [ErrorCode.INTERNAL_SERVER_ERROR]: { statusCode: 500, defaultMessage: 'Internal server error' },
      [ErrorCode.RATE_LIMIT_EXCEEDED]: { statusCode: 429, defaultMessage: 'Rate limit exceeded' },
      [ErrorCode.SERVICE_UNAVAILABLE]: { statusCode: 503, defaultMessage: 'Service temporarily unavailable' }
    }

    return configs[code] || { statusCode: 500, defaultMessage: 'Unknown error' }
  }
}

export const errorService = BackendErrorService.getInstance()
```

## Next.js 15 API Routes Structure

### API Route Organization

```typescript
// app/api structure
app/api/
├── auth/
│   ├── register/route.ts
│   ├── login/route.ts
│   ├── refresh/route.ts
│   ├── logout/route.ts
│   ├── forgot-password/route.ts
│   ├── reset-password/route.ts
│   ├── verify-email/route.ts
│   └── social/[provider]/route.ts
├── users/
│   ├── route.ts (GET, POST)
│   ├── [id]/route.ts (GET, PUT, DELETE)
│   ├── [id]/profile/route.ts
│   ├── [id]/preferences/route.ts
│   └── [id]/stats/route.ts
├── companies/
│   ├── route.ts (GET, POST)
│   ├── [id]/route.ts (GET, PUT, DELETE)
│   ├── [id]/jobs/route.ts
│   ├── [id]/team/route.ts
│   └── [id]/stats/route.ts
├── jobs/
│   ├── route.ts (GET, POST)
│   ├── [id]/route.ts (GET, PUT, DELETE)
│   ├── [id]/apply/route.ts
│   ├── [id]/save/route.ts
│   ├── search/route.ts
│   └── recommendations/route.ts
├── applications/
│   ├── route.ts (GET)
│   ├── [id]/route.ts (GET, PUT, DELETE)
│   ├── [id]/status/route.ts
│   └── [id]/feedback/route.ts
├── files/
│   ├── upload/route.ts
│   ├── [id]/route.ts (GET, DELETE)
│   └── [id]/download/route.ts
├── search/
│   ├── jobs/route.ts
│   ├── companies/route.ts
│   └── candidates/route.ts
└── admin/
    ├── dashboard/route.ts
    ├── users/route.ts
    ├── companies/route.ts
    └── analytics/route.ts
```

### API Route Error Handler Wrapper

```typescript
// lib/api/route-handler.ts
import { NextRequest, NextResponse } from 'next/server'
import { errorService } from '@/lib/errors/error-service'
import { AppError } from '@/lib/errors/error-types'

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    field?: string
    details?: Record<string, any>
  }
  meta?: {
    pagination?: PaginationMeta
    timestamp: string
    requestId: string
  }
}

export function withErrorHandler<T = any>(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse<ApiResponse<T>>>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse<ApiResponse<T>>> => {
    const requestId = crypto.randomUUID()

    try {
      // Add request ID to headers for tracking
      request.headers.set('x-request-id', requestId)

      const response = await handler(request, context)

      // Add meta information to successful responses
      const responseData = await response.json()
      if (responseData.success && !responseData.meta) {
        responseData.meta = {
          timestamp: new Date().toISOString(),
          requestId
        }
      }

      return NextResponse.json(responseData, { status: response.status })

    } catch (error) {
      let appError: AppError

      if (error instanceof AppError) {
        appError = error
      } else if (error.name === 'ValidationError' || error.code === 11000) {
        appError = errorService.handleDatabaseError(error)
      } else {
        appError = errorService.createError(
          'INTERNAL_SERVER_ERROR' as any,
          error.message || 'An unexpected error occurred'
        )
      }

      // Log the error
      errorService.logError(appError, request)

      const errorResponse: ApiResponse = {
        success: false,
        error: {
          code: appError.code,
          message: appError.message,
          field: appError.field,
          details: appError.details
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId
        }
      }

      return NextResponse.json(errorResponse, { status: appError.statusCode })
    }
  }
}

// Success response helper
export function createSuccessResponse<T>(
  data: T,
  meta?: Partial<ApiResponse['meta']>
): ApiResponse<T> {
  return {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: crypto.randomUUID(),
      ...meta
    }
  }
}
```

### Example API Route Implementation

```typescript
// app/api/jobs/route.ts
import { NextRequest } from 'next/server'
import { withErrorHandler, createSuccessResponse } from '@/lib/api/route-handler'
import { jobService } from '@/lib/services/job.service'
import { searchService } from '@/lib/services/search.service'
import { authenticateRequest } from '@/lib/auth/middleware'

export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const query = {
    q: searchParams.get('q') || '',
    location: searchParams.get('location') || '',
    skills: searchParams.getAll('skills'),
    salaryMin: searchParams.get('salaryMin') ? parseInt(searchParams.get('salaryMin')!) : undefined,
    salaryMax: searchParams.get('salaryMax') ? parseInt(searchParams.get('salaryMax')!) : undefined,
    jobType: searchParams.get('jobType') || '',
    experience: searchParams.get('experience') || '',
    page: parseInt(searchParams.get('page') || '1'),
    limit: parseInt(searchParams.get('limit') || '20'),
    sort: searchParams.get('sort') || 'relevance'
  }

  const result = await searchService.searchJobs(query)

  return createSuccessResponse(result.jobs, {
    pagination: result.pagination
  })
})

export const POST = withErrorHandler(async (request: NextRequest) => {
  const user = await authenticateRequest(request)
  const jobData = await request.json()

  const job = await jobService.createJob({
    ...jobData,
    postedBy: user.id,
    company: user.companyId
  })

  return createSuccessResponse(job, {
    message: 'Job created successfully'
  })
})
```

## MongoDB GridFS File Storage Service

```typescript
// lib/services/file-storage.service.ts
import { GridFSBucket, MongoClient, ObjectId } from 'mongodb'
import { Readable } from 'stream'

interface FileUploadResult {
  fileId: ObjectId
  filename: string
  contentType: string
  size: number
  uploadDate: Date
  url: string
}

interface FileMetadata {
  userId: string
  fileType: 'resume' | 'cover_letter' | 'portfolio' | 'company_logo' | 'document'
  originalName: string
  description?: string
  tags?: string[]
}

class FileStorageService {
  private bucket: GridFSBucket
  private client: MongoClient

  constructor() {
    this.client = new MongoClient(process.env.MONGODB_URI!)
    this.bucket = new GridFSBucket(this.client.db(), {
      bucketName: 'jobportal_files'
    })
  }

  async uploadFile(
    file: File | Buffer,
    filename: string,
    contentType: string,
    metadata: FileMetadata
  ): Promise<FileUploadResult> {
    try {
      const fileId = new ObjectId()

      // Convert File to Buffer if needed
      const buffer = file instanceof File ? Buffer.from(await file.arrayBuffer()) : file

      // Create readable stream from buffer
      const readableStream = new Readable({
        read() {
          this.push(buffer)
          this.push(null)
        }
      })

      // Upload to GridFS
      const uploadStream = this.bucket.openUploadStreamWithId(fileId, filename, {
        contentType,
        metadata: {
          ...metadata,
          uploadDate: new Date(),
          size: buffer.length
        }
      })

      return new Promise((resolve, reject) => {
        uploadStream.on('error', reject)
        uploadStream.on('finish', () => {
          resolve({
            fileId,
            filename,
            contentType,
            size: buffer.length,
            uploadDate: new Date(),
            url: `/api/files/${fileId}/download`
          })
        })

        readableStream.pipe(uploadStream)
      })
    } catch (error) {
      throw new Error(`File upload failed: ${error.message}`)
    }
  }

  async downloadFile(fileId: string): Promise<{ stream: NodeJS.ReadableStream; metadata: any }> {
    try {
      const objectId = new ObjectId(fileId)

      // Get file metadata
      const fileInfo = await this.bucket.find({ _id: objectId }).next()
      if (!fileInfo) {
        throw new Error('File not found')
      }

      // Create download stream
      const downloadStream = this.bucket.openDownloadStream(objectId)

      return {
        stream: downloadStream,
        metadata: {
          filename: fileInfo.filename,
          contentType: fileInfo.contentType || 'application/octet-stream',
          size: fileInfo.length,
          uploadDate: fileInfo.uploadDate,
          ...fileInfo.metadata
        }
      }
    } catch (error) {
      throw new Error(`File download failed: ${error.message}`)
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      const objectId = new ObjectId(fileId)
      await this.bucket.delete(objectId)
    } catch (error) {
      throw new Error(`File deletion failed: ${error.message}`)
    }
  }

  async getFilesByUser(userId: string, fileType?: string): Promise<any[]> {
    try {
      const query: any = { 'metadata.userId': userId }
      if (fileType) {
        query['metadata.fileType'] = fileType
      }

      return await this.bucket.find(query).toArray()
    } catch (error) {
      throw new Error(`Failed to get user files: ${error.message}`)
    }
  }

  async extractTextFromPDF(fileId: string): Promise<string> {
    try {
      const { stream } = await this.downloadFile(fileId)

      // Convert stream to buffer
      const chunks: Buffer[] = []
      for await (const chunk of stream) {
        chunks.push(chunk)
      }
      const buffer = Buffer.concat(chunks)

      // Extract text using pdf-parse
      const pdfParse = require('pdf-parse')
      const data = await pdfParse(buffer)

      return data.text
    } catch (error) {
      throw new Error(`PDF text extraction failed: ${error.message}`)
    }
  }

  async getFileStats(): Promise<{ totalFiles: number; totalSize: number; fileTypes: Record<string, number> }> {
    try {
      const files = await this.bucket.find({}).toArray()

      const stats = {
        totalFiles: files.length,
        totalSize: files.reduce((sum, file) => sum + file.length, 0),
        fileTypes: {}
      }

      files.forEach(file => {
        const fileType = file.metadata?.fileType || 'unknown'
        stats.fileTypes[fileType] = (stats.fileTypes[fileType] || 0) + 1
      })

      return stats
    } catch (error) {
      throw new Error(`Failed to get file stats: ${error.message}`)
    }
  }
}

export const fileStorageService = new FileStorageService()
```

## Custom Advanced Search Service

```typescript
// lib/services/search.service.ts
import { Job, Company, User } from '@/lib/models'
import { PipelineStage } from 'mongoose'

interface SearchQuery {
  q?: string
  location?: string
  skills?: string[]
  salaryMin?: number
  salaryMax?: number
  jobType?: string
  experience?: string
  company?: string
  datePosted?: 'today' | 'week' | 'month' | 'all'
  remote?: boolean
  page?: number
  limit?: number
  sort?: 'relevance' | 'date' | 'salary_high' | 'salary_low'
}

interface SearchResult<T> {
  results: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  aggregations?: {
    locations: Array<{ name: string; count: number }>
    companies: Array<{ name: string; count: number }>
    skills: Array<{ name: string; count: number }>
    salaryRanges: Array<{ range: string; count: number }>
    jobTypes: Array<{ type: string; count: number }>
  }
  searchTime: number
}

class SearchService {
  async searchJobs(query: SearchQuery): Promise<SearchResult<Job>> {
    const startTime = Date.now()

    // Build aggregation pipeline
    const pipeline: PipelineStage[] = []

    // Match stage - basic filtering
    const matchStage: any = {
      status: 'published',
      publishedAt: { $lte: new Date() }
    }

    // Text search
    if (query.q) {
      matchStage.$text = { $search: query.q }
    }

    // Location filter
    if (query.location) {
      matchStage.$or = [
        { 'location.city': new RegExp(query.location, 'i') },
        { 'location.state': new RegExp(query.location, 'i') },
        { 'location.country': new RegExp(query.location, 'i') }
      ]
    }

    // Remote filter
    if (query.remote) {
      matchStage['location.remote'] = true
    }

    // Skills filter
    if (query.skills && query.skills.length > 0) {
      matchStage['skills.name'] = { $in: query.skills }
    }

    // Salary range filter
    if (query.salaryMin || query.salaryMax) {
      matchStage['salary.min'] = {}
      if (query.salaryMin) matchStage['salary.min'].$gte = query.salaryMin
      if (query.salaryMax) matchStage['salary.max'] = { $lte: query.salaryMax }
    }

    // Job type filter
    if (query.jobType) {
      matchStage.employment = query.jobType
    }

    // Experience level filter
    if (query.experience) {
      matchStage.experience = query.experience
    }

    // Date posted filter
    if (query.datePosted && query.datePosted !== 'all') {
      const dateThresholds = {
        today: new Date(Date.now() - 24 * 60 * 60 * 1000),
        week: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        month: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      }
      matchStage.publishedAt = { $gte: dateThresholds[query.datePosted] }
    }

    pipeline.push({ $match: matchStage })

    // Lookup company information
    pipeline.push({
      $lookup: {
        from: 'companies',
        localField: 'company',
        foreignField: '_id',
        as: 'companyInfo'
      }
    })

    pipeline.push({ $unwind: '$companyInfo' })

    // Add relevance scoring
    pipeline.push({
      $addFields: {
        relevanceScore: {
          $add: [
            // Text search score
            query.q ? { $meta: 'textScore' } : 0,
            // Featured job bonus
            { $cond: [{ $eq: ['$featured', true] }, 10, 0] },
            // Urgent job bonus
            { $cond: [{ $eq: ['$urgent', true] }, 5, 0] },
            // Recent posting bonus
            {
              $cond: [
                { $gte: ['$publishedAt', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] },
                3,
                0
              ]
            },
            // View count factor
            { $divide: [{ $ifNull: ['$views', 0] }, 100] }
          ]
        }
      }
    })

    // Sorting
    const sortStage: any = {}
    switch (query.sort) {
      case 'date':
        sortStage.publishedAt = -1
        break
      case 'salary_high':
        sortStage['salary.max'] = -1
        break
      case 'salary_low':
        sortStage['salary.min'] = 1
        break
      case 'relevance':
      default:
        sortStage.relevanceScore = -1
        sortStage.publishedAt = -1
        break
    }

    pipeline.push({ $sort: sortStage })

    // Facet stage for aggregations and pagination
    pipeline.push({
      $facet: {
        results: [
          { $skip: ((query.page || 1) - 1) * (query.limit || 20) },
          { $limit: query.limit || 20 },
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              description: 1,
              location: 1,
              salary: 1,
              skills: 1,
              employment: 1,
              experience: 1,
              featured: 1,
              urgent: 1,
              views: 1,
              applications: 1,
              publishedAt: 1,
              relevanceScore: 1,
              'companyInfo.name': 1,
              'companyInfo.logo': 1,
              'companyInfo.slug': 1
            }
          }
        ],
        totalCount: [{ $count: 'count' }],
        aggregations: [
          {
            $group: {
              _id: null,
              locations: { $addToSet: '$location.city' },
              companies: { $addToSet: '$companyInfo.name' },
              skills: { $addToSet: '$skills.name' },
              jobTypes: { $addToSet: '$employment' }
            }
          }
        ]
      }
    })

    const [result] = await Job.aggregate(pipeline)
    const searchTime = Date.now() - startTime

    const total = result.totalCount[0]?.count || 0
    const totalPages = Math.ceil(total / (query.limit || 20))

    return {
      results: result.results,
      pagination: {
        page: query.page || 1,
        limit: query.limit || 20,
        total,
        totalPages
      },
      aggregations: this.processAggregations(result.aggregations[0]),
      searchTime
    }
  }

  private processAggregations(aggs: any) {
    if (!aggs) return undefined

    return {
      locations: aggs.locations?.filter(Boolean).map(name => ({ name, count: 1 })) || [],
      companies: aggs.companies?.filter(Boolean).map(name => ({ name, count: 1 })) || [],
      skills: aggs.skills?.flat().filter(Boolean).map(name => ({ name, count: 1 })) || [],
      jobTypes: aggs.jobTypes?.filter(Boolean).map(type => ({ type, count: 1 })) || [],
      salaryRanges: []
    }
  }
}

export const searchService = new SearchService()
```

## Frontend Error Handling System

### Frontend Error Service

```typescript
// lib/errors/frontend-error.service.ts
import { ErrorCode } from './error-types'

export interface FrontendError {
  code: ErrorCode
  message: string
  field?: string
  details?: Record<string, any>
  timestamp: Date
  userFriendly: string
}

class FrontendErrorService {
  private static instance: FrontendErrorService

  static getInstance(): FrontendErrorService {
    if (!FrontendErrorService.instance) {
      FrontendErrorService.instance = new FrontendErrorService()
    }
    return FrontendErrorService.instance
  }

  parseApiError(error: any): FrontendError {
    // Handle API response errors
    if (error.response?.data?.error) {
      const apiError = error.response.data.error
      return {
        code: apiError.code,
        message: apiError.message,
        field: apiError.field,
        details: apiError.details,
        timestamp: new Date(),
        userFriendly: this.getUserFriendlyMessage(apiError.code, apiError.message)
      }
    }

    // Handle network errors
    if (error.code === 'NETWORK_ERROR' || !error.response) {
      return {
        code: 'SERVICE_UNAVAILABLE' as ErrorCode,
        message: 'Network connection failed',
        timestamp: new Date(),
        userFriendly: 'Unable to connect to the server. Please check your internet connection and try again.'
      }
    }

    // Handle timeout errors
    if (error.code === 'TIMEOUT') {
      return {
        code: 'SERVICE_UNAVAILABLE' as ErrorCode,
        message: 'Request timeout',
        timestamp: new Date(),
        userFriendly: 'The request is taking too long. Please try again.'
      }
    }

    // Default error
    return {
      code: 'INTERNAL_SERVER_ERROR' as ErrorCode,
      message: error.message || 'An unexpected error occurred',
      timestamp: new Date(),
      userFriendly: 'Something went wrong. Please try again later.'
    }
  }

  private getUserFriendlyMessage(code: ErrorCode, originalMessage: string): string {
    const friendlyMessages = {
      [ErrorCode.INVALID_CREDENTIALS]: 'The email or password you entered is incorrect. Please try again.',
      [ErrorCode.TOKEN_EXPIRED]: 'Your session has expired. Please sign in again.',
      [ErrorCode.UNAUTHORIZED]: 'You need to sign in to access this feature.',
      [ErrorCode.FORBIDDEN]: 'You don\'t have permission to perform this action.',
      [ErrorCode.VALIDATION_ERROR]: 'Please check the information you entered and try again.',
      [ErrorCode.REQUIRED_FIELD_MISSING]: 'Please fill in all required fields.',
      [ErrorCode.INVALID_FORMAT]: 'Please check the format of the information you entered.',
      [ErrorCode.DUPLICATE_ENTRY]: 'This information is already in use. Please try something different.',
      [ErrorCode.RESOURCE_NOT_FOUND]: 'The requested item could not be found.',
      [ErrorCode.INSUFFICIENT_PERMISSIONS]: 'You don\'t have permission to perform this action.',
      [ErrorCode.QUOTA_EXCEEDED]: 'You\'ve reached your limit. Please upgrade your plan to continue.',
      [ErrorCode.SUBSCRIPTION_REQUIRED]: 'This feature requires a premium subscription.',
      [ErrorCode.FILE_TOO_LARGE]: 'The file you\'re trying to upload is too large. Please choose a smaller file.',
      [ErrorCode.INVALID_FILE_TYPE]: 'This file type is not supported. Please choose a different file.',
      [ErrorCode.UPLOAD_FAILED]: 'File upload failed. Please try again.',
      [ErrorCode.PAYMENT_FAILED]: 'Payment could not be processed. Please check your payment information.',
      [ErrorCode.EMAIL_SEND_FAILED]: 'We couldn\'t send the email. Please try again later.',
      [ErrorCode.RATE_LIMIT_EXCEEDED]: 'You\'re doing that too often. Please wait a moment and try again.',
      [ErrorCode.SERVICE_UNAVAILABLE]: 'The service is temporarily unavailable. Please try again later.',
      [ErrorCode.INTERNAL_SERVER_ERROR]: 'Something went wrong on our end. Please try again later.'
    }

    return friendlyMessages[code] || originalMessage || 'An unexpected error occurred.'
  }

  logError(error: FrontendError, context?: Record<string, any>): void {
    const logData = {
      ...error,
      context,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Frontend Error:', logData)
    }

    // Send to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToErrorTracking(logData)
    }
  }

  private sendToErrorTracking(errorData: any): void {
    // Implementation for error tracking service (e.g., Sentry)
    // Sentry.captureException(errorData)
  }
}

export const frontendErrorService = FrontendErrorService.getInstance()
```

### Error Components

```typescript
// components/ui/error-boundary.tsx
'use client'

import React, { Component, ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error Boundary caught an error:', error, errorInfo)

    // Log to error tracking service
    if (process.env.NODE_ENV === 'production') {
      // Send to error tracking service
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card className="max-w-md mx-auto mt-8">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="w-6 h-6 text-destructive" />
            </div>
            <CardTitle>Something went wrong</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              We encountered an unexpected error. Please try refreshing the page.
            </p>
            <Button
              onClick={() => window.location.reload()}
              className="w-full"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh Page
            </Button>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}
```

```typescript
// components/ui/error-alert.tsx
'use client'

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { AlertTriangle, X, RefreshCw } from 'lucide-react'
import { FrontendError } from '@/lib/errors/frontend-error.service'

interface ErrorAlertProps {
  error: FrontendError
  onDismiss?: () => void
  onRetry?: () => void
  className?: string
}

export function ErrorAlert({ error, onDismiss, onRetry, className }: ErrorAlertProps) {
  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle className="flex items-center justify-between">
        Error
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-auto p-1 text-destructive-foreground hover:bg-destructive/20"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </AlertTitle>
      <AlertDescription className="space-y-2">
        <p>{error.userFriendly}</p>
        {onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="mt-2"
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Try Again
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}
```

```typescript
// components/ui/inline-error.tsx
'use client'

import { AlertCircle } from 'lucide-react'
import { FrontendError } from '@/lib/errors/frontend-error.service'

interface InlineErrorProps {
  error: FrontendError | string
  className?: string
}

export function InlineError({ error, className = '' }: InlineErrorProps) {
  const message = typeof error === 'string' ? error : error.userFriendly

  return (
    <div className={`flex items-center space-x-2 text-sm text-destructive ${className}`}>
      <AlertCircle className="h-4 w-4 flex-shrink-0" />
      <span>{message}</span>
    </div>
  )
}
```

## Loading Components System

### Loading States and Indicators

```typescript
// components/ui/loading-spinner.tsx
'use client'

import { cn } from '@/lib/utils'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-current border-t-transparent',
        sizeClasses[size],
        className
      )}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  )
}
```

```typescript
// components/ui/button-loading.tsx
'use client'

import { Button, ButtonProps } from '@/components/ui/button'
import { LoadingSpinner } from './loading-spinner'
import { cn } from '@/lib/utils'

interface ButtonLoadingProps extends ButtonProps {
  loading?: boolean
  loadingText?: string
}

export function ButtonLoading({
  loading = false,
  loadingText,
  children,
  disabled,
  className,
  ...props
}: ButtonLoadingProps) {
  return (
    <Button
      disabled={disabled || loading}
      className={cn(className)}
      {...props}
    >
      {loading && <LoadingSpinner size="sm" className="mr-2" />}
      {loading ? (loadingText || 'Loading...') : children}
    </Button>
  )
}
```

```typescript
// components/ui/page-loader.tsx
'use client'

import { LoadingSpinner } from './loading-spinner'
import { Card, CardContent } from '@/components/ui/card'

interface PageLoaderProps {
  message?: string
  fullScreen?: boolean
}

export function PageLoader({ message = 'Loading...', fullScreen = false }: PageLoaderProps) {
  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
        <Card className="w-auto">
          <CardContent className="flex flex-col items-center space-y-4 p-6">
            <LoadingSpinner size="lg" />
            <p className="text-muted-foreground">{message}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <LoadingSpinner size="lg" />
      <p className="text-muted-foreground">{message}</p>
    </div>
  )
}
```

```typescript
// components/ui/skeleton-loader.tsx
'use client'

import { cn } from '@/lib/utils'

interface SkeletonProps {
  className?: string
}

export function Skeleton({ className }: SkeletonProps) {
  return (
    <div
      className={cn(
        'animate-pulse rounded-md bg-muted',
        className
      )}
    />
  )
}

// Job Card Skeleton
export function JobCardSkeleton() {
  return (
    <div className="border rounded-lg p-6 space-y-4">
      <div className="flex items-start space-x-4">
        <Skeleton className="h-12 w-12 rounded-lg" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-5 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </div>
      <div className="flex space-x-2">
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-6 w-14" />
      </div>
    </div>
  )
}

// Profile Skeleton
export function ProfileSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Skeleton className="h-20 w-20 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>
      <div className="space-y-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
    </div>
  )
}

// Table Skeleton
export function TableSkeleton({ rows = 5, columns = 4 }: { rows?: number; columns?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, j) => (
            <Skeleton key={j} className="h-4 flex-1" />
          ))}
        </div>
      ))}
    </div>
  )
}
```

```typescript
// components/ui/action-indicator.tsx
'use client'

import { CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

type ActionStatus = 'idle' | 'loading' | 'success' | 'error'

interface ActionIndicatorProps {
  status: ActionStatus
  successMessage?: string
  errorMessage?: string
  loadingMessage?: string
  className?: string
}

export function ActionIndicator({
  status,
  successMessage = 'Success!',
  errorMessage = 'Error occurred',
  loadingMessage = 'Processing...',
  className
}: ActionIndicatorProps) {
  if (status === 'idle') return null

  const indicators = {
    loading: {
      icon: Clock,
      message: loadingMessage,
      className: 'text-blue-600 bg-blue-50 border-blue-200'
    },
    success: {
      icon: CheckCircle,
      message: successMessage,
      className: 'text-green-600 bg-green-50 border-green-200'
    },
    error: {
      icon: XCircle,
      message: errorMessage,
      className: 'text-red-600 bg-red-50 border-red-200'
    }
  }

  const { icon: Icon, message, className: statusClassName } = indicators[status]

  return (
    <div className={cn(
      'flex items-center space-x-2 px-3 py-2 rounded-md border text-sm',
      statusClassName,
      className
    )}>
      <Icon className="h-4 w-4" />
      <span>{message}</span>
    </div>
  )
}
```

```typescript
// components/ui/progress-indicator.tsx
'use client'

import { Progress } from '@/components/ui/progress'
import { CheckCircle } from 'lucide-react'

interface Step {
  id: string
  title: string
  description?: string
  completed: boolean
  current: boolean
}

interface ProgressIndicatorProps {
  steps: Step[]
  className?: string
}

export function ProgressIndicator({ steps, className }: ProgressIndicatorProps) {
  const completedSteps = steps.filter(step => step.completed).length
  const progress = (completedSteps / steps.length) * 100

  return (
    <div className={className}>
      <div className="mb-6">
        <Progress value={progress} className="h-2" />
        <p className="text-sm text-muted-foreground mt-2">
          Step {completedSteps + 1} of {steps.length}
        </p>
      </div>

      <div className="space-y-4">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`flex items-start space-x-3 ${
              step.current ? 'text-primary' : step.completed ? 'text-green-600' : 'text-muted-foreground'
            }`}
          >
            <div className="flex-shrink-0 mt-0.5">
              {step.completed ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <div className={`h-5 w-5 rounded-full border-2 ${
                  step.current ? 'border-primary bg-primary' : 'border-muted-foreground'
                }`}>
                  {step.current && <div className="h-1 w-1 bg-white rounded-full m-1.5" />}
                </div>
              )}
            </div>
            <div>
              <h4 className="font-medium">{step.title}</h4>
              {step.description && (
                <p className="text-sm text-muted-foreground">{step.description}</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
```

## Enhanced Zustand Stores with Error Handling

### Auth Store with Error Handling

```typescript
// stores/auth.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthService } from '@/services/auth.service'
import { frontendErrorService, FrontendError } from '@/lib/errors/frontend-error.service'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: FrontendError | null

  // Loading states for specific actions
  loginLoading: boolean
  registerLoading: boolean
  profileUpdateLoading: boolean
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  updateProfile: (profileData: UpdateProfileData) => Promise<void>
  clearError: () => void
  setLoading: (action: string, loading: boolean) => void
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      loginLoading: false,
      registerLoading: false,
      profileUpdateLoading: false,

      // Actions
      login: async (credentials) => {
        set({ loginLoading: true, error: null })
        try {
          const response = await AuthService.login(credentials)
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            loginLoading: false
          })
        } catch (error) {
          const frontendError = frontendErrorService.parseApiError(error)
          frontendErrorService.logError(frontendError, { action: 'login' })
          set({
            error: frontendError,
            loginLoading: false
          })
          throw frontendError
        }
      },

      register: async (userData) => {
        set({ registerLoading: true, error: null })
        try {
          const response = await AuthService.register(userData)
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            registerLoading: false
          })
        } catch (error) {
          const frontendError = frontendErrorService.parseApiError(error)
          frontendErrorService.logError(frontendError, { action: 'register' })
          set({
            error: frontendError,
            registerLoading: false
          })
          throw frontendError
        }
      },

      logout: () => {
        AuthService.logout()
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null
        })
      },

      refreshToken: async () => {
        try {
          const response = await AuthService.refreshToken()
          set({
            token: response.token,
            user: response.user
          })
        } catch (error) {
          get().logout()
          throw frontendErrorService.parseApiError(error)
        }
      },

      updateProfile: async (profileData) => {
        set({ profileUpdateLoading: true, error: null })
        try {
          const updatedUser = await AuthService.updateProfile(profileData)
          set({
            user: updatedUser,
            profileUpdateLoading: false
          })
        } catch (error) {
          const frontendError = frontendErrorService.parseApiError(error)
          frontendErrorService.logError(frontendError, { action: 'updateProfile' })
          set({
            error: frontendError,
            profileUpdateLoading: false
          })
          throw frontendError
        }
      },

      clearError: () => set({ error: null }),

      setLoading: (action: string, loading: boolean) => {
        set({ [`${action}Loading`]: loading })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
```

### Jobs Store with Search Integration

```typescript
// stores/jobs.store.ts
import { create } from 'zustand'
import { JobService } from '@/services/job.service'
import { searchService } from '@/services/search.service'
import { frontendErrorService, FrontendError } from '@/lib/errors/frontend-error.service'

interface JobsState {
  jobs: Job[]
  currentJob: Job | null
  searchQuery: JobSearchQuery
  filters: JobFilters
  savedJobs: string[]
  appliedJobs: string[]
  recommendations: Job[]

  // Loading states
  searchLoading: boolean
  jobLoading: boolean
  applyLoading: boolean
  saveLoading: boolean
  recommendationsLoading: boolean

  // Error states
  error: FrontendError | null
  searchError: FrontendError | null

  // Pagination and meta
  pagination: PaginationData
  searchMeta: {
    searchTime: number
    totalResults: number
    aggregations?: any
  }
}

interface JobsActions {
  searchJobs: (query: JobSearchQuery) => Promise<void>
  getJobById: (jobId: string) => Promise<void>
  applyToJob: (jobId: string, applicationData: ApplicationData) => Promise<void>
  saveJob: (jobId: string) => Promise<void>
  unsaveJob: (jobId: string) => Promise<void>
  getRecommendations: () => Promise<void>
  updateFilters: (filters: Partial<JobFilters>) => void
  clearSearch: () => void
  clearError: () => void
}

export const useJobsStore = create<JobsState & JobsActions>((set, get) => ({
  // State
  jobs: [],
  currentJob: null,
  searchQuery: { q: '', location: '', page: 1, limit: 20 },
  filters: {},
  savedJobs: [],
  appliedJobs: [],
  recommendations: [],

  // Loading states
  searchLoading: false,
  jobLoading: false,
  applyLoading: false,
  saveLoading: false,
  recommendationsLoading: false,

  // Error states
  error: null,
  searchError: null,

  // Meta
  pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
  searchMeta: { searchTime: 0, totalResults: 0 },

  // Actions
  searchJobs: async (query) => {
    set({ searchLoading: true, searchError: null })
    try {
      const response = await searchService.searchJobs(query)
      set({
        jobs: response.results,
        pagination: response.pagination,
        searchQuery: query,
        searchMeta: {
          searchTime: response.searchTime,
          totalResults: response.pagination.total,
          aggregations: response.aggregations
        },
        searchLoading: false
      })
    } catch (error) {
      const frontendError = frontendErrorService.parseApiError(error)
      frontendErrorService.logError(frontendError, { action: 'searchJobs', query })
      set({
        searchError: frontendError,
        searchLoading: false
      })
    }
  },

  getJobById: async (jobId) => {
    set({ jobLoading: true, error: null })
    try {
      const job = await JobService.getJobById(jobId)
      set({ currentJob: job, jobLoading: false })
    } catch (error) {
      const frontendError = frontendErrorService.parseApiError(error)
      set({ error: frontendError, jobLoading: false })
    }
  },

  applyToJob: async (jobId, applicationData) => {
    set({ applyLoading: true, error: null })
    try {
      await JobService.applyToJob(jobId, applicationData)
      set(state => ({
        appliedJobs: [...state.appliedJobs, jobId],
        applyLoading: false
      }))
    } catch (error) {
      const frontendError = frontendErrorService.parseApiError(error)
      set({ error: frontendError, applyLoading: false })
      throw frontendError
    }
  },

  saveJob: async (jobId) => {
    set({ saveLoading: true })
    try {
      await JobService.saveJob(jobId)
      set(state => ({
        savedJobs: [...state.savedJobs, jobId],
        saveLoading: false
      }))
    } catch (error) {
      const frontendError = frontendErrorService.parseApiError(error)
      set({ error: frontendError, saveLoading: false })
    }
  },

  unsaveJob: async (jobId) => {
    try {
      await JobService.unsaveJob(jobId)
      set(state => ({
        savedJobs: state.savedJobs.filter(id => id !== jobId)
      }))
    } catch (error) {
      const frontendError = frontendErrorService.parseApiError(error)
      set({ error: frontendError })
    }
  },

  getRecommendations: async () => {
    set({ recommendationsLoading: true })
    try {
      const recommendations = await JobService.getRecommendations()
      set({ recommendations, recommendationsLoading: false })
    } catch (error) {
      const frontendError = frontendErrorService.parseApiError(error)
      set({ error: frontendError, recommendationsLoading: false })
    }
  },

  updateFilters: (filters) => {
    set(state => ({
      filters: { ...state.filters, ...filters }
    }))
  },

  clearSearch: () => {
    set({
      jobs: [],
      searchQuery: { q: '', location: '', page: 1, limit: 20 },
      filters: {},
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      searchError: null
    })
  },

  clearError: () => set({ error: null, searchError: null })
}))
```

### Frontend Services
```typescript
// services/api.service.ts
class ApiService {
  private baseURL: string
  private token: string | null

  async get<T>(endpoint: string): Promise<T>
  async post<T>(endpoint: string, data: any): Promise<T>
  async put<T>(endpoint: string, data: any): Promise<T>
  async delete<T>(endpoint: string): Promise<T>
  async upload<T>(endpoint: string, file: File): Promise<T>
}

// services/job.service.ts (Frontend)
class JobService {
  async searchJobs(query: JobSearchQuery): Promise<PaginatedJobs>
  async getJobById(jobId: string): Promise<Job>
  async applyToJob(jobId: string, applicationData: ApplicationData): Promise<Application>
  async saveJob(jobId: string): Promise<void>
  async getRecommendedJobs(): Promise<Job[]>
  async reportJob(jobId: string, reason: string): Promise<void>
}
```

## Premium Features & Monetization

### Subscription Tiers

#### For Companies
1. **Starter Plan** ($99/month)
   - 5 active job postings
   - Basic candidate search
   - Standard support

2. **Professional Plan** ($299/month)
   - 25 active job postings
   - Advanced candidate search & filters
   - AI-powered candidate matching
   - Priority support
   - Company branding

3. **Enterprise Plan** ($999/month)
   - Unlimited job postings
   - Full AI suite access
   - Dedicated account manager
   - Custom integrations
   - Advanced analytics
   - White-label options

#### For Job Seekers
1. **Free Plan**
   - Basic job search
   - 5 applications per month
   - Standard profile

2. **Premium Plan** ($19/month)
   - Unlimited applications
   - AI career recommendations
   - Salary insights
   - Priority application status
   - Advanced profile features

### Revenue Streams
1. **Subscription Revenue**: $50M+ annually (10K companies × $3K average)
2. **Featured Job Listings**: $500-2000 per job
3. **Recruitment Services**: 15-25% of first-year salary
4. **Enterprise Integrations**: $10K-100K setup fees
5. **Data & Analytics**: $5K-50K per company annually

## Key Implementation Highlights

### 1. Next.js 15 API Routes Architecture
- **Modern Approach**: Using Next.js API route handlers instead of Express
- **Type Safety**: Full TypeScript integration with proper error handling
- **Serverless Ready**: Optimized for deployment on Vercel, Netlify, or AWS Lambda
- **Built-in Optimization**: Automatic code splitting and performance optimization

### 2. MongoDB GridFS File Storage
- **No External Dependencies**: Files stored directly in MongoDB using GridFS
- **Cost Effective**: No additional storage service fees
- **Integrated**: Seamless integration with existing MongoDB database
- **Scalable**: Handles files of any size with automatic chunking

### 3. Custom Advanced Search Service
- **No External Search Service**: Custom MongoDB aggregation-based search
- **Cost Effective**: No Elasticsearch or Algolia fees
- **Highly Customizable**: Tailored specifically for job search requirements
- **Real-time**: Instant search results with advanced filtering

### 4. Comprehensive Error Handling
- **Backend Error Service**: Centralized error handling with proper logging
- **Frontend Error Service**: User-friendly error messages and recovery
- **Error Components**: Pre-built UI components for different error states
- **Error Tracking**: Integration ready for services like Sentry

### 5. Advanced Loading System
- **Multiple Loading States**: Page loaders, button spinners, skeleton screens
- **Action Indicators**: Progress indicators for multi-step processes
- **Optimistic Updates**: Immediate UI feedback with rollback on errors
- **Performance Focused**: Minimal impact on user experience

## Implementation Timeline (Simplified)

### Phase 1: Foundation (Weeks 1-3)
- **Week 1**: Database setup, error handling system, basic API routes
- **Week 2**: Authentication system, user management, file storage
- **Week 3**: Frontend error components, loading system, basic stores

### Phase 2: Core Features (Weeks 4-7)
- **Week 4**: Job management system, company profiles
- **Week 5**: Advanced search service, job search functionality
- **Week 6**: Application system, candidate profiles
- **Week 7**: AI integration, job recommendations

### Phase 3: Advanced Features (Weeks 8-11)
- **Week 8**: Real-time notifications, messaging system
- **Week 9**: Payment integration, subscription management
- **Week 10**: Admin dashboard, analytics
- **Week 11**: Performance optimization, caching

### Phase 4: Polish & Launch (Weeks 12-14)
- **Week 12**: UI/UX refinements, mobile optimization
- **Week 13**: Testing, bug fixes, security audit
- **Week 14**: Deployment, monitoring setup, launch preparation

## Key Competitive Advantages

### 1. Cost-Effective Architecture
- **No External Services**: MongoDB for everything (database + file storage + search)
- **Next.js Efficiency**: Single codebase for frontend and backend
- **Reduced Complexity**: Fewer moving parts, easier maintenance

### 2. Superior Error Handling
- **User-Friendly**: Clear, actionable error messages
- **Developer-Friendly**: Comprehensive error logging and tracking
- **Resilient**: Graceful degradation and recovery mechanisms

### 3. Performance Optimized
- **Fast Search**: Custom MongoDB aggregation pipelines
- **Efficient Loading**: Smart loading states and skeleton screens
- **Optimistic UI**: Immediate feedback with error rollback

### 4. Enterprise Ready
- **Scalable**: Built to handle millions of users
- **Secure**: Comprehensive security measures and audit trails
- **Maintainable**: Clean architecture with proper separation of concerns

## Revenue Projections (Simplified Model)

### Year 1 Targets
- **Companies**: 500 paying customers @ $200/month average = $1.2M
- **Job Seekers**: 50,000 users, 5% premium @ $15/month = $450K
- **Total Year 1 Revenue**: $1.65M

### Year 2 Targets
- **Companies**: 2,000 customers @ $300/month average = $7.2M
- **Job Seekers**: 200,000 users, 8% premium @ $18/month = $3.46M
- **Enterprise Deals**: 10 @ $25K each = $250K
- **Total Year 2 Revenue**: $10.91M

### Year 3 Targets
- **Companies**: 5,000 customers @ $400/month average = $24M
- **Job Seekers**: 500,000 users, 12% premium @ $20/month = $14.4M
- **Enterprise Revenue**: $5M
- **Total Year 3 Revenue**: $43.4M

## Conclusion

This simplified but comprehensive implementation plan creates a robust, scalable job portal using modern technologies while keeping costs low and maintaining high performance. The focus on Next.js 15 API routes, MongoDB GridFS storage, custom search utilities, and comprehensive error handling will set it apart from competitors while ensuring a superior user experience and developer productivity.
4. **Enterprise Integrations**: $10K-100K setup fees
5. **Data & Analytics**: $5K-50K per company annually

## Implementation Phases

### Phase 1: Core Backend (Weeks 1-4)
- Database setup & models
- Authentication system
- Basic CRUD operations
- API route structure

### Phase 2: User Management (Weeks 5-6)
- User registration/login
- Profile management
- Role-based permissions

### Phase 3: Job & Company Management (Weeks 7-10)
- Company profiles
- Job posting system
- Application management
- Search functionality

### Phase 4: Advanced Features (Weeks 11-14)
- AI matching system
- Real-time notifications
- File upload system
- Payment integration

### Phase 5: Enterprise Features (Weeks 15-18)
- Advanced analytics
- Bulk operations
- API integrations
- Performance optimization

### Phase 6: Launch Preparation (Weeks 19-20)
- Security audit
- Performance testing
- Documentation
- Deployment setup

## Success Metrics & KPIs

### Technical Metrics
- **Response Time**: <200ms for API calls
- **Uptime**: 99.9% availability
- **Scalability**: Handle 100K+ concurrent users

### Business Metrics
- **User Acquisition**: 10K+ companies, 1M+ job seekers
- **Revenue**: $50M+ ARR within 2 years
- **Job Success Rate**: 80%+ application-to-hire ratio
- **Customer Satisfaction**: 4.8+ rating

This implementation plan creates a world-class job portal that enterprises will pay premium prices for, with scalable architecture, advanced AI features, and comprehensive business functionality.

## Detailed Technical Implementation

### Backend API Routes Structure

```typescript
// API Routes Organization
/api/v1/
├── auth/
│   ├── POST /register
│   ├── POST /login
│   ├── POST /refresh
│   ├── POST /logout
│   ├── POST /forgot-password
│   ├── POST /reset-password
│   ├── POST /verify-email
│   └── POST /social-login
├── users/
│   ├── GET /profile
│   ├── PUT /profile
│   ├── DELETE /account
│   ├── GET /preferences
│   ├── PUT /preferences
│   └── GET /stats
├── companies/
│   ├── GET / (search & list)
│   ├── POST / (create)
│   ├── GET /:id
│   ├── PUT /:id
│   ├── DELETE /:id
│   ├── POST /:id/follow
│   ├── DELETE /:id/unfollow
│   ├── GET /:id/jobs
│   ├── GET /:id/stats
│   ├── POST /:id/team
│   └── DELETE /:id/team/:userId
├── jobs/
│   ├── GET / (search & list)
│   ├── POST / (create)
│   ├── GET /:id
│   ├── PUT /:id
│   ├── DELETE /:id
│   ├── POST /:id/apply
│   ├── POST /:id/save
│   ├── DELETE /:id/unsave
│   ├── GET /:id/applications
│   ├── GET /:id/stats
│   └── POST /:id/report
├── applications/
│   ├── GET / (list user's applications)
│   ├── GET /:id
│   ├── PUT /:id/status
│   ├── DELETE /:id (withdraw)
│   ├── POST /:id/feedback
│   └── GET /stats
├── ai/
│   ├── GET /recommendations
│   ├── POST /match-score
│   ├── POST /analyze-resume
│   ├── POST /generate-job-description
│   └── GET /salary-prediction
├── admin/
│   ├── GET /dashboard
│   ├── GET /users
│   ├── GET /companies
│   ├── GET /jobs
│   ├── PUT /companies/:id/verify
│   ├── PUT /jobs/:id/approve
│   └── GET /analytics
└── payments/
    ├── POST /create-subscription
    ├── PUT /update-subscription
    ├── POST /cancel-subscription
    ├── GET /billing-history
    └── POST /webhooks/stripe
```

### Advanced Database Relationships & Indexes

```typescript
// MongoDB Indexes for Performance
db.jobs.createIndex({ "title": "text", "description": "text", "skills.name": "text" })
db.jobs.createIndex({ "location.city": 1, "location.country": 1 })
db.jobs.createIndex({ "salary.min": 1, "salary.max": 1 })
db.jobs.createIndex({ "company": 1, "status": 1, "createdAt": -1 })
db.jobs.createIndex({ "skills.name": 1, "experience": 1 })
db.jobs.createIndex({ "featured": 1, "urgent": 1, "publishedAt": -1 })

db.applications.createIndex({ "candidate": 1, "status": 1, "createdAt": -1 })
db.applications.createIndex({ "job": 1, "status": 1, "createdAt": -1 })
db.applications.createIndex({ "company": 1, "status": 1, "createdAt": -1 })

db.users.createIndex({ "email": 1 }, { unique: true })
db.users.createIndex({ "role": 1, "isActive": 1 })
db.users.createIndex({ "profile.location.city": 1, "profile.location.country": 1 })

db.companies.createIndex({ "slug": 1 }, { unique: true })
db.companies.createIndex({ "industry": 1, "size": 1 })
db.companies.createIndex({ "location.city": 1, "location.country": 1 })
```

### Real-time Features with Socket.io

```typescript
// Real-time Events
interface SocketEvents {
  // Job-related events
  'job:new': (job: Job) => void
  'job:updated': (job: Job) => void
  'job:application': (application: Application) => void

  // Application events
  'application:status_changed': (application: Application) => void
  'application:feedback': (feedback: ApplicationFeedback) => void

  // Notification events
  'notification:new': (notification: Notification) => void
  'notification:read': (notificationId: string) => void

  // Chat/messaging events
  'message:new': (message: Message) => void
  'message:typing': (userId: string) => void

  // System events
  'system:maintenance': (message: string) => void
  'system:announcement': (announcement: Announcement) => void
}

// Socket.io Server Setup
class SocketService {
  private io: Server

  constructor(server: http.Server) {
    this.io = new Server(server, {
      cors: { origin: process.env.FRONTEND_URL }
    })
    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      socket.on('join:user', (userId) => {
        socket.join(`user:${userId}`)
      })

      socket.on('join:company', (companyId) => {
        socket.join(`company:${companyId}`)
      })

      socket.on('join:job', (jobId) => {
        socket.join(`job:${jobId}`)
      })
    })
  }

  notifyUser(userId: string, event: string, data: any) {
    this.io.to(`user:${userId}`).emit(event, data)
  }

  notifyCompany(companyId: string, event: string, data: any) {
    this.io.to(`company:${companyId}`).emit(event, data)
  }
}
```

### Advanced Search with Elasticsearch

```typescript
// Elasticsearch Job Search Service
class ElasticsearchService {
  private client: Client

  async indexJob(job: Job): Promise<void> {
    await this.client.index({
      index: 'jobs',
      id: job._id.toString(),
      body: {
        title: job.title,
        description: job.description,
        company: job.company,
        location: job.location,
        skills: job.skills,
        salary: job.salary,
        experience: job.experience,
        employment: job.employment,
        tags: job.tags,
        createdAt: job.createdAt,
        featured: job.featured,
        urgent: job.urgent
      }
    })
  }

  async searchJobs(query: JobSearchQuery): Promise<SearchResult> {
    const searchBody = {
      query: {
        bool: {
          must: [],
          filter: [],
          should: []
        }
      },
      sort: [],
      from: (query.page - 1) * query.limit,
      size: query.limit,
      aggs: {
        locations: { terms: { field: 'location.city.keyword' } },
        companies: { terms: { field: 'company.name.keyword' } },
        skills: { terms: { field: 'skills.name.keyword' } },
        salary_ranges: { histogram: { field: 'salary.min', interval: 10000 } }
      }
    }

    // Add text search
    if (query.q) {
      searchBody.query.bool.must.push({
        multi_match: {
          query: query.q,
          fields: ['title^3', 'description^2', 'skills.name^2', 'company.name'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      })
    }

    // Add filters
    if (query.location) {
      searchBody.query.bool.filter.push({
        term: { 'location.city.keyword': query.location }
      })
    }

    if (query.salaryMin || query.salaryMax) {
      const salaryFilter: any = { range: { 'salary.min': {} } }
      if (query.salaryMin) salaryFilter.range['salary.min'].gte = query.salaryMin
      if (query.salaryMax) salaryFilter.range['salary.max'].lte = query.salaryMax
      searchBody.query.bool.filter.push(salaryFilter)
    }

    // Add sorting
    switch (query.sort) {
      case 'newest':
        searchBody.sort.push({ createdAt: { order: 'desc' } })
        break
      case 'salary_high':
        searchBody.sort.push({ 'salary.max': { order: 'desc' } })
        break
      case 'relevance':
      default:
        searchBody.sort.push({ _score: { order: 'desc' } })
        break
    }

    const result = await this.client.search({
      index: 'jobs',
      body: searchBody
    })

    return {
      jobs: result.body.hits.hits.map(hit => hit._source),
      total: result.body.hits.total.value,
      aggregations: result.body.aggregations,
      took: result.body.took
    }
  }
}
```

### AI/ML Integration Architecture

```typescript
// AI Service with OpenAI Integration
class AIService {
  private openai: OpenAI
  private vectorStore: VectorStore

  constructor() {
    this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY })
    this.vectorStore = new PineconeVectorStore()
  }

  async generateJobRecommendations(candidateId: string): Promise<JobRecommendation[]> {
    const candidate = await CandidateProfile.findOne({ user: candidateId })
    const candidateVector = await this.createCandidateVector(candidate)

    // Find similar jobs using vector similarity
    const similarJobs = await this.vectorStore.similaritySearch(candidateVector, 20)

    // Use GPT to rank and explain matches
    const prompt = `
      Candidate Profile: ${JSON.stringify(candidate)}
      Similar Jobs: ${JSON.stringify(similarJobs)}

      Rank these jobs by match quality and provide reasoning for each match.
      Consider: skills alignment, experience level, salary expectations, location preferences.
    `

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.3
    })

    return this.parseJobRecommendations(response.choices[0].message.content)
  }

  async analyzeResume(resumeText: string): Promise<ResumeAnalysis> {
    const prompt = `
      Analyze this resume and extract structured information:

      Resume Text: ${resumeText}

      Extract and return JSON with:
      - skills: array of technical and soft skills
      - experience: work experience with companies, roles, duration
      - education: degrees, institutions, years
      - certifications: professional certifications
      - summary: professional summary
      - strengths: key strengths identified
      - improvements: areas for improvement
    `

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.1
    })

    return JSON.parse(response.choices[0].message.content)
  }

  async generateJobDescription(jobTitle: string, requirements: string[]): Promise<string> {
    const prompt = `
      Generate a compelling job description for: ${jobTitle}

      Requirements: ${requirements.join(', ')}

      Include:
      - Engaging job summary
      - Key responsibilities
      - Required qualifications
      - Preferred qualifications
      - Company benefits section placeholder

      Make it professional, inclusive, and attractive to top talent.
    `

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7
    })

    return response.choices[0].message.content
  }
}
```

### File Upload & Storage Service

```typescript
// File Upload Service with AWS S3
class FileUploadService {
  private s3: AWS.S3
  private cloudinary: v2

  constructor() {
    this.s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION
    })

    this.cloudinary.config({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
      api_key: process.env.CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET
    })
  }

  async uploadResume(file: Express.Multer.File, userId: string): Promise<UploadResult> {
    const fileName = `resumes/${userId}/${Date.now()}-${file.originalname}`

    const uploadParams = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: fileName,
      Body: file.buffer,
      ContentType: file.mimetype,
      ServerSideEncryption: 'AES256',
      Metadata: {
        userId: userId,
        uploadedAt: new Date().toISOString()
      }
    }

    const result = await this.s3.upload(uploadParams).promise()

    // Extract text from PDF for AI analysis
    const resumeText = await this.extractTextFromPDF(file.buffer)

    return {
      url: result.Location,
      key: result.Key,
      text: resumeText,
      size: file.size,
      mimeType: file.mimetype
    }
  }

  async uploadCompanyLogo(file: Express.Multer.File, companyId: string): Promise<UploadResult> {
    const result = await this.cloudinary.uploader.upload_stream(
      {
        folder: `company-logos/${companyId}`,
        transformation: [
          { width: 200, height: 200, crop: 'fill' },
          { quality: 'auto', fetch_format: 'auto' }
        ]
      },
      (error, result) => {
        if (error) throw error
        return result
      }
    )

    return {
      url: result.secure_url,
      publicId: result.public_id,
      width: result.width,
      height: result.height
    }
  }

  private async extractTextFromPDF(buffer: Buffer): Promise<string> {
    // Use pdf-parse or similar library
    const pdfParse = require('pdf-parse')
    const data = await pdfParse(buffer)
    return data.text
  }
}
```

### Email Service Integration

```typescript
// Email Service with SendGrid
class EmailService {
  private sgMail: MailService

  constructor() {
    this.sgMail = require('@sendgrid/mail')
    this.sgMail.setApiKey(process.env.SENDGRID_API_KEY)
  }

  async sendWelcomeEmail(user: User): Promise<void> {
    const msg = {
      to: user.email,
      from: process.env.FROM_EMAIL,
      templateId: 'd-welcome-template-id',
      dynamicTemplateData: {
        firstName: user.profile.firstName,
        loginUrl: `${process.env.FRONTEND_URL}/signin`
      }
    }

    await this.sgMail.send(msg)
  }

  async sendJobAlert(user: User, jobs: Job[]): Promise<void> {
    const msg = {
      to: user.email,
      from: process.env.FROM_EMAIL,
      templateId: 'd-job-alert-template-id',
      dynamicTemplateData: {
        firstName: user.profile.firstName,
        jobs: jobs.map(job => ({
          title: job.title,
          company: job.company.name,
          location: job.location.city,
          url: `${process.env.FRONTEND_URL}/jobs/${job.slug}`
        }))
      }
    }

    await this.sgMail.send(msg)
  }

  async sendApplicationStatusUpdate(application: Application): Promise<void> {
    const msg = {
      to: application.candidate.email,
      from: process.env.FROM_EMAIL,
      templateId: 'd-application-status-template-id',
      dynamicTemplateData: {
        candidateName: application.candidate.profile.firstName,
        jobTitle: application.job.title,
        companyName: application.job.company.name,
        status: application.status,
        applicationUrl: `${process.env.FRONTEND_URL}/applications/${application._id}`
      }
    }

    await this.sgMail.send(msg)
  }
}
```

### Payment Integration with Stripe

```typescript
// Payment Service for Subscription Management
class PaymentService {
  private stripe: Stripe

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16'
    })
  }

  async createCustomer(user: User): Promise<Stripe.Customer> {
    return await this.stripe.customers.create({
      email: user.email,
      name: `${user.profile.firstName} ${user.profile.lastName}`,
      metadata: {
        userId: user._id.toString(),
        role: user.role
      }
    })
  }

  async createSubscription(customerId: string, priceId: string): Promise<Stripe.Subscription> {
    return await this.stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent']
    })
  }

  async handleWebhook(body: string, signature: string): Promise<void> {
    const event = this.stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    )

    switch (event.type) {
      case 'customer.subscription.created':
        await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break
      case 'customer.subscription.updated':
        await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break
      case 'customer.subscription.deleted':
        await this.handleSubscriptionCanceled(event.data.object as Stripe.Subscription)
        break
      case 'invoice.payment_succeeded':
        await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice)
        break
      case 'invoice.payment_failed':
        await this.handlePaymentFailed(event.data.object as Stripe.Invoice)
        break
    }
  }

  private async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<void> {
    const customer = await this.stripe.customers.retrieve(subscription.customer as string)
    const userId = (customer as Stripe.Customer).metadata.userId

    await User.findByIdAndUpdate(userId, {
      'subscription.stripeSubscriptionId': subscription.id,
      'subscription.status': subscription.status,
      'subscription.currentPeriodEnd': new Date(subscription.current_period_end * 1000)
    })
  }
}
```

### Frontend Zustand Store Implementation

```typescript
// stores/auth.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthService } from '@/services/auth.service'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  updateProfile: (profileData: UpdateProfileData) => Promise<void>
  clearError: () => void
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null })
        try {
          const response = await AuthService.login(credentials)
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({
            error: error.message,
            isLoading: false
          })
          throw error
        }
      },

      register: async (userData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await AuthService.register(userData)
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({
            error: error.message,
            isLoading: false
          })
          throw error
        }
      },

      logout: () => {
        AuthService.logout()
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null
        })
      },

      refreshToken: async () => {
        try {
          const response = await AuthService.refreshToken()
          set({
            token: response.token,
            user: response.user
          })
        } catch (error) {
          get().logout()
          throw error
        }
      },

      updateProfile: async (profileData) => {
        set({ isLoading: true })
        try {
          const updatedUser = await AuthService.updateProfile(profileData)
          set({
            user: updatedUser,
            isLoading: false
          })
        } catch (error) {
          set({
            error: error.message,
            isLoading: false
          })
          throw error
        }
      },

      clearError: () => set({ error: null })
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// stores/jobs.store.ts
import { create } from 'zustand'
import { JobService } from '@/services/job.service'

interface JobsState {
  jobs: Job[]
  currentJob: Job | null
  searchQuery: JobSearchQuery
  filters: JobFilters
  savedJobs: string[]
  appliedJobs: string[]
  isLoading: boolean
  error: string | null
  pagination: PaginationData
  recommendations: Job[]
}

interface JobsActions {
  searchJobs: (query: JobSearchQuery) => Promise<void>
  getJobById: (jobId: string) => Promise<void>
  applyToJob: (jobId: string, applicationData: ApplicationData) => Promise<void>
  saveJob: (jobId: string) => Promise<void>
  unsaveJob: (jobId: string) => Promise<void>
  getRecommendations: () => Promise<void>
  updateFilters: (filters: Partial<JobFilters>) => void
  clearSearch: () => void
}

export const useJobsStore = create<JobsState & JobsActions>((set, get) => ({
  // State
  jobs: [],
  currentJob: null,
  searchQuery: { q: '', location: '', page: 1, limit: 20 },
  filters: {},
  savedJobs: [],
  appliedJobs: [],
  isLoading: false,
  error: null,
  pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
  recommendations: [],

  // Actions
  searchJobs: async (query) => {
    set({ isLoading: true, error: null })
    try {
      const response = await JobService.searchJobs(query)
      set({
        jobs: response.jobs,
        pagination: response.pagination,
        searchQuery: query,
        isLoading: false
      })
    } catch (error) {
      set({ error: error.message, isLoading: false })
    }
  },

  getJobById: async (jobId) => {
    set({ isLoading: true, error: null })
    try {
      const job = await JobService.getJobById(jobId)
      set({ currentJob: job, isLoading: false })
    } catch (error) {
      set({ error: error.message, isLoading: false })
    }
  },

  applyToJob: async (jobId, applicationData) => {
    try {
      await JobService.applyToJob(jobId, applicationData)
      set(state => ({
        appliedJobs: [...state.appliedJobs, jobId]
      }))
    } catch (error) {
      set({ error: error.message })
      throw error
    }
  },

  saveJob: async (jobId) => {
    try {
      await JobService.saveJob(jobId)
      set(state => ({
        savedJobs: [...state.savedJobs, jobId]
      }))
    } catch (error) {
      set({ error: error.message })
    }
  },

  unsaveJob: async (jobId) => {
    try {
      await JobService.unsaveJob(jobId)
      set(state => ({
        savedJobs: state.savedJobs.filter(id => id !== jobId)
      }))
    } catch (error) {
      set({ error: error.message })
    }
  },

  getRecommendations: async () => {
    try {
      const recommendations = await JobService.getRecommendations()
      set({ recommendations })
    } catch (error) {
      set({ error: error.message })
    }
  },

  updateFilters: (filters) => {
    set(state => ({
      filters: { ...state.filters, ...filters }
    }))
  },

  clearSearch: () => {
    set({
      jobs: [],
      searchQuery: { q: '', location: '', page: 1, limit: 20 },
      filters: {},
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
    })
  }
}))
```

### Frontend Service Layer

```typescript
// services/api.service.ts
class ApiService {
  private baseURL: string
  private token: string | null = null

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1'
  }

  setToken(token: string) {
    this.token = token
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json'
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    return headers
  }

  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'GET',
      headers: this.getHeaders()
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
      headers: this.getHeaders()
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  async upload<T>(endpoint: string, formData: FormData): Promise<T> {
    const headers: HeadersInit = {}

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }
}

export const apiService = new ApiService()
```

### Security Implementation

```typescript
// Security Middleware & Best Practices
import rateLimit from 'express-rate-limit'
import helmet from 'helmet'
import cors from 'cors'
import mongoSanitize from 'express-mongo-sanitize'

// Rate limiting configuration
const createRateLimiter = (windowMs: number, max: number) =>
  rateLimit({
    windowMs,
    max,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  })

// Security middleware setup
export const securityMiddleware = [
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'", process.env.FRONTEND_URL]
      }
    }
  }),
  cors({
    origin: process.env.FRONTEND_URL,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }),
  mongoSanitize(),
  createRateLimiter(15 * 60 * 1000, 100), // 100 requests per 15 minutes
]

// Authentication middleware
export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ error: 'Access token required' })
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload
    const user = await User.findById(decoded.userId).select('-password')

    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'Invalid or inactive user' })
    }

    req.user = user
    next()
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' })
  }
}

// Role-based authorization
export const authorize = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' })
    }

    next()
  }
}
```

### Performance Optimization

```typescript
// Caching Strategy with Redis
import Redis from 'ioredis'

class CacheService {
  private redis: Redis

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3
    })
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value))
    } catch (error) {
      console.error('Cache set error:', error)
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key)
    } catch (error) {
      console.error('Cache delete error:', error)
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern)
      if (keys.length > 0) {
        await this.redis.del(...keys)
      }
    } catch (error) {
      console.error('Cache invalidate error:', error)
    }
  }
}

// Database optimization with aggregation pipelines
class JobService {
  async getJobsWithAggregation(query: JobSearchQuery): Promise<PaginatedJobs> {
    const pipeline = [
      // Match stage
      {
        $match: {
          status: 'published',
          ...(query.location && { 'location.city': query.location }),
          ...(query.salaryMin && { 'salary.min': { $gte: query.salaryMin } }),
          ...(query.skills && { 'skills.name': { $in: query.skills } })
        }
      },

      // Lookup company information
      {
        $lookup: {
          from: 'companies',
          localField: 'company',
          foreignField: '_id',
          as: 'companyInfo'
        }
      },

      // Unwind company info
      { $unwind: '$companyInfo' },

      // Add computed fields
      {
        $addFields: {
          relevanceScore: {
            $add: [
              { $cond: [{ $eq: ['$featured', true] }, 10, 0] },
              { $cond: [{ $eq: ['$urgent', true] }, 5, 0] },
              { $divide: ['$views', 100] }
            ]
          }
        }
      },

      // Sort by relevance and date
      { $sort: { relevanceScore: -1, createdAt: -1 } },

      // Pagination
      { $skip: (query.page - 1) * query.limit },
      { $limit: query.limit },

      // Project final fields
      {
        $project: {
          title: 1,
          slug: 1,
          description: 1,
          location: 1,
          salary: 1,
          skills: 1,
          employment: 1,
          featured: 1,
          urgent: 1,
          views: 1,
          applications: 1,
          createdAt: 1,
          'companyInfo.name': 1,
          'companyInfo.logo': 1,
          'companyInfo.slug': 1
        }
      }
    ]

    const [jobs, totalCount] = await Promise.all([
      Job.aggregate(pipeline),
      Job.countDocuments(pipeline[0].$match)
    ])

    return {
      jobs,
      pagination: {
        page: query.page,
        limit: query.limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / query.limit)
      }
    }
  }
}
```

## Deployment & DevOps Strategy

### Docker Configuration

```dockerfile
# Backend Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime

WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .

EXPOSE 3001
CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=${MONGODB_URI}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongodb
      - redis
      - elasticsearch

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3001/api/v1

  mongodb:
    image: mongo:7
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  elasticsearch:
    image: elasticsearch:8.11.0
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

volumes:
  mongodb_data:
  redis_data:
  elasticsearch_data:
```

### CI/CD Pipeline (GitHub Actions)

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run lint
      - run: npm run type-check

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Build and push Docker images
        run: |
          docker build -t jobportal-backend ./backend
          docker build -t jobportal-frontend ./frontend

          aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REGISTRY

          docker tag jobportal-backend:latest $ECR_REGISTRY/jobportal-backend:latest
          docker tag jobportal-frontend:latest $ECR_REGISTRY/jobportal-frontend:latest

          docker push $ECR_REGISTRY/jobportal-backend:latest
          docker push $ECR_REGISTRY/jobportal-frontend:latest

      - name: Deploy to ECS
        run: |
          aws ecs update-service --cluster jobportal-cluster --service backend-service --force-new-deployment
          aws ecs update-service --cluster jobportal-cluster --service frontend-service --force-new-deployment
```

## Monitoring & Analytics

### Application Monitoring

```typescript
// Monitoring service with custom metrics
import { createPrometheusMetrics } from 'prom-client'

class MonitoringService {
  private metrics = {
    httpRequests: new prometheus.Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status']
    }),

    jobApplications: new prometheus.Counter({
      name: 'job_applications_total',
      help: 'Total number of job applications',
      labelNames: ['company', 'job_type']
    }),

    userRegistrations: new prometheus.Counter({
      name: 'user_registrations_total',
      help: 'Total number of user registrations',
      labelNames: ['user_type', 'source']
    }),

    responseTime: new prometheus.Histogram({
      name: 'http_request_duration_seconds',
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'route']
    })
  }

  recordHttpRequest(method: string, route: string, status: number) {
    this.metrics.httpRequests.inc({ method, route, status: status.toString() })
  }

  recordJobApplication(company: string, jobType: string) {
    this.metrics.jobApplications.inc({ company, job_type: jobType })
  }

  recordUserRegistration(userType: string, source: string) {
    this.metrics.userRegistrations.inc({ user_type: userType, source })
  }
}
```

## Enterprise Features & Competitive Advantages

### 1. Advanced AI-Powered Features
- **Smart Job Matching**: 95%+ accuracy using ML algorithms
- **Resume Optimization**: AI-powered resume improvement suggestions
- **Interview Preparation**: Personalized interview questions and tips
- **Salary Negotiation**: Data-driven salary recommendations
- **Career Path Planning**: AI-generated career progression roadmaps

### 2. Enterprise Integration Capabilities
- **ATS Integration**: Seamless integration with existing Applicant Tracking Systems
- **HRIS Sync**: Two-way sync with HR Information Systems
- **Single Sign-On (SSO)**: SAML/OAuth integration for enterprise authentication
- **API-First Architecture**: Comprehensive REST and GraphQL APIs
- **Webhook System**: Real-time event notifications for external systems

### 3. Advanced Analytics & Reporting
- **Recruitment Analytics**: Detailed hiring funnel analysis
- **Market Intelligence**: Industry salary trends and hiring patterns
- **Diversity Metrics**: DEI tracking and reporting
- **ROI Calculations**: Cost-per-hire and time-to-fill analytics
- **Predictive Analytics**: Candidate success probability scoring

### 4. White-Label Solutions
- **Custom Branding**: Full customization of UI/UX for enterprise clients
- **Domain Mapping**: Custom domain support for enterprise portals
- **Feature Customization**: Modular feature enabling/disabling
- **Custom Workflows**: Configurable hiring processes per organization

## Revenue Projections & Business Model

### Year 1 Targets
- **Companies**: 1,000 paying customers
- **Average Revenue Per User (ARPU)**: $3,600/year
- **Total Revenue**: $3.6M
- **Job Seekers**: 100,000 registered users
- **Premium Conversions**: 10% (10,000 × $228/year = $2.28M)
- **Total Year 1 Revenue**: $5.88M

### Year 2 Targets
- **Companies**: 5,000 paying customers
- **ARPU Growth**: $4,200/year (enterprise upsells)
- **Company Revenue**: $21M
- **Job Seekers**: 500,000 registered users
- **Premium Revenue**: $11.4M
- **Enterprise Deals**: 50 × $50K = $2.5M
- **Total Year 2 Revenue**: $34.9M

### Year 3 Targets
- **Companies**: 15,000 paying customers
- **Company Revenue**: $63M
- **Job Seekers**: 2M registered users
- **Premium Revenue**: $45.6M
- **Enterprise Revenue**: $25M
- **Total Year 3 Revenue**: $133.6M

This comprehensive implementation plan creates a world-class, enterprise-grade job portal that will command premium pricing and achieve significant market penetration through superior technology, AI capabilities, and comprehensive feature set.
```
