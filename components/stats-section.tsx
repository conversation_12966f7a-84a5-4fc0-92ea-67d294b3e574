// "use client"

// import { motion } from "framer-motion"
// import { TrendingUp, Users, Building2, Award } from "lucide-react"
// import { AnimatedCounter } from "@/components/animated-counter"

// const stats = [
//   {
//     icon: TrendingUp,
//     number: 98,
//     suffix: "%",
//     label: "Success Rate",
//     description: "Job placement success",
//   },
//   {
//     icon: Users,
//     number: 500000,
//     suffix: "+",
//     label: "Active Users",
//     description: "Professionals trust us",
//   },
//   {
//     icon: Building2,
//     number: 15000,
//     suffix: "+",
//     label: "Companies",
//     description: "Partner with us",
//   },
//   {
//     icon: Award,
//     number: 50,
//     suffix: "+",
//     label: "Awards Won",
//     description: "Industry recognition",
//   },
// ]

// export function StatsSection() {
//   const containerVariants = {
//     hidden: { opacity: 0 },
//     visible: {
//       opacity: 1,
//       transition: {
//         staggerChildren: 0.2,
//       },
//     },
//   }

//   const itemVariants = {
//     hidden: { opacity: 0, y: 30 },
//     visible: {
//       opacity: 1,
//       y: 0,
//       transition: {
//         duration: 0.6,
//         ease: "easeOut",
//       },
//     },
//   }

//   return (
//     <section className="py-20 bg-primary/5">
//       <div className="container mx-auto px-4">
//         <motion.div
//           variants={containerVariants}
//           initial="hidden"
//           whileInView="visible"
//           viewport={{ once: true }}
//           className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
//         >
//           {stats.map((stat, index) => (
//             <motion.div
//               key={stat.label}
//               variants={itemVariants}
//               whileHover={{ scale: 1.05 }}
//               className="text-center p-8 rounded-2xl bg-background/80 backdrop-blur-sm border border-border/50 shadow-lg"
//             >
//               <motion.div
//                 whileHover={{ rotate: 360 }}
//                 transition={{ duration: 0.6 }}
//                 className="w-16 h-16 mx-auto mb-6 bg-primary/10 rounded-2xl flex items-center justify-center"
//               >
//                 <stat.icon className="w-8 h-8 text-primary" />
//               </motion.div>

//               <div className="text-4xl font-bold text-primary mb-2">
//                 <AnimatedCounter from={0} to={stat.number} duration={2} delay={index * 0.2} />
//                 {stat.suffix}
//               </div>

//               <h3 className="text-xl font-semibold mb-2">{stat.label}</h3>
//               <p className="text-muted-foreground">{stat.description}</p>
//             </motion.div>
//           ))}
//         </motion.div>
//       </div>
//     </section>
//   )
// }



"use client"

import { motion, Variants } from "framer-motion"
import { TrendingUp, Users, Building2, Award } from "lucide-react"
import { AnimatedCounter } from "@/components/animated-counter"

const stats = [
  {
    icon: TrendingUp,
    number: 98,
    suffix: "%",
    label: "Success Rate",
    description: "Job placement success",
  },
  {
    icon: Users,
    number: 500000,
    suffix: "+",
    label: "Active Users",
    description: "Professionals trust us",
  },
  {
    icon: Building2,
    number: 15000,
    suffix: "+",
    label: "Companies",
    description: "Partner with us",
  },
  {
    icon: Award,
    number: 50,
    suffix: "+",
    label: "Awards Won",
    description: "Industry recognition",
  },
]

export function StatsSection() {
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut" as const,
      },
    },
  }

  return (
    <section className="py-20 bg-primary/5">
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              className="text-center p-8 rounded-2xl bg-background/80 backdrop-blur-sm border border-border/50 shadow-lg"
            >
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
                className="w-16 h-16 mx-auto mb-6 bg-primary/10 rounded-2xl flex items-center justify-center"
              >
                <stat.icon className="w-8 h-8 text-primary" />
              </motion.div>

              <div className="text-4xl font-bold text-primary mb-2">
                <AnimatedCounter from={0} to={stat.number} duration={2} delay={index * 0.2} />
                {stat.suffix}
              </div>

              <h3 className="text-xl font-semibold mb-2">{stat.label}</h3>
              <p className="text-muted-foreground">{stat.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
