"use client"

import { motion } from "framer-motion"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, MapPin, TrendingUp } from "lucide-react"
import { AnimatedCounter } from "@/components/animated-counter"
import { getAllJobs } from "@/lib/job-data"

type Theme = "blue" | "green" | "dark"

interface HeroSectionProps {
  onSearch?: (query: string, location: string) => void
  isSearchActive?: boolean
}

export function HeroSection({ onSearch, isSearchActive = false }: HeroSectionProps) {
  const [currentTheme, setCurrentTheme] = useState<Theme>("blue")
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [searchQuery, setSearchQuery] = useState("")
  const [locationQuery, setLocationQuery] = useState("")
  const [allJobs, setAllJobs] = useState<any[]>([])

  // Load all jobs for search
  useEffect(() => {
    const loadJobs = async () => {
      try {
        const jobs = await getAllJobs()
        setAllJobs(jobs)
      } catch (error) {
        console.error('Failed to load jobs:', error)
      }
    }
    loadJobs()
  }, [])

  // Professional workplace images from Unsplash
  const backgroundImages = [
    {
      url: "https://images.unsplash.com/photo-1590650516494-0c8e4a4dd67e?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0",
      alt: "Professional women collaborating at office desk"
    },
    {
      url: "https://images.unsplash.com/photo-1556761175-b413da4baf72?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0",
      alt: "Modern office team meeting"
    },
    {
      url: "https://images.unsplash.com/photo-1521737604893-d14cc237f11d?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0",
      alt: "Diverse business team collaboration"
    },
    {
      url: "https://images.unsplash.com/photo-1552664730-d307ca884978?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0",
      alt: "Professional office workspace"
    }
  ]

  useEffect(() => {
    // Check for theme class on html element
    const checkTheme = () => {
      const html = document.documentElement
      if (html.classList.contains("theme-green")) {
        setCurrentTheme("green")
      } else if (html.classList.contains("theme-dark")) {
        setCurrentTheme("dark")
      } else {
        setCurrentTheme("blue")
      }
    }

    checkTheme()

    // Watch for theme changes
    const observer = new MutationObserver(checkTheme)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    })

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    // Rotate background images every 10 seconds
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % backgroundImages.length)
    }, 10000)

    return () => clearInterval(interval)
  }, [backgroundImages.length])

  // Handle search input changes
  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    if (value.trim() || locationQuery.trim()) {
      triggerSearch(value, locationQuery)
    }
  }

  const handleLocationChange = (value: string) => {
    setLocationQuery(value)
    if (searchQuery.trim() || value.trim()) {
      triggerSearch(searchQuery, value)
    }
  }

  const triggerSearch = (query: string, location: string) => {
    if (onSearch) {
      onSearch(query, location)
    }
  }

  const handleSearchSubmit = () => {
    if (searchQuery.trim() || locationQuery.trim()) {
      triggerSearch(searchQuery, locationQuery)
    } else {
      // Navigate to jobs page if no search query
      window.location.href = '/jobs'
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  // Theme-aware gradient colors
  const getGradientOverlay = () => {
    switch (currentTheme) {
      case "blue":
        return "from-blue-600/40 via-blue-500/60 to-blue-900/80"
      case "green":
        return "from-green-600/40 via-green-500/60 to-green-900/80"
      case "dark":
        return "from-gray-800/60 via-gray-700/80 to-gray-900/95"
      default:
        return "from-blue-600/40 via-blue-500/60 to-blue-900/80"
    }
  }

  // Theme-aware text colors
  const getTextColors = () => {
    switch (currentTheme) {
      case "blue":
        return {
          primary: "text-foreground",
          secondary: "text-muted-foreground",
          accent: "from-primary via-primary/80 to-primary/60"
        }
      case "green":
        return {
          primary: "text-foreground",
          secondary: "text-muted-foreground",
          accent: "from-primary via-primary/80 to-primary/60"
        }
      case "dark":
        return {
          primary: "text-foreground",
          secondary: "text-muted-foreground",
          accent: "from-primary via-primary/80 to-primary/60"
        }
      default:
        return {
          primary: "text-foreground",
          secondary: "text-muted-foreground",
          accent: "from-primary via-primary/80 to-primary/60"
        }
    }
  }

  const textColors = getTextColors()

  // Premium theme-aware background gradients
  const getThemeBackground = () => {
    switch (currentTheme) {
      case "blue":
        return "bg-gradient-to-br from-primary/30 via-background to-primary/10"
      case "green":
        return "bg-gradient-to-br from-primary/25 via-background to-primary/15"
      case "dark":
        return "bg-gradient-to-br from-primary/20 via-background to-primary/10"
      default:
        return "bg-gradient-to-br from-primary/30 via-background to-primary/10"
    }
  }

  const getPatternOverlay = () => {
    return "opacity-15 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px]"
  }

  const getShapeStyles = () => {
    return "opacity-20 bg-gradient-to-br from-primary/40 to-primary/20 blur-3xl"
  }

  const getSecondaryShapeStyles = () => {
    return "opacity-15 bg-gradient-to-tl from-primary/30 to-accent/25 blur-2xl rounded-3xl"
  }

  return (
    <section className="relative pt-32 pb-20 overflow-hidden min-h-[90vh]">
      {/* Professional Theme-Aware Background */}
      <div className="absolute inset-0 -z-20">
        {/* Base gradient background */}
        <div className={`absolute inset-0 ${getThemeBackground()}`} />

        {/* Sophisticated pattern overlay */}
        <div className={`absolute inset-0 ${getPatternOverlay()}`} />

        {/* Elegant geometric shapes */}
        <motion.div
          animate={{
            rotate: [0, 360],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 60,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          className={`absolute top-1/4 right-1/4 w-96 h-96 rounded-full ${getShapeStyles()}`}
        />

        <motion.div
          animate={{
            rotate: [360, 0],
            scale: [1.1, 1, 1.1],
          }}
          transition={{
            duration: 45,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          className={`absolute bottom-1/4 left-1/4 w-80 h-80 ${getSecondaryShapeStyles()}`}
        />
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          className="absolute top-20 right-10 w-72 h-72 bg-primary/5 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.1, 1, 1.1],
            rotate: [0, -5, 0],
          }}
          transition={{
            duration: 25,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          className="absolute bottom-20 left-10 w-96 h-96 bg-primary/3 rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto text-center"
        >
          <motion.div variants={itemVariants} className="mb-6">
            <motion.span
              animate={{
                scale: [1, 1.05, 1],
                boxShadow: [
                  "0 0 0 0 rgba(59, 130, 246, 0.4)",
                  "0 0 0 10px rgba(59, 130, 246, 0)",
                  "0 0 0 0 rgba(59, 130, 246, 0)"
                ]
              }}
              transition={{
                duration: 2,
                repeat: Number.POSITIVE_INFINITY,
                ease: "easeInOut"
              }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-card/95 backdrop-blur-sm border border-border/50 text-primary text-sm font-medium shadow-lg"
            >
              <TrendingUp className="w-4 h-4 mr-2" />
              #1 Job Platform
            </motion.span>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className={`text-5xl md:text-7xl font-bold mb-6 ${textColors.primary}`}
          >
            <motion.span
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              Find Your Dream
            </motion.span>
            <br />
            <motion.span
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className={`bg-gradient-to-r ${textColors.accent} bg-clip-text text-transparent`}
            >
              Career Today
            </motion.span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className={`text-xl ${textColors.secondary} mb-12 max-w-2xl mx-auto leading-relaxed`}
          >
            Connect with top companies and discover opportunities that match your skills, passion, and career goals.
            Your next big opportunity is just a search away.
          </motion.p>

          <motion.div
            variants={itemVariants}
            whileHover={{ y: -5 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="bg-background/95 backdrop-blur-md border border-border/30 rounded-2xl p-6 mb-12 shadow-2xl hover:shadow-3xl transition-shadow duration-300"
          >
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="Job title, keywords, or company"
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-12 h-14 text-lg border-0 bg-muted/50 focus:bg-background transition-colors duration-200"
                />
              </div>
              <div className="flex-1 relative">
                <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="City, state, or remote"
                  value={locationQuery}
                  onChange={(e) => handleLocationChange(e.target.value)}
                  className="pl-12 h-14 text-lg border-0 bg-muted/50 focus:bg-background transition-colors duration-200"
                />
              </div>
              <Button
                size="lg"
                className="h-14 px-8 text-lg font-semibold"
                onClick={handleSearchSubmit}
              >
                <Search className="w-5 h-5 mr-2" />
                Search Jobs
              </Button>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            {[
              { number: 50000, label: "Active Jobs", suffix: "+" },
              { number: 25000, label: "Companies", suffix: "+" },
              { number: 1000000, label: "Success Stories", suffix: "+" },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                whileHover={{ scale: 1.05 }}
                className="text-center p-6 rounded-xl bg-background/90 backdrop-blur-md border border-border/30 shadow-lg"
              >
                <div className="text-3xl font-bold text-primary mb-2">
                  <AnimatedCounter from={0} to={stat.number} duration={2} delay={index * 0.2} />
                  {stat.suffix}
                </div>
                <div className="text-muted-foreground font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>

          {/* Background Image Indicators */}
          <motion.div
            variants={itemVariants}
            className="flex justify-center space-x-2 mt-8"
          >
            {backgroundImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentImageIndex
                    ? 'bg-white shadow-lg scale-125'
                    : 'bg-white/50 hover:bg-white/75'
                }`}
                aria-label={`Switch to background image ${index + 1}`}
              />
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
