'use client'

import { useState, useEffect } from 'react'
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"

export function HeroSectionDebug() {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Debug info */}
      <div className="absolute top-4 left-4 z-50 bg-black/80 text-white p-4 rounded text-sm">
        <div>Image loaded: {imageLoaded ? '✅' : '❌'}</div>
        <div>Image error: {imageError ? '❌' : '✅'}</div>
        <div>Path: /images/hero/hero-main.jpg</div>
      </div>

      {/* Single background image for testing */}
      <div className="absolute inset-0 -z-20">
        {/* Fallback background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-900" />
        
        {/* Test image */}
        <img
          src="/images/hero/hero-main.jpg"
          alt="Hero background"
          className="absolute inset-0 w-full h-full object-cover"
          onLoad={() => {
            console.log('✅ Image loaded successfully')
            setImageLoaded(true)
          }}
          onError={(e) => {
            console.error('❌ Image failed to load', e)
            setImageError(true)
          }}
        />
      </div>

      {/* Dark overlay */}
      <div className="absolute inset-0 bg-black/40 -z-10" />

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
        <motion.h1 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-4xl md:text-6xl font-bold mb-6"
        >
          Find Your Dream Job
        </motion.h1>
        
        <motion.p 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto"
        >
          Connect with top employers and discover opportunities that match your skills and aspirations.
        </motion.p>
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
            Find Jobs
          </Button>
          <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3">
            Post a Job
          </Button>
        </motion.div>

        {/* Test all images */}
        <div className="mt-8 flex justify-center space-x-2">
          <img src="/images/hero/hero-main.jpg" alt="Test 1" className="w-8 h-8 object-cover rounded" />
          <img src="/images/hero/team-collaboration.jpg" alt="Test 2" className="w-8 h-8 object-cover rounded" />
          <img src="/images/hero/office-meeting.jpg" alt="Test 3" className="w-8 h-8 object-cover rounded" />
          <img src="/images/hero/office-background.jpg" alt="Test 4" className="w-8 h-8 object-cover rounded" />
        </div>
      </div>
    </section>
  )
}
