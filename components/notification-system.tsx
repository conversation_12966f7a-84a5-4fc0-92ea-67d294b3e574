"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bell, X, Briefcase, MessageSquare, TrendingUp, CheckCircle, AlertCircle } from "lucide-react"

interface Notification {
  id: string
  type: "job_match" | "application_update" | "message" | "achievement" | "alert"
  title: string
  message: string
  timestamp: Date
  read: boolean
  actionUrl?: string
  priority: "low" | "medium" | "high"
}

const sampleNotifications: Notification[] = [
  {
    id: "1",
    type: "job_match",
    title: "New Job Match!",
    message: "Senior React Developer at TechCorp (95% match)",
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    priority: "high",
  },
  {
    id: "2",
    type: "application_update",
    title: "Application Update",
    message: "Your application for Product Manager at StartupXYZ has been viewed",
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    read: false,
    priority: "medium",
  },
  {
    id: "3",
    type: "message",
    title: "New Message",
    message: "Recruiter from InnovateLab sent you a message",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    read: true,
    priority: "medium",
  },
  {
    id: "4",
    type: "achievement",
    title: "Profile Milestone!",
    message: "Your profile has been viewed 100+ times this week",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    read: true,
    priority: "low",
  },
]

export function NotificationSystem() {
  const [notifications, setNotifications] = useState<Notification[]>(sampleNotifications)
  const [showPanel, setShowPanel] = useState(false)
  const [newNotificationCount, setNewNotificationCount] = useState(0)

  useEffect(() => {
    const unreadCount = notifications.filter((n) => !n.read).length
    setNewNotificationCount(unreadCount)
  }, [notifications])

  useEffect(() => {
    // Simulate new notifications
    const interval = setInterval(() => {
      if (Math.random() > 0.8) {
        // 20% chance every 10 seconds
        const newNotification: Notification = {
          id: Date.now().toString(),
          type: "job_match",
          title: "New Job Alert!",
          message: `${["Frontend Developer", "Product Manager", "UX Designer"][Math.floor(Math.random() * 3)]} position matches your profile`,
          timestamp: new Date(),
          read: false,
          priority: "medium",
        }
        setNotifications((prev) => [newNotification, ...prev])
      }
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const markAsRead = (id: string) => {
    setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)))
  }

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })))
  }

  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id))
  }

  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "job_match":
        return <Briefcase className="w-4 h-4" />
      case "application_update":
        return <CheckCircle className="w-4 h-4" />
      case "message":
        return <MessageSquare className="w-4 h-4" />
      case "achievement":
        return <TrendingUp className="w-4 h-4" />
      case "alert":
        return <AlertCircle className="w-4 h-4" />
      default:
        return <Bell className="w-4 h-4" />
    }
  }

  const getNotificationColor = (type: Notification["type"]) => {
    switch (type) {
      case "job_match":
        return "text-blue-500"
      case "application_update":
        return "text-green-500"
      case "message":
        return "text-purple-500"
      case "achievement":
        return "text-orange-500"
      case "alert":
        return "text-red-500"
      default:
        return "text-gray-500"
    }
  }

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return "Just now"
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  return (
    <>
      {/* Notification Panel */}
      <AnimatePresence>
        {showPanel && (
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            className="fixed top-0 right-0 h-full w-96 z-50 bg-background/95 backdrop-blur-lg border-l border-border/50 shadow-2xl"
          >
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-border/50">
                <div>
                  <h2 className="text-xl font-bold">Notifications</h2>
                  <p className="text-sm text-muted-foreground">{newNotificationCount} unread</p>
                </div>
                <div className="flex items-center space-x-2">
                  {newNotificationCount > 0 && (
                    <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                      Mark all read
                    </Button>
                  )}
                  <Button variant="ghost" size="icon" onClick={() => setShowPanel(false)}>
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Notifications List */}
              <div className="flex-1 overflow-y-auto">
                <AnimatePresence>
                  {notifications.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full text-center p-6">
                      <Bell className="w-12 h-12 text-muted-foreground/50 mb-4" />
                      <h3 className="font-semibold mb-2">No notifications</h3>
                      <p className="text-sm text-muted-foreground">
                        You're all caught up! New notifications will appear here.
                      </p>
                    </div>
                  ) : (
                    <div className="p-4 space-y-3">
                      {notifications.map((notification, index) => (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, x: 300 }}
                          transition={{ delay: index * 0.05 }}
                          onClick={() => markAsRead(notification.id)}
                          className="cursor-pointer"
                        >
                          <Card
                            className={`hover:shadow-md transition-all duration-200 ${
                              !notification.read ? "border-primary/50 bg-primary/5" : "border-border/50"
                            }`}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-start space-x-3">
                                <div className={`p-2 rounded-full bg-muted ${getNotificationColor(notification.type)}`}>
                                  {getNotificationIcon(notification.type)}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center justify-between mb-1">
                                    <h4 className="font-semibold text-sm truncate">{notification.title}</h4>
                                    <div className="flex items-center space-x-2">
                                      {!notification.read && <div className="w-2 h-2 bg-primary rounded-full" />}
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          removeNotification(notification.id)
                                        }}
                                        className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                      >
                                        <X className="w-3 h-3" />
                                      </Button>
                                    </div>
                                  </div>
                                  <p className="text-sm text-muted-foreground mb-2">{notification.message}</p>
                                  <div className="flex items-center justify-between">
                                    <span className="text-xs text-muted-foreground">
                                      {formatTimeAgo(notification.timestamp)}
                                    </span>
                                    <Badge
                                      variant={notification.priority === "high" ? "destructive" : "secondary"}
                                      className="text-xs"
                                    >
                                      {notification.priority}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Notification Bell Button */}
      <motion.div className="fixed top-6 right-6 z-40" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          variant="outline"
          size="icon"
          onClick={() => setShowPanel(!showPanel)}
          className="relative bg-background/80 backdrop-blur-sm border-border/50 hover:bg-background shadow-lg"
        >
          <Bell className="w-5 h-5" />
          <AnimatePresence>
            {newNotificationCount > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold"
              >
                {newNotificationCount > 9 ? "9+" : newNotificationCount}
              </motion.div>
            )}
          </AnimatePresence>
        </Button>
      </motion.div>

      {/* Overlay */}
      <AnimatePresence>
        {showPanel && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowPanel(false)}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          />
        )}
      </AnimatePresence>
    </>
  )
}
