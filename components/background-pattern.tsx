"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"

export function BackgroundPattern() {
  const [currentTheme, setCurrentTheme] = useState("blue")

  useEffect(() => {
    // Check for theme class on html element
    const checkTheme = () => {
      const html = document.documentElement
      if (html.classList.contains("theme-green")) {
        setCurrentTheme("green")
      } else if (html.classList.contains("theme-dark")) {
        setCurrentTheme("dark")
      } else {
        setCurrentTheme("blue")
      }
    }

    checkTheme()

    // Watch for theme changes
    const observer = new MutationObserver(checkTheme)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    })

    return () => observer.disconnect()
  }, [])

  const getThemeColors = () => {
    switch (currentTheme) {
      case "green":
        return {
          primary: "hsl(142, 65%, 55%)",
          primaryLight: "hsl(142, 65%, 65%)",
          primaryDark: "hsl(142, 65%, 45%)",
          accent: "hsl(150, 25%, 18%)",
          gradient1: "hsl(150, 25%, 8%)",
          gradient2: "hsl(150, 30%, 10%)",
          gradient3: "hsl(142, 65%, 55%)",
          gradient4: "hsl(160, 50%, 40%)",
          dots: "hsl(142, 65%, 55%)",
          background: "hsl(150, 25%, 8%)",
          surface: "hsl(150, 30%, 10%)",
          overlay: "hsl(142, 65%, 55%)",
        }
      case "dark":
        return {
          primary: "hsl(217, 91%, 60%)",
          primaryLight: "hsl(217, 91%, 70%)",
          primaryDark: "hsl(217, 91%, 50%)",
          accent: "hsl(215, 28%, 17%)",
          gradient1: "hsl(224, 71%, 4%)",
          gradient2: "hsl(215, 28%, 17%)",
          gradient3: "hsl(217, 91%, 60%)",
          gradient4: "hsl(200, 80%, 50%)",
          dots: "hsl(217, 91%, 60%)",
          background: "hsl(224, 71%, 4%)",
          surface: "hsl(224, 71%, 6%)",
          overlay: "hsl(217, 91%, 60%)",
        }
      default: // blue
        return {
          primary: "hsl(217, 91%, 65%)",
          primaryLight: "hsl(217, 91%, 75%)",
          primaryDark: "hsl(217, 91%, 55%)",
          accent: "hsl(220, 30%, 15%)",
          gradient1: "hsl(220, 30%, 6%)",
          gradient2: "hsl(220, 35%, 8%)",
          gradient3: "hsl(217, 91%, 65%)",
          gradient4: "hsl(200, 100%, 60%)",
          dots: "hsl(217, 91%, 65%)",
          background: "hsl(220, 30%, 6%)",
          surface: "hsl(220, 35%, 8%)",
          overlay: "hsl(217, 91%, 65%)",
        }
    }
  }

  const colors = getThemeColors()

  return (
    <div className="fixed inset-0 -z-20 overflow-hidden">
      {/* Immersive Theme Background */}
      <div
        className="absolute inset-0"
        style={{
          background: `
            radial-gradient(circle at 20% 20%, ${colors.gradient3}15 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, ${colors.gradient4}10 0%, transparent 50%),
            linear-gradient(135deg, ${colors.background} 0%, ${colors.surface} 30%, ${colors.gradient2} 70%, ${colors.background} 100%)
          `,
        }}
      />

      {/* Enhanced Grid Pattern with Theme Colors */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            linear-gradient(to right, ${colors.primary}40 1px, transparent 1px),
            linear-gradient(to bottom, ${colors.primary}40 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, ${colors.overlay}15 2px, transparent 2px),
            radial-gradient(circle at 75% 75%, ${colors.gradient3}10 1px, transparent 1px)
          `,
          backgroundSize: "80px 80px, 80px 80px, 160px 160px, 120px 120px",
          backgroundPosition: "0 0, 0 0, 0 0, 40px 40px",
        }}
      />

      {/* Theme-specific ambient lighting */}
      <div
        className="absolute inset-0 opacity-30"
        style={{
          background: `
            radial-gradient(ellipse at top left, ${colors.primary}20 0%, transparent 50%),
            radial-gradient(ellipse at bottom right, ${colors.gradient3}15 0%, transparent 50%),
            radial-gradient(ellipse at center, ${colors.gradient4}08 0%, transparent 70%)
          `,
        }}
      />

      {/* Theme-specific geometric patterns */}
      {currentTheme === "green" && (
        <>
          {/* Rich Forest Atmosphere */}
          <motion.div
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 50,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute top-1/6 left-1/6 w-[500px] h-[500px] opacity-15"
            style={{
              background: `conic-gradient(from 0deg, ${colors.primary}30, ${colors.gradient3}25, ${colors.gradient4}20, ${colors.primary}15, transparent)`,
              borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
              filter: "blur(2px)",
            }}
          />
          <motion.div
            animate={{
              rotate: [360, 0],
              scale: [1.2, 1, 1.2],
            }}
            transition={{
              duration: 40,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute bottom-1/6 right-1/6 w-96 h-96 opacity-12"
            style={{
              background: `radial-gradient(ellipse at 40% 60%, ${colors.gradient3}35 0%, ${colors.primary}25 30%, ${colors.gradient4}15 60%, transparent 100%)`,
              borderRadius: "40% 60% 60% 40% / 40% 40% 60% 60%",
              filter: "blur(1.5px)",
            }}
          />
          {/* Forest depth layers */}
          <div
            className="absolute inset-0 opacity-8"
            style={{
              backgroundImage: `
                radial-gradient(circle at 20% 30%, ${colors.primary}25 3px, transparent 3px),
                radial-gradient(circle at 70% 60%, ${colors.gradient3}20 2px, transparent 2px),
                radial-gradient(circle at 40% 80%, ${colors.gradient4}15 1px, transparent 1px)
              `,
              backgroundSize: "120px 120px, 180px 180px, 240px 240px",
            }}
          />
        </>
      )}

      {currentTheme === "dark" && (
        <>
          {/* Sharp, tech-focused patterns for dark theme */}
          <motion.div
            animate={{
              rotate: [0, 180, 360],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 40,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute top-1/3 left-1/3 w-72 h-72 opacity-20"
            style={{
              background: `conic-gradient(from 0deg, ${colors.gradient1}40, ${colors.gradient2}20, ${colors.gradient1}40)`,
              clipPath: "polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)",
            }}
          />
          <motion.div
            animate={{
              rotate: [360, 180, 0],
              scale: [1.1, 1, 1.1],
            }}
            transition={{
              duration: 35,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute bottom-1/3 right-1/3 w-64 h-64 opacity-15"
            style={{
              background: `linear-gradient(45deg, ${colors.gradient1}30, ${colors.gradient2}15, transparent)`,
              clipPath: "polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%)",
            }}
          />
        </>
      )}

      {currentTheme === "blue" && (
        <>
          {/* Deep Ocean Atmosphere */}
          <motion.div
            animate={{
              rotate: [0, 360],
              scale: [1, 1.3, 1],
            }}
            transition={{
              duration: 45,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute top-1/6 left-1/6 w-[520px] h-[520px] rounded-full opacity-18"
            style={{
              background: `conic-gradient(from 45deg, ${colors.primary}35, ${colors.gradient3}30, ${colors.gradient4}25, ${colors.primary}20, transparent)`,
              filter: "blur(3px)",
            }}
          />
          <motion.div
            animate={{
              rotate: [360, 0],
              scale: [1.3, 1, 1.3],
            }}
            transition={{
              duration: 38,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute bottom-1/6 right-1/6 w-[400px] h-[400px] rounded-full opacity-15"
            style={{
              background: `radial-gradient(ellipse at 50% 50%, ${colors.gradient3}40 0%, ${colors.primary}30 30%, ${colors.gradient4}20 60%, transparent 100%)`,
              filter: "blur(2px)",
            }}
          />
          {/* Ocean depth layers */}
          <div
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `
                radial-gradient(circle at 15% 25%, ${colors.primary}30 4px, transparent 4px),
                radial-gradient(circle at 75% 65%, ${colors.gradient3}25 3px, transparent 3px),
                radial-gradient(circle at 45% 85%, ${colors.gradient4}20 2px, transparent 2px)
              `,
              backgroundSize: "140px 140px, 200px 200px, 280px 280px",
            }}
          />
        </>
      )}

      {/* Animated floating dots */}
      <div className="absolute inset-0">
        {Array.from({ length: currentTheme === "dark" ? 30 : 50 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full"
            style={{
              width: currentTheme === "dark" ? "2px" : "1px",
              height: currentTheme === "dark" ? "2px" : "1px",
              backgroundColor: colors.dots,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: currentTheme === "dark" ? [0.3, 0.8, 0.3] : [0.2, 0.8, 0.2],
              scale: currentTheme === "dark" ? [1, 2, 1] : [1, 1.5, 1],
            }}
            transition={{
              duration: currentTheme === "dark" ? 4 + Math.random() * 2 : 3 + Math.random() * 2,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Theme-specific overlay gradients */}
      <motion.div
        animate={{
          x: [0, 100, 0],
          y: [0, -50, 0],
        }}
        transition={{
          duration: currentTheme === "dark" ? 25 : 20,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
        className="absolute top-1/4 left-1/4 w-96 h-96 blur-3xl"
        style={{
          background: `linear-gradient(45deg, ${colors.gradient1}15, transparent)`,
        }}
      />

      <motion.div
        animate={{
          x: [0, -100, 0],
          y: [0, 50, 0],
        }}
        transition={{
          duration: currentTheme === "dark" ? 30 : 25,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
        className="absolute bottom-1/4 right-1/4 w-96 h-96 blur-3xl"
        style={{
          background: `linear-gradient(-45deg, ${colors.gradient2}10, transparent)`,
        }}
      />
    </div>
  )
}
