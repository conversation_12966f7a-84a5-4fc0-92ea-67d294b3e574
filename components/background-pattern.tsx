"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"

export function BackgroundPattern() {
  const [currentTheme, setCurrentTheme] = useState("blue")

  useEffect(() => {
    // Check for theme class on html element
    const checkTheme = () => {
      const html = document.documentElement
      if (html.classList.contains("theme-green")) {
        setCurrentTheme("green")
      } else if (html.classList.contains("theme-dark")) {
        setCurrentTheme("dark")
      } else {
        setCurrentTheme("blue")
      }
    }

    checkTheme()

    // Watch for theme changes
    const observer = new MutationObserver(checkTheme)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    })

    return () => observer.disconnect()
  }, [])

  const getThemeColors = () => {
    switch (currentTheme) {
      case "green":
        return {
          primary: "hsl(142, 76%, 36%)",
          primaryLight: "hsl(142, 76%, 46%)",
          primaryDark: "hsl(142, 76%, 26%)",
          accent: "hsl(120, 60%, 50%)",
          gradient1: "hsl(142, 76%, 36%)",
          gradient2: "hsl(120, 60%, 40%)",
          dots: "hsl(142, 76%, 36%)",
        }
      case "dark":
        return {
          primary: "hsl(210, 40%, 98%)",
          primaryLight: "hsl(210, 40%, 88%)",
          primaryDark: "hsl(210, 40%, 78%)",
          accent: "hsl(217, 32%, 17%)",
          gradient1: "hsl(217, 32%, 17%)",
          gradient2: "hsl(215, 28%, 25%)",
          dots: "hsl(210, 40%, 98%)",
        }
      default: // blue
        return {
          primary: "hsl(221, 83%, 53%)",
          primaryLight: "hsl(221, 83%, 63%)",
          primaryDark: "hsl(221, 83%, 43%)",
          accent: "hsl(210, 100%, 60%)",
          gradient1: "hsl(221, 83%, 53%)",
          gradient2: "hsl(210, 100%, 60%)",
          dots: "hsl(221, 83%, 53%)",
        }
    }
  }

  const colors = getThemeColors()

  return (
    <div className="fixed inset-0 -z-20 overflow-hidden">
      {/* Animated Grid Pattern */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `linear-gradient(to right, ${colors.primary}20 1px, transparent 1px), linear-gradient(to bottom, ${colors.primary}20 1px, transparent 1px)`,
          backgroundSize: "4rem 4rem",
        }}
      />

      {/* Theme-specific geometric patterns */}
      {currentTheme === "green" && (
        <>
          {/* Organic flowing patterns for green theme */}
          <motion.div
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 30,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute top-1/4 left-1/4 w-96 h-96 opacity-10"
            style={{
              background: `radial-gradient(circle, ${colors.gradient1}40 0%, ${colors.gradient2}20 50%, transparent 100%)`,
              borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
            }}
          />
          <motion.div
            animate={{
              rotate: [360, 0],
              scale: [1.2, 1, 1.2],
            }}
            transition={{
              duration: 25,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute bottom-1/4 right-1/4 w-80 h-80 opacity-15"
            style={{
              background: `radial-gradient(circle, ${colors.gradient2}30 0%, ${colors.gradient1}15 50%, transparent 100%)`,
              borderRadius: "30% 70% 70% 30% / 30% 30% 70% 70%",
            }}
          />
        </>
      )}

      {currentTheme === "dark" && (
        <>
          {/* Sharp, tech-focused patterns for dark theme */}
          <motion.div
            animate={{
              rotate: [0, 180, 360],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 40,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute top-1/3 left-1/3 w-72 h-72 opacity-20"
            style={{
              background: `conic-gradient(from 0deg, ${colors.gradient1}40, ${colors.gradient2}20, ${colors.gradient1}40)`,
              clipPath: "polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)",
            }}
          />
          <motion.div
            animate={{
              rotate: [360, 180, 0],
              scale: [1.1, 1, 1.1],
            }}
            transition={{
              duration: 35,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute bottom-1/3 right-1/3 w-64 h-64 opacity-15"
            style={{
              background: `linear-gradient(45deg, ${colors.gradient1}30, ${colors.gradient2}15, transparent)`,
              clipPath: "polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%)",
            }}
          />
        </>
      )}

      {currentTheme === "blue" && (
        <>
          {/* Professional circular patterns for blue theme */}
          <motion.div
            animate={{
              rotate: [0, 360],
              scale: [1, 1.15, 1],
            }}
            transition={{
              duration: 35,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full opacity-10"
            style={{
              background: `radial-gradient(circle, ${colors.gradient1}30 0%, ${colors.gradient2}15 50%, transparent 100%)`,
            }}
          />
          <motion.div
            animate={{
              rotate: [360, 0],
              scale: [1.15, 1, 1.15],
            }}
            transition={{
              duration: 30,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
            className="absolute bottom-1/4 right-1/4 w-80 h-80 rounded-full opacity-15"
            style={{
              background: `radial-gradient(circle, ${colors.gradient2}25 0%, ${colors.gradient1}10 50%, transparent 100%)`,
            }}
          />
        </>
      )}

      {/* Animated floating dots */}
      <div className="absolute inset-0">
        {Array.from({ length: currentTheme === "dark" ? 30 : 50 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full"
            style={{
              width: currentTheme === "dark" ? "2px" : "1px",
              height: currentTheme === "dark" ? "2px" : "1px",
              backgroundColor: colors.dots,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: currentTheme === "dark" ? [0.3, 0.8, 0.3] : [0.2, 0.8, 0.2],
              scale: currentTheme === "dark" ? [1, 2, 1] : [1, 1.5, 1],
            }}
            transition={{
              duration: currentTheme === "dark" ? 4 + Math.random() * 2 : 3 + Math.random() * 2,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Theme-specific overlay gradients */}
      <motion.div
        animate={{
          x: [0, 100, 0],
          y: [0, -50, 0],
        }}
        transition={{
          duration: currentTheme === "dark" ? 25 : 20,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
        className="absolute top-1/4 left-1/4 w-96 h-96 blur-3xl"
        style={{
          background: `linear-gradient(45deg, ${colors.gradient1}15, transparent)`,
        }}
      />

      <motion.div
        animate={{
          x: [0, -100, 0],
          y: [0, 50, 0],
        }}
        transition={{
          duration: currentTheme === "dark" ? 30 : 25,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
        className="absolute bottom-1/4 right-1/4 w-96 h-96 blur-3xl"
        style={{
          background: `linear-gradient(-45deg, ${colors.gradient2}10, transparent)`,
        }}
      />
    </div>
  )
}
