"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { Check, X, Shield, ShieldCheck, ShieldAlert, ShieldX } from 'lucide-react'
import { cn } from '@/lib/utils'
import { PasswordStrength } from '@/lib/utils'

interface PasswordStrengthIndicatorProps {
  password: string
  strength: PasswordStrength
  className?: string
}

export function PasswordStrengthIndicator({ 
  password, 
  strength, 
  className 
}: PasswordStrengthIndicatorProps) {
  const getStrengthColor = (score: number) => {
    switch (score) {
      case 0:
      case 1:
        return 'text-red-500'
      case 2:
        return 'text-orange-500'
      case 3:
        return 'text-yellow-500'
      case 4:
        return 'text-green-500'
      default:
        return 'text-gray-400'
    }
  }

  const getStrengthText = (score: number) => {
    switch (score) {
      case 0:
        return 'Very Weak'
      case 1:
        return 'Weak'
      case 2:
        return 'Fair'
      case 3:
        return 'Good'
      case 4:
        return 'Strong'
      default:
        return 'Enter password'
    }
  }

  const getStrengthIcon = (score: number) => {
    switch (score) {
      case 0:
      case 1:
        return ShieldX
      case 2:
        return ShieldAlert
      case 3:
        return Shield
      case 4:
        return ShieldCheck
      default:
        return Shield
    }
  }

  const StrengthIcon = getStrengthIcon(strength.score)

  if (!password) {
    return null
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Strength Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-muted-foreground">
            Password Strength
          </span>
          <div className="flex items-center space-x-1">
            <StrengthIcon className={cn('w-4 h-4', getStrengthColor(strength.score))} />
            <span className={cn('text-sm font-medium', getStrengthColor(strength.score))}>
              {getStrengthText(strength.score)}
            </span>
          </div>
        </div>
        
        <div className="flex space-x-1">
          {[0, 1, 2, 3, 4].map((index) => (
            <motion.div
              key={index}
              className={cn(
                'h-2 flex-1 rounded-full transition-colors duration-300',
                index < strength.score
                  ? strength.score <= 1
                    ? 'bg-red-500'
                    : strength.score === 2
                    ? 'bg-orange-500'
                    : strength.score === 3
                    ? 'bg-yellow-500'
                    : 'bg-green-500'
                  : 'bg-muted'
              )}
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ delay: index * 0.1 }}
            />
          ))}
        </div>
      </div>

      {/* Requirements List */}
      {strength.feedback.length > 0 && (
        <div className="space-y-1">
          <span className="text-sm font-medium text-muted-foreground">
            Requirements:
          </span>
          <div className="space-y-1">
            {[
              { text: 'At least 8 characters', check: password.length >= 8 },
              { text: 'One uppercase letter', check: /[A-Z]/.test(password) },
              { text: 'One lowercase letter', check: /[a-z]/.test(password) },
              { text: 'One number', check: /\d/.test(password) },
              { text: 'One special character', check: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password) }
            ].map((requirement, index) => (
              <motion.div
                key={index}
                className="flex items-center space-x-2"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                {requirement.check ? (
                  <Check className="w-3 h-3 text-green-500" />
                ) : (
                  <X className="w-3 h-3 text-red-500" />
                )}
                <span className={cn(
                  'text-xs',
                  requirement.check ? 'text-green-600' : 'text-muted-foreground'
                )}>
                  {requirement.text}
                </span>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
