'use client'

import { LoadingSpinner, DotsSpinner } from './loading-spinner'
import { Card, CardContent } from '@/components/ui/card'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface PageLoaderProps {
  message?: string
  fullScreen?: boolean
  variant?: 'spinner' | 'dots' | 'pulse'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export function PageLoader({ 
  message = 'Loading...', 
  fullScreen = false,
  variant = 'spinner',
  size = 'lg',
  className
}: PageLoaderProps) {
  const LoaderComponent = () => {
    switch (variant) {
      case 'dots':
        return <DotsSpinner />
      case 'pulse':
        return <LoadingSpinner size={size} />
      default:
        return <LoadingSpinner size={size} />
    }
  }

  if (fullScreen) {
    return (
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
      >
        <Card className="w-auto shadow-lg">
          <CardContent className="flex flex-col items-center space-y-4 p-8">
            <LoaderComponent />
            <p className="text-muted-foreground font-medium">{message}</p>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <div className={cn(
      'flex flex-col items-center justify-center py-12 space-y-4',
      className
    )}>
      <LoaderComponent />
      <p className="text-muted-foreground font-medium">{message}</p>
    </div>
  )
}

// Inline loader for smaller sections
export function InlineLoader({ 
  message,
  size = 'sm',
  className
}: Omit<PageLoaderProps, 'fullScreen' | 'variant'>) {
  return (
    <div className={cn(
      'flex items-center space-x-2 py-2',
      className
    )}>
      <LoadingSpinner size={size} />
      {message && (
        <span className="text-sm text-muted-foreground">{message}</span>
      )}
    </div>
  )
}

// Section loader with background
export function SectionLoader({ 
  message = 'Loading content...',
  className
}: Omit<PageLoaderProps, 'fullScreen' | 'variant' | 'size'>) {
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        'bg-muted/30 rounded-lg border border-border/50 p-8 flex flex-col items-center space-y-4',
        className
      )}
    >
      <LoadingSpinner size="lg" />
      <p className="text-muted-foreground font-medium">{message}</p>
    </motion.div>
  )
}

// Overlay loader for forms
export function FormLoader({ 
  message = 'Processing...',
  show = false
}: {
  message?: string
  show?: boolean
}) {
  if (!show) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10 rounded-lg"
    >
      <div className="flex flex-col items-center space-y-3">
        <LoadingSpinner size="md" />
        <p className="text-sm text-muted-foreground font-medium">{message}</p>
      </div>
    </motion.div>
  )
}
