// Error System - Comprehensive error handling components
// Re-export all error-related components for easy importing

export {
  ErrorBoundary,
  withErrorBoundary,
  useErrorHandler
} from '../error-boundary'

export {
  ErrorAlert,
  InlineError,
  ErrorList,
  ErrorToast,
  NetworkError,
  NotFoundError
} from './error-alert'

export {
  errorService,
  handleAsyncError,
  logReactError,
  type AppError,
  type ErrorLevel
} from '../../lib/error-service'

// Error handling hook for React components
import React, { useState, useCallback } from 'react'
import { errorService, type AppError } from '../../lib/error-service'

export function useErrorState() {
  const [error, setError] = useState<AppError | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const handleError = useCallback((error: Error | AppError, context?: string) => {
    const appError = errorService.logError(error, context)
    setError(appError)
    return appError
  }, [])

  const handleApiError = useCallback((response: Response, context?: string) => {
    const appError = errorService.handleApiError(response, context)
    setError(appError)
    return appError
  }, [])

  return {
    error,
    clearError,
    handleError,
    handleApiError,
    hasError: !!error,
    errorMessage: error ? errorService.getUserFriendlyMessage(error) : null
  }
}

// Form error handling hook
export function useFormErrors() {
  const [errors, setErrors] = useState<Record<string, string[]>>({})

  const setFieldError = useCallback((field: string, messages: string[]) => {
    setErrors(prev => ({
      ...prev,
      [field]: messages
    }))
  }, [])

  const clearFieldError = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[field]
      return newErrors
    })
  }, [])

  const clearAllErrors = useCallback(() => {
    setErrors({})
  }, [])

  const hasErrors = Object.keys(errors).length > 0
  const getFieldError = useCallback((field: string) => errors[field]?.[0], [errors])

  return {
    errors,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    hasErrors,
    getFieldError
  }
}

// Global error context
import React, { createContext, useContext, ReactNode } from 'react'

interface ErrorContextType {
  reportError: (error: Error | AppError, context?: string) => void
  clearErrors: () => void
  recentErrors: AppError[]
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined)

export function ErrorProvider({ children }: { children: ReactNode }) {
  const reportError = useCallback((error: Error | AppError, context?: string) => {
    errorService.logError(error, context)
  }, [])

  const clearErrors = useCallback(() => {
    errorService.clearErrors()
  }, [])

  const recentErrors = errorService.getRecentErrors()

  return (
    <ErrorContext.Provider value={{ reportError, clearErrors, recentErrors }}>
      {children}
    </ErrorContext.Provider>
  )
}

export function useErrorContext() {
  const context = useContext(ErrorContext)
  if (context === undefined) {
    throw new Error('useErrorContext must be used within an ErrorProvider')
  }
  return context
}
