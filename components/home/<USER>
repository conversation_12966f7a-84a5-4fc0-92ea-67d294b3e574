'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Zap, 
  Target, 
  Brain, 
  Shield, 
  TrendingUp, 
  Users, 
  Globe, 
  Award,
  Rocket,
  Star,
  CheckCircle,
  ArrowRight,
  Sparkles,
  BarChart3,
  MessageSquare,
  Calendar,
  Search,
  Filter,
  Bell,
  Lock
} from 'lucide-react'
import Link from 'next/link'

const premiumFeatures = [
  {
    icon: Brain,
    title: "AI-Powered Job Matching",
    description: "Advanced algorithms analyze your skills, experience, and preferences to deliver perfectly matched opportunities.",
    benefits: ["99% accuracy rate", "Real-time matching", "Continuous learning"],
    color: "from-blue-500 to-purple-600",
    badge: "AI-Powered"
  },
  {
    icon: Target,
    title: "Location-Based Discovery",
    description: "Hierarchical job filtering from local to international, with smart proximity-based recommendations.",
    benefits: ["Local to global reach", "Distance-based sorting", "Regional insights"],
    color: "from-green-500 to-teal-600",
    badge: "Location Smart"
  },
  {
    icon: TrendingUp,
    title: "Real-Time Market Analytics",
    description: "Live salary insights, industry trends, and demand forecasting to guide your career decisions.",
    benefits: ["Live market data", "Salary benchmarking", "Trend predictions"],
    color: "from-orange-500 to-red-600",
    badge: "Analytics"
  },
  {
    icon: Shield,
    title: "Enterprise-Grade Security",
    description: "Bank-level encryption and privacy controls to keep your professional data completely secure.",
    benefits: ["256-bit encryption", "GDPR compliant", "Privacy controls"],
    color: "from-gray-500 to-slate-600",
    badge: "Secure"
  },
  {
    icon: Users,
    title: "Professional Network",
    description: "Connect with industry leaders, mentors, and peers in your field for career advancement.",
    benefits: ["Industry connections", "Mentorship programs", "Networking events"],
    color: "from-pink-500 to-rose-600",
    badge: "Network"
  },
  {
    icon: Rocket,
    title: "Career Acceleration",
    description: "Personalized career paths, skill assessments, and development recommendations.",
    benefits: ["Career roadmaps", "Skill gap analysis", "Learning recommendations"],
    color: "from-indigo-500 to-blue-600",
    badge: "Growth"
  }
]

const platformStats = [
  { label: "Active Jobs", value: "50K+", icon: Search },
  { label: "Companies", value: "5K+", icon: Users },
  { label: "Success Rate", value: "94%", icon: Target },
  { label: "Countries", value: "120+", icon: Globe }
]

const testimonialHighlights = [
  {
    quote: "Found my dream job in 2 weeks with perfect location matching!",
    author: "Sarah M., Software Engineer",
    location: "Cape Town, South Africa"
  },
  {
    quote: "The AI recommendations were spot-on. Best hiring platform we've used.",
    author: "David K., HR Director",
    location: "Nairobi, Kenya"
  },
  {
    quote: "Location-based filtering helped me find local opportunities I never knew existed.",
    author: "Amina T., Marketing Manager",
    location: "Lilongwe, Malawi"
  }
]

export function PremiumFeaturesSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Sparkles className="w-6 h-6 text-primary" />
            <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
              Premium Platform
            </Badge>
          </div>
          
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-primary via-primary to-secondary bg-clip-text text-transparent">
            Enterprise-Grade Features
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Experience the most advanced job discovery platform with AI-powered matching, 
            location intelligence, and professional-grade tools that top companies pay premium for.
          </p>

          {/* Platform Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            {platformStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="card-premium text-center">
                  <CardContent className="p-6">
                    <stat.icon className="w-8 h-8 text-primary mx-auto mb-3" />
                    <div className="text-3xl font-bold text-primary mb-1">{stat.value}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Premium Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {premiumFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="card-premium h-full hover:shadow-xl transition-all duration-300 group">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {feature.badge}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl mb-2">{feature.title}</CardTitle>
                  <CardDescription className="text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Testimonial Highlights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mb-16"
        >
          <h3 className="text-3xl font-bold text-center mb-8">Trusted by Professionals Worldwide</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {testimonialHighlights.map((testimonial, index) => (
              <Card key={index} className="card-premium">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <blockquote className="text-muted-foreground mb-4 italic">
                    "{testimonial.quote}"
                  </blockquote>
                  <div>
                    <div className="font-semibold">{testimonial.author}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.location}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="text-center"
        >
          <Card className="card-premium max-w-4xl mx-auto bg-gradient-to-r from-primary/10 via-background to-secondary/10 border-primary/20">
            <CardContent className="p-12">
              <div className="flex items-center justify-center space-x-2 mb-6">
                <Award className="w-8 h-8 text-primary" />
                <h3 className="text-3xl font-bold">Ready to Experience Premium?</h3>
              </div>
              
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                Join thousands of professionals who've accelerated their careers with our 
                enterprise-grade platform. Start your journey today.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button asChild size="lg" className="button-premium text-lg px-8 py-3">
                  <Link href="/auth/register">
                    <Rocket className="w-5 h-5 mr-2" />
                    Get Started Free
                  </Link>
                </Button>
                
                <Button asChild variant="outline" size="lg" className="text-lg px-8 py-3">
                  <Link href="/jobs">
                    <Search className="w-5 h-5 mr-2" />
                    Explore Jobs
                  </Link>
                </Button>
              </div>

              <div className="flex items-center justify-center space-x-6 mt-8 text-sm text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Free to start</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Premium features included</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
