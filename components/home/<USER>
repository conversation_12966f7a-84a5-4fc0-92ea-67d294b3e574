// components\home\location-based-jobs-section.tsx
'use client'

import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useLocationStore, getLocationBasedJobs } from '@/stores/location-store'
import { getAllJobs, getApplicationStatusInfo } from '@/lib/job-data'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { JobApplicationModal } from '@/components/jobs/job-application-modal'
import {
  MapPin,
  Clock,
  Users,
  Target,
  Globe,
  Building,
  Zap,
  ArrowRight,
  DollarSign,
  Briefcase,
  Eye,
  Heart,
  Share2,
  Bookmark
} from 'lucide-react'
import Link from 'next/link'


export function LocationBasedJobsSection() {
  const { currentLocation, jobPreferences } = useLocationStore()
  const [jobs, setJobs] = useState<any[]>([])
  const [filteredJobs, setFilteredJobs] = useState<any[]>([])
  const [activeTab, setActiveTab] = useState('local')
  const [likedJobs, setLikedJobs] = useState<number[]>([])
  const [bookmarkedJobs, setBookmarkedJobs] = useState<number[]>([])
  const [applicationJob, setApplicationJob] = useState<any>(null)

  const toggleLike = (jobId: number) => {
    setLikedJobs((prev) => (prev.includes(jobId) ? prev.filter((id) => id !== jobId) : [...prev, jobId]))
  }

  const toggleBookmark = (jobId: number) => {
    setBookmarkedJobs((prev) => (prev.includes(jobId) ? prev.filter((id) => id !== jobId) : [...prev, jobId]))
  }

  const renderJobCard = (job: any) => {
    const applicationStatusInfo = getApplicationStatusInfo(job)

    return (
      <Card
        className={`glass hover:shadow-xl transition-all duration-300 ${
          job.featured ? "border-primary/50 bg-primary/5" : "border-border/50"
        } ${job.urgent ? "ring-2 ring-orange-500/20" : ""}`}
      >
        {job.urgent && (
          <div className="absolute -top-2 -right-2">
            <Badge className="bg-orange-500 text-white animate-pulse">
              <Zap className="w-3 h-3 mr-1" />
              Urgent
            </Badge>
          </div>
        )}

        <CardHeader className="pb-3 md:pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 md:space-x-4 flex-1 min-w-0">
              <Avatar className="w-10 h-10 md:w-12 md:h-12 flex-shrink-0">
                <AvatarImage src={job.company?.logo || "/placeholder.svg"} alt={job.company?.name || job.company} />
                <AvatarFallback>{(job.company?.name || job.company)[0]}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="text-base md:text-xl font-bold hover:text-primary transition-colors duration-200 cursor-pointer truncate">
                  {job.title}
                </h3>
                <p className="text-sm md:text-base text-muted-foreground truncate">{job.company?.name || job.company}</p>
                {/* Mobile: Show only essential info */}
                <div className="flex items-center space-x-2 md:space-x-4 text-xs md:text-sm text-muted-foreground mt-1">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{job.posted}</span>
                  </div>
                  {/* Hide some stats on mobile */}
                  <div className="hidden sm:flex items-center space-x-1">
                    <Users className="w-3 h-3" />
                    <span>{job.applicants} applicants</span>
                  </div>
                  <div className="hidden md:flex items-center space-x-1">
                    <Eye className="w-3 h-3" />
                    <span>{job.views} views</span>
                  </div>
                </div>
              </div>
            </div>
            {/* Action buttons - Simplified for mobile */}
            <div className="flex items-center space-x-1.5 md:space-x-2 flex-shrink-0">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => toggleLike(job.id)}
                className={`p-1.5 md:p-2 rounded-full transition-colors duration-200 ${
                  likedJobs.includes(job.id) ? "bg-red-100 text-red-500" : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Heart className={`w-3 h-3 md:w-4 md:h-4 ${likedJobs.includes(job.id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => toggleBookmark(job.id)}
                className={`p-1.5 md:p-2 rounded-full transition-colors duration-200 ${
                  bookmarkedJobs.includes(job.id)
                    ? "bg-primary/10 text-primary"
                    : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Bookmark className={`w-3 h-3 md:w-4 md:h-4 ${bookmarkedJobs.includes(job.id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-muted hover:bg-muted/80 transition-colors duration-200"
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-muted-foreground" />
              <span>{job.location}</span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-muted-foreground" />
              <span className="font-semibold text-primary">
                {job.salary ? `$${job.salary.min?.toLocaleString()} - $${job.salary.max?.toLocaleString()}` : 'Salary not specified'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Briefcase className="w-4 h-4 text-muted-foreground" />
              <span>{job.type}</span>
            </div>
          </div>

          {job.skills && job.skills.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {job.skills.slice(0, 4).map((skill: string) => (
                <Badge key={skill} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {job.skills.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{job.skills.length - 4} more
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t border-border/50">
            <div className="flex items-center space-x-4">
              <Badge
                variant="secondary"
                className={`text-xs ${
                  applicationStatusInfo.color === 'green' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  applicationStatusInfo.color === 'orange' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                  applicationStatusInfo.color === 'red' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}
              >
                {applicationStatusInfo.label}
              </Badge>
              {/* Hide featured badge on mobile to prevent layout issues */}
              {job.featured && (
                <Badge className="hidden sm:inline-flex bg-primary/10 text-primary border-primary/20">Featured</Badge>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setApplicationJob(job)}
              >
                Quick Apply
              </Button>
              <Button size="sm" asChild>
                <Link href={`/jobs/${job.id}`}>View Details</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  useEffect(() => {
    const allJobs = getAllJobs()
    setJobs(allJobs)
    
    if (currentLocation) {
      const locationBasedJobs = getLocationBasedJobs(allJobs, currentLocation, jobPreferences)
      setFilteredJobs(locationBasedJobs)
    } else {
      setFilteredJobs(allJobs.slice(0, 12))
    }
  }, [currentLocation, jobPreferences])

  const getJobsByLocationLevel = (level: string) => {
    if (!currentLocation) return jobs.slice(0, 6)

    switch (level) {
      case 'local':
        return filteredJobs.filter(job => 
          job.locationDetails?.city?.toLowerCase() === currentLocation.city.toLowerCase() ||
          job.location.toLowerCase().includes(currentLocation.city.toLowerCase())
        ).slice(0, 6)
      
      case 'regional':
        return filteredJobs.filter(job => 
          job.locationDetails?.region?.toLowerCase() === currentLocation.region.toLowerCase() ||
          job.location.toLowerCase().includes(currentLocation.region.toLowerCase())
        ).slice(0, 6)
      
      case 'national':
        return filteredJobs.filter(job => 
          job.locationDetails?.country?.toLowerCase() === currentLocation.country.toLowerCase() ||
          job.location.toLowerCase().includes(currentLocation.country.toLowerCase())
        ).slice(0, 6)
      
      case 'continental':
        return filteredJobs.filter(job => 
          job.locationDetails?.continent?.toLowerCase() === currentLocation.continent.toLowerCase()
        ).slice(0, 6)
      
      case 'international':
        return filteredJobs.filter(job => 
          job.locationDetails?.continent?.toLowerCase() !== currentLocation.continent.toLowerCase() ||
          job.workModel === 'Remote'
        ).slice(0, 6)
      
      default:
        return filteredJobs.slice(0, 6)
    }
  }

  const locationStats = {
    local: getJobsByLocationLevel('local').length,
    regional: getJobsByLocationLevel('regional').length,
    national: getJobsByLocationLevel('national').length,
    continental: getJobsByLocationLevel('continental').length,
    international: getJobsByLocationLevel('international').length
  }

  if (!currentLocation) {
    return (
      <section className="py-16 bg-gradient-to-br from-background via-background to-primary/5">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl font-bold mb-4">
                Discover Opportunities Worldwide
              </h2>
              <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
                Enable location access to see personalized job recommendations based on your area, 
                or explore our global opportunities below.
              </p>
              
              <div className="max-w-4xl mx-auto space-y-6 mb-8">
                {jobs.slice(0, 6).map((job, index) => (
                  <motion.div
                    key={job.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    className="relative"
                  >
                    {renderJobCard(job)}
                  </motion.div>
                ))}
              </div>

              <Button asChild size="lg" className="button-premium">
                <Link href="/jobs">
                  <Globe className="w-5 h-5 mr-2" />
                  View All Jobs
                </Link>
              </Button>
            </motion.div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-12 md:py-16 bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8 md:mb-12"
        >
          <div className="flex items-center justify-center space-x-2 mb-3 md:mb-4">
            <MapPin className="w-4 h-4 md:w-6 md:h-6 text-primary" />
            <span className="text-sm md:text-lg font-medium text-primary">
              {currentLocation.city}, {currentLocation.country}
            </span>
          </div>

          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 md:mb-4">
            <span className="hidden md:inline">Jobs Near You & Beyond</span>
            <span className="md:hidden">Local Jobs</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground mb-6 md:mb-8 max-w-3xl mx-auto px-4 md:px-0">
            <span className="hidden md:inline">
              Discover opportunities prioritized by proximity - from your neighborhood to the global market.
            </span>
            <span className="md:hidden">
              Jobs sorted by distance from you
            </span>
          </p>

          {/* Location Stats - Simplified for Mobile */}
          <div className="flex flex-wrap justify-center gap-2 md:gap-3 mb-6 md:mb-8">
            <div className="flex items-center space-x-1.5 md:space-x-2 bg-primary/10 text-primary px-3 py-1.5 md:px-4 md:py-2 rounded-full border border-primary/20">
              <Target className="w-3 h-3 md:w-4 md:h-4" />
              <span className="font-semibold text-sm md:text-base">{locationStats.local}</span>
              <span className="text-xs md:text-sm">Local</span>
            </div>
            {/* Hide regional on mobile */}
            <div className="hidden sm:flex items-center space-x-1.5 md:space-x-2 bg-blue-50 dark:bg-blue-950/20 text-blue-600 dark:text-blue-400 px-3 py-1.5 md:px-4 md:py-2 rounded-full border border-blue-200 dark:border-blue-800">
              <MapPin className="w-3 h-3 md:w-4 md:h-4" />
              <span className="font-semibold text-sm md:text-base">{locationStats.regional}</span>
              <span className="text-xs md:text-sm">Regional</span>
            </div>
            <div className="flex items-center space-x-1.5 md:space-x-2 bg-green-50 dark:bg-green-950/20 text-green-600 dark:text-green-400 px-3 py-1.5 md:px-4 md:py-2 rounded-full border border-green-200 dark:border-green-800">
              <Building className="w-3 h-3 md:w-4 md:h-4" />
              <span className="font-semibold text-sm md:text-base">{locationStats.national}</span>
              <span className="text-xs md:text-sm">
                <span className="hidden md:inline">National</span>
                <span className="md:hidden">All</span>
              </span>
            </div>
            {/* Hide continental on mobile */}
            <div className="hidden lg:flex items-center space-x-1.5 md:space-x-2 bg-orange-50 dark:bg-orange-950/20 text-orange-600 dark:text-orange-400 px-3 py-1.5 md:px-4 md:py-2 rounded-full border border-orange-200 dark:border-orange-800">
              <Globe className="w-3 h-3 md:w-4 md:h-4" />
              <span className="font-semibold text-sm md:text-base">{locationStats.continental}</span>
              <span className="text-xs md:text-sm">Continental</span>
            </div>
            <div className="flex items-center space-x-1.5 md:space-x-2 bg-purple-50 dark:bg-purple-950/20 text-purple-600 dark:text-purple-400 px-3 py-1.5 md:px-4 md:py-2 rounded-full border border-purple-200 dark:border-purple-800">
              <Zap className="w-3 h-3 md:w-4 md:h-4" />
              <span className="font-semibold text-sm md:text-base">{locationStats.international}</span>
              <span className="text-xs md:text-sm">Remote</span>
            </div>
          </div>
        </motion.div>

        {/* Location-based Job Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5 mb-8">
            <TabsTrigger value="local" className="flex items-center space-x-2">
              <Target className="w-4 h-4" />
              <span className="hidden sm:inline">Local</span>
            </TabsTrigger>
            <TabsTrigger value="regional" className="flex items-center space-x-2">
              <MapPin className="w-4 h-4" />
              <span className="hidden sm:inline">Regional</span>
            </TabsTrigger>
            <TabsTrigger value="national" className="flex items-center space-x-2">
              <Building className="w-4 h-4" />
              <span className="hidden sm:inline">National</span>
            </TabsTrigger>
            <TabsTrigger value="continental" className="flex items-center space-x-2">
              <Globe className="w-4 h-4" />
              <span className="hidden sm:inline">Continental</span>
            </TabsTrigger>
            <TabsTrigger value="international" className="flex items-center space-x-2">
              <Zap className="w-4 h-4" />
              <span className="hidden sm:inline">Global</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="local" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold">Jobs in {currentLocation.city}</h3>
                <p className="text-muted-foreground">Opportunities in your immediate area</p>
              </div>
              <Button variant="outline" asChild>
                <Link href={`/jobs?location=${currentLocation.city}`}>
                  View All Local Jobs
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>
            
            <div className="max-w-4xl mx-auto space-y-6">
              {getJobsByLocationLevel('local').map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  {renderJobCard(job)}
                </motion.div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Button asChild size="lg" className="button-premium">
                <Link href={`/jobs?location=${encodeURIComponent(currentLocation.city)}`}>
                  <MapPin className="w-5 h-5 mr-2" />
                  View All Local Jobs
                </Link>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="regional" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold">Jobs in {currentLocation.region}</h3>
                <p className="text-muted-foreground">Regional opportunities within your area</p>
              </div>
              <Button variant="outline" asChild>
                <Link href={`/jobs?location=${currentLocation.region}`}>
                  View All Regional Jobs
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>
            
            <div className="max-w-4xl mx-auto space-y-6">
              {getJobsByLocationLevel('regional').map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  {renderJobCard(job)}
                </motion.div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Button asChild size="lg" className="button-premium">
                <Link href={`/jobs?location=${encodeURIComponent(currentLocation.region)}`}>
                  <Building className="w-5 h-5 mr-2" />
                  View All Regional Jobs
                </Link>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="national" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold">Jobs in {currentLocation.country}</h3>
                <p className="text-muted-foreground">National opportunities across the country</p>
              </div>
              <Button variant="outline" asChild>
                <Link href={`/jobs?location=${currentLocation.country}`}>
                  View All National Jobs
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>
            
            <div className="max-w-4xl mx-auto space-y-6">
              {getJobsByLocationLevel('national').map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  {renderJobCard(job)}
                </motion.div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Button asChild size="lg" className="button-premium">
                <Link href={`/jobs?location=${encodeURIComponent(currentLocation.country)}`}>
                  <Target className="w-5 h-5 mr-2" />
                  View All National Jobs
                </Link>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="continental" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold">Jobs in {currentLocation.continent}</h3>
                <p className="text-muted-foreground">Continental opportunities across {currentLocation.continent}</p>
              </div>
              <Button variant="outline" asChild>
                <Link href={`/jobs?location=${currentLocation.continent}`}>
                  View All Continental Jobs
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>
            
            <div className="max-w-4xl mx-auto space-y-6">
              {getJobsByLocationLevel('continental').map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  {renderJobCard(job)}
                </motion.div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Button asChild size="lg" className="button-premium">
                <Link href={`/jobs?location=${encodeURIComponent(currentLocation.continent)}`}>
                  <Globe className="w-5 h-5 mr-2" />
                  View All Continental Jobs
                </Link>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="international" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold">International Opportunities</h3>
                <p className="text-muted-foreground">Global remote and international positions</p>
              </div>
              <Button variant="outline" asChild>
                <Link href="/jobs?remote=true">
                  View All International Jobs
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>
            
            <div className="max-w-4xl mx-auto space-y-6">
              {getJobsByLocationLevel('international').map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  {renderJobCard(job)}
                </motion.div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Button asChild size="lg" className="button-premium">
                <Link href="/jobs?remote=true">
                  <Zap className="w-5 h-5 mr-2" />
                  View All Remote Jobs
                </Link>
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
         
        </motion.div>

        {/* Job Application Modal */}
        <JobApplicationModal
          job={applicationJob}
          isOpen={!!applicationJob}
          onClose={() => setApplicationJob(null)}
          onSubmit={(applicationData) => {
            console.log('Application submitted:', applicationData)
            // In real app, would submit to API
            alert(`Application submitted successfully for ${applicationData.jobTitle}!`)
            setApplicationJob(null)
          }}
        />

      </div>
    </section>
  )
}
