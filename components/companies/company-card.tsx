'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  MapPin, 
  Star, 
  Users, 
  Calendar, 
  Eye, 
  Heart, 
  MessageCircle,
  Building,
  CheckCircle,
  TrendingUp,
  Award,
  Briefcase,
  ExternalLink,
  Globe,
  Target
} from 'lucide-react'

interface Company {
  id: number
  name: string
  industry: string
  location: string
  logo: string
  rating: number
  size: string
  founded: number
  description: string
  specialties: string[]
  openJobs: number
  followers: number
  website: string
  benefits: string[]
  culture: string
  verified: boolean
}

interface CompanyCardProps {
  company: Company
  viewMode: 'grid' | 'list'
  onViewProfile: (company: Company) => void
  onFollow: (company: Company) => void
  onViewJobs: (company: Company) => void
}

export function CompanyCard({ 
  company, 
  viewMode, 
  onViewProfile, 
  onFollow, 
  onViewJobs 
}: CompanyCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)

  const getCompanySizeLabel = (size: string) => {
    switch (size) {
      case 'startup':
        return '1-10 employees'
      case 'small':
        return '11-50 employees'
      case 'medium':
        return '51-200 employees'
      case 'large':
        return '201-1000 employees'
      case 'enterprise':
        return '1000+ employees'
      default:
        return size
    }
  }

  const handleFollow = () => {
    setIsFollowing(!isFollowing)
    if (onFollow) onFollow(company)
  }

  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <Card className={`card-premium transition-all duration-300 ${
          isHovered ? 'scale-[1.02] theme-glow' : ''
        }`}>
          <CardContent className="p-6">
            <div className="flex items-start space-x-6">
              {/* Logo and Verification */}
              <div className="relative">
                <Avatar className="w-20 h-20 border-2 border-primary/20">
                  <AvatarImage src={company.logo} alt={company.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-lg">
                    {company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                {company.verified && (
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                    <CheckCircle className="w-3 h-3 text-white" />
                  </div>
                )}
              </div>

              {/* Main Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-xl font-bold mb-1 truncate">{company.name}</h3>
                    <p className="text-primary font-medium mb-2">{company.industry}</p>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{company.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{company.rating}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Users className="w-4 h-4" />
                        <span>{getCompanySizeLabel(company.size)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>Founded {company.founded}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleFollow}
                      className={`p-2 ${isFollowing ? 'text-red-500' : 'text-muted-foreground'}`}
                    >
                      <Heart className={`w-4 h-4 ${isFollowing ? 'fill-current' : ''}`} />
                    </Button>
                    <div className="text-right">
                      <div className="text-lg font-bold text-primary">{company.openJobs}</div>
                      <div className="text-xs text-muted-foreground">open jobs</div>
                    </div>
                  </div>
                </div>

                <p className="text-muted-foreground mb-4 line-clamp-2">{company.description}</p>

                {/* Specialties */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {company.specialties.slice(0, 5).map((specialty) => (
                    <Badge key={specialty} variant="secondary" className="theme-glow">
                      {specialty}
                    </Badge>
                  ))}
                  {company.specialties.length > 5 && (
                    <Badge variant="outline">+{company.specialties.length - 5} more</Badge>
                  )}
                </div>

                {/* Stats and Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4 text-blue-500" />
                      <span>{company.followers.toLocaleString()} followers</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Briefcase className="w-4 h-4 text-green-500" />
                      <span>{company.openJobs} open positions</span>
                    </div>
                    {company.verified && (
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span>Verified Company</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onViewJobs(company)}
                    >
                      <Briefcase className="w-4 h-4 mr-2" />
                      View Jobs
                    </Button>
                    <Button 
                      size="sm" 
                      className="button-premium"
                      onClick={() => onViewProfile(company)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Quick View
                    </Button>
                    <Button 
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        window.open(`/company/${company.id}`, '_blank')
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Full Profile
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  // Grid View
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={`card-premium h-full transition-all duration-300 ${
        isHovered ? 'scale-105 theme-glow' : ''
      }`}>
        <CardContent className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="relative">
              <Avatar className="w-16 h-16 border-2 border-primary/20">
                <AvatarImage src={company.logo} alt={company.name} />
                <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold">
                  {company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              {company.verified && (
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                  <CheckCircle className="w-3 h-3 text-white" />
                </div>
              )}
            </div>
            
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleFollow}
                className={`p-2 ${isFollowing ? 'text-red-500' : 'text-muted-foreground'}`}
              >
                <Heart className={`w-4 h-4 ${isFollowing ? 'fill-current' : ''}`} />
              </Button>
              <div className="text-right">
                <div className="text-lg font-bold text-primary">{company.openJobs}</div>
                <div className="text-xs text-muted-foreground">jobs</div>
              </div>
            </div>
          </div>

          {/* Company Info */}
          <div className="mb-3">
            <h3 className="text-lg font-bold mb-1 truncate">{company.name}</h3>
            <p className="text-primary font-medium text-sm mb-2">{company.industry}</p>
            <div className="flex items-center space-x-3 text-xs text-muted-foreground">
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>{company.location}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                <span>{company.rating}</span>
              </div>
            </div>
          </div>

          {/* Description */}
          <p className="text-muted-foreground text-sm mb-4 line-clamp-3">{company.description}</p>

          {/* Specialties */}
          <div className="flex flex-wrap gap-1 mb-4">
            {company.specialties.slice(0, 3).map((specialty) => (
              <Badge key={specialty} variant="secondary" className="text-xs theme-glow">
                {specialty}
              </Badge>
            ))}
            {company.specialties.length > 3 && (
              <Badge variant="outline" className="text-xs">+{company.specialties.length - 3}</Badge>
            )}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-2 mb-4 text-xs">
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Users className="w-3 h-3 text-blue-500" />
              <span>{company.followers.toLocaleString()} followers</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Calendar className="w-3 h-3" />
              <span>Founded {company.founded}</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Building className="w-3 h-3" />
              <span>{getCompanySizeLabel(company.size)}</span>
            </div>
            {company.verified && (
              <div className="flex items-center space-x-1 text-muted-foreground">
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span>Verified</span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex flex-col space-y-2">
            <Button 
              size="sm" 
              className="w-full button-premium"
              onClick={() => onViewProfile(company)}
            >
              <Eye className="w-4 h-4 mr-2" />
              Quick View
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => onViewJobs(company)}
            >
              <Briefcase className="w-4 h-4 mr-2" />
              View Jobs ({company.openJobs})
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => {
                window.open(`/company/${company.id}`, '_blank')
              }}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Full Profile
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
