'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { 
  MapPin, 
  Building, 
  Users, 
  Calendar, 
  Star, 
  TrendingUp,
  ChevronDown,
  ChevronUp,
  X,
  Filter,
  Briefcase,
  Globe,
  Award,
  Target,
  Zap,
  Heart,
  Shield,
  Lightbulb,
  Rocket,
  Cpu,
  Palette,
  Stethoscope,
  GraduationCap,
  Scale,
  DollarSign,
  Home,
  Car,
  Plane
} from 'lucide-react'

interface CompanyFiltersProps {
  selectedIndustries: string[]
  setSelectedIndustries: (industries: string[]) => void
  companySizeRange: number[]
  setCompanySizeRange: (range: number[]) => void
  foundedRange: number[]
  setFoundedRange: (range: number[]) => void
  onFiltersChange?: () => void
  showApplyButton?: boolean
}

export function CompanyFilters({
  selectedIndustries,
  setSelectedIndustries,
  companySizeRange,
  setCompanySizeRange,
  foundedRange,
  setFoundedRange,
  onFiltersChange,
  showApplyButton = true
}: CompanyFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    industries: true,
    size: true,
    founded: true,
    location: true,
    benefits: true,
    rating: true
  })

  const [selectedLocation, setSelectedLocation] = useState('')
  const [selectedBenefits, setSelectedBenefits] = useState<string[]>([])
  const [minRating, setMinRating] = useState(0)

  const industryCategories = [
    {
      name: 'Technology & Software',
      icon: Cpu,
      industries: ['Software Development', 'AI/Machine Learning', 'Cybersecurity', 'Cloud Computing', 'Mobile Apps', 'SaaS', 'E-commerce', 'Fintech']
    },
    {
      name: 'Design & Creative',
      icon: Palette,
      industries: ['Graphic Design', 'UX/UI Design', 'Digital Marketing', 'Advertising', 'Media & Entertainment', 'Gaming', 'Animation', 'Photography']
    },
    {
      name: 'Healthcare & Life Sciences',
      icon: Stethoscope,
      industries: ['Healthcare', 'Biotechnology', 'Pharmaceuticals', 'Medical Devices', 'Telemedicine', 'Health Tech', 'Clinical Research', 'Wellness']
    },
    {
      name: 'Finance & Banking',
      icon: DollarSign,
      industries: ['Banking', 'Investment', 'Insurance', 'Accounting', 'Financial Services', 'Cryptocurrency', 'Trading', 'Wealth Management']
    },
    {
      name: 'Education & Training',
      icon: GraduationCap,
      industries: ['Education Technology', 'Online Learning', 'Corporate Training', 'Universities', 'K-12 Education', 'Professional Development', 'Certification', 'Tutoring']
    },
    {
      name: 'Legal & Compliance',
      icon: Scale,
      industries: ['Law Firms', 'Legal Tech', 'Compliance', 'Regulatory Affairs', 'Intellectual Property', 'Corporate Law', 'Litigation', 'Legal Services']
    },
    {
      name: 'Manufacturing & Industrial',
      icon: Building,
      industries: ['Manufacturing', 'Automotive', 'Aerospace', 'Industrial Equipment', 'Supply Chain', 'Logistics', 'Construction', 'Engineering']
    },
    {
      name: 'Retail & E-commerce',
      icon: Home,
      industries: ['Retail', 'E-commerce', 'Fashion', 'Consumer Goods', 'Luxury Brands', 'Food & Beverage', 'Sports & Recreation', 'Home & Garden']
    },
    {
      name: 'Transportation & Mobility',
      icon: Car,
      industries: ['Transportation', 'Logistics', 'Automotive Tech', 'Ride Sharing', 'Delivery Services', 'Aviation', 'Maritime', 'Public Transit']
    },
    {
      name: 'Energy & Environment',
      icon: Zap,
      industries: ['Renewable Energy', 'Oil & Gas', 'Utilities', 'Environmental Services', 'Clean Tech', 'Solar', 'Wind Energy', 'Sustainability']
    },
    {
      name: 'Travel & Hospitality',
      icon: Plane,
      industries: ['Travel', 'Hospitality', 'Tourism', 'Hotels', 'Airlines', 'Cruise Lines', 'Event Management', 'Vacation Rentals']
    },
    {
      name: 'Consulting & Services',
      icon: Target,
      industries: ['Management Consulting', 'IT Consulting', 'Business Services', 'Professional Services', 'Strategy', 'Operations', 'HR Consulting', 'Marketing Services']
    }
  ]

  const locations = [
    'San Francisco, CA', 'New York, NY', 'Los Angeles, CA', 'Seattle, WA', 'Austin, TX',
    'Boston, MA', 'Chicago, IL', 'Denver, CO', 'Miami, FL', 'Atlanta, GA',
    'Toronto, ON', 'Vancouver, BC', 'London, UK', 'Berlin, Germany', 'Amsterdam, Netherlands',
    'Singapore', 'Tokyo, Japan', 'Sydney, Australia', 'Remote-First', 'Global'
  ]

  const benefitOptions = [
    { value: 'health-insurance', label: 'Health Insurance', color: 'bg-green-500' },
    { value: 'dental-vision', label: 'Dental & Vision', color: 'bg-blue-500' },
    { value: 'retirement-401k', label: '401(k) / Retirement', color: 'bg-purple-500' },
    { value: 'flexible-hours', label: 'Flexible Hours', color: 'bg-orange-500' },
    { value: 'remote-work', label: 'Remote Work', color: 'bg-teal-500' },
    { value: 'unlimited-pto', label: 'Unlimited PTO', color: 'bg-pink-500' },
    { value: 'professional-development', label: 'Professional Development', color: 'bg-indigo-500' },
    { value: 'stock-options', label: 'Stock Options', color: 'bg-yellow-500' },
    { value: 'gym-wellness', label: 'Gym & Wellness', color: 'bg-red-500' },
    { value: 'parental-leave', label: 'Parental Leave', color: 'bg-cyan-500' }
  ]

  const companySizes = [
    { value: [1, 10], label: 'Startup (1-10)' },
    { value: [11, 50], label: 'Small (11-50)' },
    { value: [51, 200], label: 'Medium (51-200)' },
    { value: [201, 1000], label: 'Large (201-1000)' },
    { value: [1001, 10000], label: 'Enterprise (1000+)' }
  ]

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const toggleIndustry = (industry: string) => {
    setSelectedIndustries(
      selectedIndustries.includes(industry)
        ? selectedIndustries.filter(i => i !== industry)
        : [...selectedIndustries, industry]
    )
  }

  const toggleBenefit = (benefit: string) => {
    setSelectedBenefits(
      selectedBenefits.includes(benefit)
        ? selectedBenefits.filter(b => b !== benefit)
        : [...selectedBenefits, benefit]
    )
  }

  const clearAllFilters = () => {
    setSelectedIndustries([])
    setCompanySizeRange([1, 10000])
    setFoundedRange([1900, 2024])
    setSelectedLocation('')
    setSelectedBenefits([])
    setMinRating(0)
  }

  const activeFilterCount = selectedIndustries.length + 
    (companySizeRange[0] > 1 || companySizeRange[1] < 10000 ? 1 : 0) +
    (foundedRange[0] > 1900 || foundedRange[1] < 2024 ? 1 : 0) +
    (selectedLocation ? 1 : 0) +
    selectedBenefits.length +
    (minRating > 0 ? 1 : 0)

  const FilterSection = ({ 
    title, 
    icon: Icon, 
    section, 
    children 
  }: { 
    title: string
    icon: any
    section: keyof typeof expandedSections
    children: React.ReactNode 
  }) => (
    <div className="space-y-3">
      <Button
        variant="ghost"
        onClick={() => toggleSection(section)}
        className="w-full justify-between p-0 h-auto font-medium text-left hover:bg-transparent"
      >
        <div className="flex items-center space-x-2">
          <Icon className="w-4 h-4 text-primary" />
          <span>{title}</span>
        </div>
        {expandedSections[section] ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </Button>
      
      <AnimatePresence>
        {expandedSections[section] && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
      
      <Separator />
    </div>
  )

  return (
    <Card className="card-premium">
      <CardHeader className="pb-4 md:pb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 md:w-5 md:h-5 text-primary" />
            <CardTitle className="text-base md:text-lg">Filters</CardTitle>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="theme-glow text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-muted-foreground hover:text-foreground text-xs md:text-sm"
            >
              <X className="w-3 h-3 md:w-4 md:h-4 mr-1" />
              <span className="hidden sm:inline">Clear</span>
              <span className="sm:hidden">×</span>
            </Button>
          )}
        </div>
        <CardDescription className="text-xs md:text-sm">
          <span className="hidden md:inline">Find the perfect company for your career</span>
          <span className="md:hidden">Filter companies</span>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Industry Filter */}
        <FilterSection title="Industry & Sector" icon={Building} section="industries">
          <div className="space-y-4">
            {industryCategories.map((category) => (
              <div key={category.name} className="space-y-3">
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-primary/15 to-primary/10 border border-primary/20">
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <category.icon className="w-4 h-4 text-primary-foreground" />
                  </div>
                  <span className="font-semibold text-primary text-sm">{category.name}</span>
                </div>
                <div className="flex flex-wrap gap-2 pl-2">
                  {category.industries.map((industry) => (
                    <Badge
                      key={industry}
                      variant={selectedIndustries.includes(industry) ? "default" : "outline"}
                      className={`cursor-pointer transition-all duration-200 ${
                        selectedIndustries.includes(industry) 
                          ? 'theme-glow bg-primary text-primary-foreground border-primary' 
                          : 'hover:border-primary/50 hover:bg-primary/5'
                      }`}
                      onClick={() => toggleIndustry(industry)}
                    >
                      {industry}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Company Size Filter */}
        <FilterSection title="Company Size" icon={Users} section="size">
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Number of Employees</span>
              <span className="font-medium">
                {companySizeRange[0]} - {companySizeRange[1] === 10000 ? '10,000+' : companySizeRange[1]} employees
              </span>
            </div>
            <Slider
              value={companySizeRange}
              onValueChange={setCompanySizeRange}
              max={10000}
              min={1}
              step={10}
              className="w-full"
            />
            <div className="grid grid-cols-2 gap-2">
              {companySizes.map((size) => (
                <Button
                  key={size.label}
                  variant="outline"
                  size="sm"
                  onClick={() => setCompanySizeRange(size.value)}
                  className="text-xs"
                >
                  {size.label}
                </Button>
              ))}
            </div>
          </div>
        </FilterSection>

        {showApplyButton && (
          <Button 
            onClick={onFiltersChange} 
            className="w-full button-premium"
          >
            Apply Filters
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
