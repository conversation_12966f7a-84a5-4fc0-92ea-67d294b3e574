'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

export function HeroSectionSimple() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  const backgroundImages = [
    {
      url: "/images/hero/hero-main.jpg",
      alt: "Professional workplace - main hero image"
    },
    {
      url: "/images/hero/team-collaboration.jpg",
      alt: "Team collaboration in modern office"
    },
    {
      url: "/images/hero/office-meeting.jpg",
      alt: "Professional office meeting"
    },
    {
      url: "/images/hero/office-background.jpg",
      alt: "Modern office workspace"
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % backgroundImages.length)
    }, 3000) // Change every 3 seconds for testing
    return () => clearInterval(interval)
  }, [backgroundImages.length])

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Images */}
      <div className="absolute inset-0 -z-10">
        {backgroundImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentImageIndex ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <Image
              src={image.url}
              alt={image.alt}
              fill
              className="object-cover"
              priority={index === 0}
              quality={85}
              sizes="100vw"
              onLoad={() => console.log(`✅ Simple hero loaded: ${image.url}`)}
              onError={(e) => console.error(`❌ Simple hero failed: ${image.url}`, e)}
            />
          </div>
        ))}
      </div>

      {/* Dark overlay */}
      <div className="absolute inset-0 bg-black/40 -z-5" />

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4">
        <h1 className="text-4xl md:text-6xl font-bold mb-6">
          Find Your Dream Job
        </h1>
        <p className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto">
          Connect with top employers and discover opportunities that match your skills and aspirations.
        </p>
        
        {/* Image indicators */}
        <div className="flex justify-center space-x-2 mt-8">
          {backgroundImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentImageIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentImageIndex
                  ? 'bg-white scale-125'
                  : 'bg-white/50 hover:bg-white/75'
              }`}
              aria-label={`Switch to image ${index + 1}`}
            />
          ))}
        </div>

        {/* Debug info */}
        <div className="mt-4 text-sm opacity-75">
          Current image: {currentImageIndex + 1} / {backgroundImages.length}
          <br />
          {backgroundImages[currentImageIndex].url}
        </div>
      </div>
    </div>
  )
}
