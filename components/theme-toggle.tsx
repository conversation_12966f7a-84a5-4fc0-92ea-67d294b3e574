"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Leaf } from "lucide-react"

type Theme = "blue" | "green" | "dark"

export function ThemeToggle() {
  const [theme, setTheme] = useState<Theme>("blue")

  useEffect(() => {
    const savedTheme = (localStorage.getItem("theme") as Theme) || "blue"
    setTheme(savedTheme)
    applyTheme(savedTheme)
  }, [])

  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement

    // Remove existing theme classes
    root.classList.remove("theme-blue", "theme-green", "theme-dark")

    // Add new theme class
    root.classList.add(`theme-${newTheme}`)

    // Update CSS variables based on theme
    switch (newTheme) {
      case "blue":
        root.style.setProperty("--primary", "221 83% 53%")
        root.style.setProperty("--primary-foreground", "210 40% 98%")
        root.style.setProperty("--background", "0 0% 100%")
        root.style.setProperty("--foreground", "222.2 84% 4.9%")
        break
      case "green":
        root.style.setProperty("--primary", "142 76% 36%")
        root.style.setProperty("--primary-foreground", "355.7 100% 97.3%")
        root.style.setProperty("--background", "0 0% 100%")
        root.style.setProperty("--foreground", "222.2 84% 4.9%")
        break
      case "dark":
        root.style.setProperty("--primary", "210 40% 98%")
        root.style.setProperty("--primary-foreground", "222.2 84% 4.9%")
        root.style.setProperty("--background", "222.2 84% 4.9%")
        root.style.setProperty("--foreground", "210 40% 98%")
        break
    }
  }

  const handleThemeChange = (newTheme: Theme) => {
    setTheme(newTheme)
    localStorage.setItem("theme", newTheme)
    applyTheme(newTheme)
  }

  const themes = [
    { value: "blue" as const, label: "Ocean Blue", icon: Sun, color: "bg-blue-500" },
    { value: "green" as const, label: "Forest Green", icon: Leaf, color: "bg-green-600" },
    { value: "dark" as const, label: "Midnight", icon: Moon, color: "bg-gray-900" },
  ]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <motion.div whileHover={{ rotate: 180 }} transition={{ duration: 0.3 }}>
            <Palette className="w-5 h-5" />
          </motion.div>
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <AnimatePresence>
          {themes.map((themeOption, index) => (
            <motion.div
              key={themeOption.value}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <DropdownMenuItem
                onClick={() => handleThemeChange(themeOption.value)}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <div className={`w-4 h-4 rounded-full ${themeOption.color}`} />
                <themeOption.icon className="w-4 h-4" />
                <span>{themeOption.label}</span>
                {theme === themeOption.value && (
                  <motion.div layoutId="activeTheme" className="ml-auto w-2 h-2 bg-primary rounded-full" />
                )}
              </DropdownMenuItem>
            </motion.div>
          ))}
        </AnimatePresence>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
