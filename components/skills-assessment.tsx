"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Brain, CheckCircle, Clock, Star, Trophy, Target, ArrowRight, RotateCcw, Award } from "lucide-react"

const skillCategories = [
  {
    id: "frontend",
    name: "Frontend Development",
    icon: "💻",
    skills: ["React", "Vue.js", "Angular", "TypeScript", "CSS", "HTML"],
    color: "bg-blue-500",
  },
  {
    id: "backend",
    name: "Backend Development",
    icon: "⚙️",
    skills: ["Node.js", "Python", "Java", "Go", "PostgreSQL", "MongoDB"],
    color: "bg-green-500",
  },
  {
    id: "design",
    name: "UI/UX Design",
    icon: "🎨",
    skills: ["Figma", "Sketch", "Adobe XD", "Prototyping", "User Research"],
    color: "bg-purple-500",
  },
  {
    id: "data",
    name: "Data Science",
    icon: "📊",
    skills: ["Python", "R", "SQL", "Machine Learning", "Statistics"],
    color: "bg-orange-500",
  },
]

const sampleQuestions = [
  {
    id: 1,
    category: "frontend",
    question: "What is the purpose of React's useEffect hook?",
    options: [
      "To manage component state",
      "To perform side effects in functional components",
      "To create custom hooks",
      "To handle form submissions",
    ],
    correct: 1,
    difficulty: "intermediate",
  },
  {
    id: 2,
    category: "frontend",
    question: "Which CSS property is used to create a flexbox container?",
    options: ["display: block", "display: flex", "display: grid", "display: inline"],
    correct: 1,
    difficulty: "beginner",
  },
  {
    id: 3,
    category: "frontend",
    question: "What is the difference between let and const in JavaScript?",
    options: [
      "No difference",
      "let is block-scoped, const is function-scoped",
      "const creates immutable bindings, let allows reassignment",
      "let is newer than const",
    ],
    correct: 2,
    difficulty: "intermediate",
  },
]

export function SkillsAssessment() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState<string>("")
  const [answers, setAnswers] = useState<number[]>([])
  const [showResults, setShowResults] = useState(false)
  const [timeLeft, setTimeLeft] = useState(300) // 5 minutes
  const [isActive, setIsActive] = useState(false)

  const startAssessment = (categoryId: string) => {
    setSelectedCategory(categoryId)
    setCurrentQuestion(0)
    setAnswers([])
    setShowResults(false)
    setIsActive(true)
    setTimeLeft(300)
  }

  const handleAnswerSelect = (answerIndex: string) => {
    setSelectedAnswer(answerIndex)
  }

  const nextQuestion = () => {
    const newAnswers = [...answers, Number.parseInt(selectedAnswer)]
    setAnswers(newAnswers)
    setSelectedAnswer("")

    if (currentQuestion < sampleQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    } else {
      setShowResults(true)
      setIsActive(false)
    }
  }

  const resetAssessment = () => {
    setSelectedCategory(null)
    setCurrentQuestion(0)
    setAnswers([])
    setShowResults(false)
    setIsActive(false)
    setSelectedAnswer("")
  }

  const calculateScore = () => {
    let correct = 0
    answers.forEach((answer, index) => {
      if (answer === sampleQuestions[index].correct) {
        correct++
      }
    })
    return Math.round((correct / sampleQuestions.length) * 100)
  }

  const getSkillLevel = (score: number) => {
    if (score >= 90) return { level: "Expert", color: "text-green-600", icon: Trophy }
    if (score >= 75) return { level: "Advanced", color: "text-blue-600", icon: Star }
    if (score >= 60) return { level: "Intermediate", color: "text-orange-600", icon: Target }
    return { level: "Beginner", color: "text-gray-600", icon: Award }
  }

  return (
    <section className="py-12 md:py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8 md:mb-16"
        >
          <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-primary/10 text-primary text-xs md:text-sm font-medium mb-4 md:mb-6">
            <Brain className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2" />
            Skills Assessment
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
            Test Your <span className="text-primary">Expertise</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto px-4 md:px-0">
            <span className="hidden md:inline">
              Take our comprehensive skills assessment to showcase your abilities and get matched with relevant
              opportunities.
            </span>
            <span className="md:hidden">
              Test your skills and get better job matches.
            </span>
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            {!selectedCategory ? (
              // Category Selection
              <motion.div
                key="categories"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6"
              >
                {skillCategories.map((category, index) => (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card
                      className="glass cursor-pointer hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20"
                      onClick={() => startAssessment(category.id)}
                    >
                      <CardHeader className="pb-3 md:pb-6">
                        <CardTitle className="flex items-center space-x-3">
                          <div className="text-2xl md:text-3xl">{category.icon}</div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-lg md:text-xl font-bold truncate">{category.name}</h3>
                            <p className="text-xs md:text-sm text-muted-foreground">
                              <span className="hidden sm:inline">15 questions • 5 minutes</span>
                              <span className="sm:hidden">5 min test</span>
                            </p>
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3 md:space-y-4">
                        <div className="flex flex-wrap gap-1.5 md:gap-2">
                          {category.skills.slice(0, 3).map((skill) => (
                            <Badge key={skill} variant="secondary" className="text-xs">
                              {skill}
                            </Badge>
                          ))}
                          {category.skills.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{category.skills.length - 3}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <Clock className="w-4 h-4" />
                            <span>Quick Assessment</span>
                          </div>
                          <ArrowRight className="w-5 h-5 text-primary" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            ) : !showResults ? (
              // Assessment Questions
              <motion.div
                key="assessment"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
              >
                <Card className="glass">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2">
                        <div className="text-2xl">{skillCategories.find((c) => c.id === selectedCategory)?.icon}</div>
                        <span>{skillCategories.find((c) => c.id === selectedCategory)?.name}</span>
                      </CardTitle>
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-muted-foreground">
                          {currentQuestion + 1} of {sampleQuestions.length}
                        </div>
                        <div className="flex items-center space-x-2 text-sm">
                          <Clock className="w-4 h-4" />
                          <span>
                            {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, "0")}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Progress value={((currentQuestion + 1) / sampleQuestions.length) * 100} className="h-2" />
                  </CardHeader>
                  <CardContent className="space-y-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-6">{sampleQuestions[currentQuestion].question}</h3>
                      <RadioGroup value={selectedAnswer} onValueChange={handleAnswerSelect}>
                        <div className="space-y-4">
                          {sampleQuestions[currentQuestion].options.map((option, index) => (
                            <motion.div
                              key={index}
                              whileHover={{ scale: 1.01 }}
                              className="flex items-center space-x-3 p-4 rounded-lg border border-border/50 hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 cursor-pointer"
                            >
                              <RadioGroupItem value={index.toString()} id={`option-${index}`} />
                              <Label htmlFor={`option-${index}`} className="flex-1 cursor-pointer">
                                {option}
                              </Label>
                            </motion.div>
                          ))}
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="flex items-center justify-between">
                      <Button variant="outline" onClick={resetAssessment}>
                        <RotateCcw className="w-4 h-4 mr-2" />
                        Start Over
                      </Button>
                      <Button onClick={nextQuestion} disabled={!selectedAnswer} className="min-w-[120px]">
                        {currentQuestion === sampleQuestions.length - 1 ? "Finish" : "Next"}
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ) : (
              // Results
              <motion.div
                key="results"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="text-center space-y-8"
              >
                <Card className="glass">
                  <CardContent className="p-12">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring" }}
                      className="mb-8"
                    >
                      <div className="w-32 h-32 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-6">
                        {(() => {
                          const { icon: Icon } = getSkillLevel(calculateScore())
                          return <Icon className="w-16 h-16 text-primary" />
                        })()}
                      </div>
                      <h3 className="text-4xl font-bold mb-2">{calculateScore()}%</h3>
                      <p className={`text-xl font-semibold ${getSkillLevel(calculateScore()).color}`}>
                        {getSkillLevel(calculateScore()).level} Level
                      </p>
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                      <div className="p-4 rounded-lg bg-muted/30">
                        <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">
                          {answers.filter((answer, index) => answer === sampleQuestions[index].correct).length}
                        </div>
                        <div className="text-sm text-muted-foreground">Correct Answers</div>
                      </div>
                      <div className="p-4 rounded-lg bg-muted/30">
                        <Clock className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">
                          {Math.floor((300 - timeLeft) / 60)}:{((300 - timeLeft) % 60).toString().padStart(2, "0")}
                        </div>
                        <div className="text-sm text-muted-foreground">Time Taken</div>
                      </div>
                      <div className="p-4 rounded-lg bg-muted/30">
                        <Star className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">{Math.round(calculateScore() / 10)}</div>
                        <div className="text-sm text-muted-foreground">Skill Rating</div>
                      </div>
                    </div>

                    <div className="space-y-4 mb-8">
                      <h4 className="text-lg font-semibold">Recommended Next Steps:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                        <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                          <h5 className="font-semibold text-primary mb-2">Job Matches</h5>
                          <p className="text-sm text-muted-foreground">
                            Based on your score, you're qualified for {calculateScore() >= 75 ? "senior" : "mid-level"}{" "}
                            positions.
                          </p>
                        </div>
                        <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                          <h5 className="font-semibold text-primary mb-2">Skill Development</h5>
                          <p className="text-sm text-muted-foreground">
                            Consider improving in areas where you scored lower to unlock more opportunities.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button size="lg" className="text-lg px-8">
                        View Matching Jobs
                      </Button>
                      <Button variant="outline" size="lg" onClick={resetAssessment}>
                        Take Another Assessment
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </section>
  )
}
