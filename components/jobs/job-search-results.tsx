'use client'

import React from 'react'
import { useJobsStore, type Job } from '@/stores'
import { JobCard } from './job-card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { SearchResultsSkeleton } from '@/components/ui/skeleton'
import { ErrorAlert } from '@/components/ui/error-alert'
import { 
  ChevronLeft, 
  ChevronRight, 
  ArrowUpDown,
  Grid3X3,
  List,
  Briefcase
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface JobSearchResultsProps {
  className?: string
  viewMode?: 'grid' | 'list'
  onViewModeChange?: (mode: 'grid' | 'list') => void
}

export function JobSearchResults({ 
  className,
  viewMode = 'list',
  onViewModeChange
}: JobSearchResultsProps) {
  const { 
    jobs,
    searchLoading,
    searchError,
    pagination,
    searchQuery,
    updateSearchQuery,
    searchJobs,
    clearSearchError
  } = useJobsStore()

  // Sort options
  const sortOptions = [
    { value: 'relevance', label: 'Most Relevant' },
    { value: 'date', label: 'Most Recent' },
    { value: 'salary', label: 'Highest Salary' }
  ]

  // Handle sort change
  const handleSortChange = async (sortBy: string) => {
    const newQuery = {
      ...searchQuery,
      sortBy: sortBy as 'relevance' | 'date' | 'salary',
      page: 1 // Reset to first page
    }
    
    updateSearchQuery(newQuery)
    
    try {
      await searchJobs(newQuery)
    } catch (error) {
      console.error('Sort failed:', error)
    }
  }

  // Handle page change
  const handlePageChange = async (newPage: number) => {
    if (newPage < 1 || newPage > pagination.totalPages) return
    
    const newQuery = { ...searchQuery, page: newPage }
    updateSearchQuery(newQuery)
    
    try {
      await searchJobs(newQuery)
    } catch (error) {
      console.error('Page change failed:', error)
    }
  }

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const { page, totalPages } = pagination
    const pages: (number | string)[] = []
    
    if (totalPages <= 7) {
      // Show all pages if 7 or fewer
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Show first page
      pages.push(1)
      
      if (page > 3) {
        pages.push('...')
      }
      
      // Show pages around current page
      const start = Math.max(2, page - 1)
      const end = Math.min(totalPages - 1, page + 1)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      if (page < totalPages - 2) {
        pages.push('...')
      }
      
      // Show last page
      if (totalPages > 1) {
        pages.push(totalPages)
      }
    }
    
    return pages
  }

  // Loading state
  if (searchLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <SearchResultsSkeleton count={6} />
      </div>
    )
  }

  // Error state
  if (searchError) {
    return (
      <div className={className}>
        <ErrorAlert
          type="error"
          title="Search Failed"
          message={searchError.message || 'Failed to search jobs. Please try again.'}
          dismissible
          onDismiss={clearSearchError}
          actions={
            <Button
              variant="outline"
              size="sm"
              onClick={() => searchJobs(searchQuery)}
              className="mt-2"
            >
              Try Again
            </Button>
          }
        />
      </div>
    )
  }

  // No results state
  if (!jobs.length && !searchLoading) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No jobs found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search criteria or filters to find more results.
          </p>
          <Button
            variant="outline"
            onClick={() => {
              updateSearchQuery({ q: '', location: '', page: 1 })
              searchJobs({ q: '', location: '', page: 1 })
            }}
          >
            Clear Search
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold">
            {pagination.total.toLocaleString()} Jobs Found
          </h2>
          
          {/* View Mode Toggle */}
          {onViewModeChange && (
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewModeChange('list')}
                className="h-8 px-3"
              >
                <List className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewModeChange('grid')}
                className="h-8 px-3"
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Sort Dropdown */}
        <div className="flex items-center space-x-2">
          <ArrowUpDown className="w-4 h-4 text-muted-foreground" />
          <Select
            value={searchQuery.sortBy || 'relevance'}
            onValueChange={handleSortChange}
          >
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Job Results */}
      <div className={cn(
        viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
      )}>
        {jobs.map((job) => (
          <JobCard
            key={job._id}
            job={job}
            variant={viewMode === 'grid' ? 'default' : 'default'}
          />
        ))}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total.toLocaleString()} results
              </div>

              <div className="flex items-center space-x-2">
                {/* Previous Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1 || searchLoading}
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                  Previous
                </Button>

                {/* Page Numbers */}
                <div className="flex items-center space-x-1">
                  {getPageNumbers().map((pageNum, index) => (
                    <React.Fragment key={index}>
                      {pageNum === '...' ? (
                        <span className="px-2 py-1 text-muted-foreground">...</span>
                      ) : (
                        <Button
                          variant={pageNum === pagination.page ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => handlePageChange(pageNum as number)}
                          disabled={searchLoading}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      )}
                    </React.Fragment>
                  ))}
                </div>

                {/* Next Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages || searchLoading}
                >
                  Next
                  <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
