'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Job } from '@/lib/job-data'
import { 
  MapPin, 
  DollarSign, 
  Clock, 
  Eye, 
  Heart, 
  Briefcase,
  CheckCircle,
  TrendingUp,
  Users,
  Calendar,
  ExternalLink,
  Bookmark,
  Share2,
  Building,
  Globe,
  Zap,
  Star,
  GraduationCap,
  Shield,
  Plane,
  Languages,
  Award,
  Target,
  ChevronRight,
  X
} from 'lucide-react'

interface JobDetailModalProps {
  job: Job | null
  isOpen: boolean
  onClose: () => void
  onApply: (job: Job) => void
  onSave: (job: Job) => void
}

export function PremiumJobDetailModal({ 
  job, 
  isOpen, 
  onClose, 
  onApply, 
  onSave 
}: JobDetailModalProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [isSaved, setIsSaved] = useState(false)

  if (!job) return null

  const formatSalary = (salary: Job['salary']) => {
    const min = salary.min.toLocaleString()
    const max = salary.max.toLocaleString()
    const period = salary.period === 'year' ? '/year' : salary.period === 'month' ? '/month' : '/hour'
    return `$${min} - $${max}${period}`
  }

  const handleSave = () => {
    setIsSaved(!isSaved)
    onSave(job)
  }

  const handleApply = () => {
    onApply(job)
    // Don't close the modal immediately - let the parent handle it
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <DialogHeader className="p-6 pb-4 border-b bg-gradient-to-r from-primary/5 to-primary/10">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="relative">
                  <Avatar className="w-16 h-16 border-2 border-primary/20">
                    <AvatarImage src={job.company.logo} alt={job.company.name} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-lg">
                      {job.company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  {job.company.verified && (
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                      <CheckCircle className="w-3 h-3 text-white" />
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <DialogTitle className="text-2xl font-bold">{job.title}</DialogTitle>
                    {job.featured && (
                      <Badge variant="secondary" className="bg-yellow-500 text-white">
                        <Star className="w-3 h-3 mr-1" />
                        Featured
                      </Badge>
                    )}
                    {job.urgent && (
                      <Badge variant="secondary" className="bg-red-500 text-white">
                        <Zap className="w-3 h-3 mr-1" />
                        Urgent
                      </Badge>
                    )}
                  </div>
                  <p className="text-primary font-semibold text-lg mb-2">{job.company.name}</p>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>{job.location}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4" />
                      <span>{formatSalary(job.salary)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Briefcase className="w-4 h-4" />
                      <span>{job.type}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Globe className="w-4 h-4" />
                      <span>{job.workModel}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="w-4 h-4" />
                      <span>{job.experience}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSave}
                  className={`p-2 ${isSaved ? 'text-yellow-500' : 'text-muted-foreground'}`}
                >
                  <Bookmark className={`w-5 h-5 ${isSaved ? 'fill-current' : ''}`} />
                </Button>
                <Button variant="ghost" size="sm" className="p-2">
                  <Share2 className="w-5 h-5" />
                </Button>
                <Button variant="ghost" size="sm" onClick={onClose} className="p-2">
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-4 mx-6 mt-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="requirements">Requirements</TabsTrigger>
                <TabsTrigger value="company">Company</TabsTrigger>
                <TabsTrigger value="benefits">Benefits</TabsTrigger>
              </TabsList>
              
              <div className="flex-1 overflow-hidden">
                <ScrollArea className="h-full px-6 pb-6">
                  <TabsContent value="overview" className="space-y-6 mt-6">
                    {/* Job Description */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Job Description</h3>
                      <p className="text-muted-foreground leading-relaxed">{job.description}</p>
                    </div>

                    {/* Key Responsibilities */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Key Responsibilities</h3>
                      <ul className="space-y-2">
                        {job.responsibilities.map((responsibility, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <ChevronRight className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                            <span className="text-muted-foreground">{responsibility}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Skills */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Required Skills</h3>
                      <div className="flex flex-wrap gap-2">
                        {job.skills.map((skill) => (
                          <Badge key={skill} variant="secondary" className="theme-glow">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Job Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-primary">{job.applicants}</div>
                        <div className="text-sm text-muted-foreground">Applicants</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-primary">{job.views.toLocaleString()}</div>
                        <div className="text-sm text-muted-foreground">Views</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-primary">{job.posted}</div>
                        <div className="text-sm text-muted-foreground">Posted</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-primary">{job.company.rating}</div>
                        <div className="text-sm text-muted-foreground">Company Rating</div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="requirements" className="space-y-6 mt-6">
                    {/* Requirements */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Requirements</h3>
                      <ul className="space-y-2">
                        {job.requirements.map((requirement, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span className="text-muted-foreground">{requirement}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Additional Info */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {job.education && (
                        <div className="flex items-start space-x-3">
                          <GraduationCap className="w-5 h-5 text-primary mt-0.5" />
                          <div>
                            <h4 className="font-medium">Education</h4>
                            <p className="text-sm text-muted-foreground">{job.education}</p>
                          </div>
                        </div>
                      )}
                      
                      {job.languages && (
                        <div className="flex items-start space-x-3">
                          <Languages className="w-5 h-5 text-primary mt-0.5" />
                          <div>
                            <h4 className="font-medium">Languages</h4>
                            <p className="text-sm text-muted-foreground">{job.languages.join(', ')}</p>
                          </div>
                        </div>
                      )}
                      
                      {job.travelRequired && (
                        <div className="flex items-start space-x-3">
                          <Plane className="w-5 h-5 text-primary mt-0.5" />
                          <div>
                            <h4 className="font-medium">Travel Required</h4>
                            <p className="text-sm text-muted-foreground">{job.travelRequired}</p>
                          </div>
                        </div>
                      )}
                      
                      {job.securityClearance && (
                        <div className="flex items-start space-x-3">
                          <Shield className="w-5 h-5 text-primary mt-0.5" />
                          <div>
                            <h4 className="font-medium">Security Clearance</h4>
                            <p className="text-sm text-muted-foreground">Required</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="company" className="space-y-6 mt-6">
                    {/* Company Info */}
                    <div className="flex items-start space-x-4 p-4 bg-muted/50 rounded-lg">
                      <Avatar className="w-16 h-16">
                        <AvatarImage src={job.company.logo} alt={job.company.name} />
                        <AvatarFallback>{job.company.name.slice(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold mb-1">{job.company.name}</h3>
                        <p className="text-muted-foreground mb-2">{job.company.industry}</p>
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <Users className="w-4 h-4" />
                            <span>{job.company.size} employees</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-500" />
                            <span>{job.company.rating}/5.0</span>
                          </div>
                          {job.company.verified && (
                            <div className="flex items-center space-x-1">
                              <CheckCircle className="w-4 h-4 text-green-500" />
                              <span>Verified</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Team Info */}
                    {job.teamSize && (
                      <div>
                        <h4 className="font-semibold mb-2">Team Information</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <Users className="w-4 h-4 text-primary" />
                            <span>Team Size: {job.teamSize}</span>
                          </div>
                          {job.reportingTo && (
                            <div className="flex items-center space-x-2">
                              <Target className="w-4 h-4 text-primary" />
                              <span>Reports to: {job.reportingTo}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="benefits" className="space-y-6 mt-6">
                    {/* Benefits */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Benefits & Perks</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {job.benefits.map((benefit, index) => (
                          <div key={index} className="flex items-center space-x-2 p-3 bg-muted/50 rounded-lg">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-sm">{benefit}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Visa Sponsorship */}
                    {job.visaSponsorship && (
                      <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Globe className="w-5 h-5 text-blue-500" />
                          <span className="font-medium text-blue-700 dark:text-blue-300">Visa Sponsorship Available</span>
                        </div>
                        <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                          This company provides visa sponsorship for qualified candidates.
                        </p>
                      </div>
                    )}
                  </TabsContent>
                </ScrollArea>
              </div>
            </Tabs>
          </div>

          {/* Footer Actions */}
          <div className="p-6 border-t bg-muted/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>Posted {job.posted}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{job.views.toLocaleString()} views</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Button variant="outline" onClick={() => window.open(`/jobs/${job.id}`, '_blank')}>
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Full Details
                </Button>
                <Button onClick={handleApply} className="button-premium">
                  <Briefcase className="w-4 h-4 mr-2" />
                  Apply Now
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
