'use client'

import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useJobsStore, useAuthStore, type Job, type ApplicationData } from '@/stores'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ButtonLoading } from '@/components/ui/button-loading'
import { ErrorAlert, InlineError } from '@/components/ui/error-alert'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  FileText, 
  User, 
  Mail, 
  Phone, 
  MapPin,
  Briefcase,
  X,
  CheckCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface JobApplicationFormProps {
  job: Job
  onSuccess?: () => void
  onCancel?: () => void
}

interface ApplicationFormData {
  coverLetter: string
  resumeFile: File | null
  portfolioUrl: string
  linkedinUrl: string
  availableStartDate: string
  salaryExpectation: string
  willingToRelocate: boolean
  customAnswers: Record<string, string>
}

interface FormErrors {
  coverLetter?: string
  resumeFile?: string
  portfolioUrl?: string
  linkedinUrl?: string
  availableStartDate?: string
  salaryExpectation?: string
  general?: string
}

export function JobApplicationForm({ job, onSuccess, onCancel }: JobApplicationFormProps) {
  const router = useRouter()
  const { user } = useAuthStore()
  const { applyToJob, applyLoading, error, clearError } = useJobsStore()
  
  const [formData, setFormData] = useState<ApplicationFormData>({
    coverLetter: '',
    resumeFile: null,
    portfolioUrl: '',
    linkedinUrl: '',
    availableStartDate: '',
    salaryExpectation: '',
    willingToRelocate: false,
    customAnswers: {}
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [dragActive, setDragActive] = useState(false)

  // Sample custom questions (would come from job posting)
  const customQuestions = [
    {
      id: 'experience_years',
      question: 'How many years of experience do you have in this field?',
      type: 'select',
      options: ['0-1 years', '2-3 years', '4-5 years', '6-10 years', '10+ years'],
      required: true
    },
    {
      id: 'remote_preference',
      question: 'What is your preference for remote work?',
      type: 'select',
      options: ['Fully remote', 'Hybrid', 'On-site only', 'Flexible'],
      required: false
    },
    {
      id: 'motivation',
      question: 'What motivates you to apply for this position?',
      type: 'textarea',
      required: true
    }
  ]

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.coverLetter.trim()) {
      newErrors.coverLetter = 'Cover letter is required'
    } else if (formData.coverLetter.length < 100) {
      newErrors.coverLetter = 'Cover letter should be at least 100 characters'
    }

    if (!formData.resumeFile) {
      newErrors.resumeFile = 'Resume is required'
    }

    if (formData.portfolioUrl && !isValidUrl(formData.portfolioUrl)) {
      newErrors.portfolioUrl = 'Please enter a valid URL'
    }

    if (formData.linkedinUrl && !isValidUrl(formData.linkedinUrl)) {
      newErrors.linkedinUrl = 'Please enter a valid LinkedIn URL'
    }

    if (!formData.availableStartDate) {
      newErrors.availableStartDate = 'Available start date is required'
    }

    // Validate custom questions
    customQuestions.forEach(question => {
      if (question.required && !formData.customAnswers[question.id]) {
        newErrors.general = 'Please answer all required questions'
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // URL validation helper
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()

    if (!validateForm()) {
      return
    }

    try {
      const applicationData: ApplicationData = {
        coverLetter: formData.coverLetter,
        resumeId: 'temp-resume-id', // Would be actual resume ID after upload
        customFields: {
          portfolioUrl: formData.portfolioUrl,
          linkedinUrl: formData.linkedinUrl,
          availableStartDate: formData.availableStartDate,
          salaryExpectation: formData.salaryExpectation,
          willingToRelocate: formData.willingToRelocate,
          ...formData.customAnswers
        }
      }

      await applyToJob(job._id, applicationData)
      
      if (onSuccess) {
        onSuccess()
      } else {
        router.push(`/applications?applied=${job._id}`)
      }
    } catch (error) {
      console.error('Application failed:', error)
    }
  }

  // Handle file upload
  const handleFileUpload = (file: File) => {
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({ ...prev, resumeFile: 'Please upload a PDF or Word document' }))
      return
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, resumeFile: 'File size must be less than 5MB' }))
      return
    }

    setFormData(prev => ({ ...prev, resumeFile: file }))
    setErrors(prev => ({ ...prev, resumeFile: undefined }))
  }

  // Handle drag and drop
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0])
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof ApplicationFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = field === 'willingToRelocate' ? (e.target as HTMLInputElement).checked : e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear field error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Handle custom question answers
  const handleCustomAnswer = (questionId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      customAnswers: { ...prev.customAnswers, [questionId]: value }
    }))
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl">Apply for {job.title}</CardTitle>
              <CardDescription className="mt-2">
                at {job.company.name} • {job.location.remote ? 'Remote' : `${job.location.city}, ${job.location.state}`}
              </CardDescription>
            </div>
            <Badge variant="outline" className="ml-4">
              {job.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* General Error */}
        {error && (
          <ErrorAlert
            type="error"
            message={error.message || 'Application failed. Please try again.'}
            dismissible
            onDismiss={clearError}
          />
        )}

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="w-5 h-5" />
              <span>Personal Information</span>
            </CardTitle>
            <CardDescription>
              This information will be shared with the employer
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Full Name</Label>
                <Input
                  value={`${user?.profile?.firstName} ${user?.profile?.lastName}`}
                  disabled
                  className="bg-muted"
                />
              </div>
              <div>
                <Label>Email</Label>
                <Input
                  value={user?.email}
                  disabled
                  className="bg-muted"
                />
              </div>
              <div>
                <Label>Phone</Label>
                <Input
                  value={user?.profile?.phone || 'Not provided'}
                  disabled
                  className="bg-muted"
                />
              </div>
              <div>
                <Label>Location</Label>
                <Input
                  value={user?.profile?.location ? 
                    `${user.profile.location.city}, ${user.profile.location.state}` : 
                    'Not provided'
                  }
                  disabled
                  className="bg-muted"
                />
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              To update this information, please visit your{' '}
              <Button variant="link" className="p-0 h-auto" onClick={() => router.push('/profile')}>
                profile settings
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Resume Upload */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5" />
              <span>Resume *</span>
            </CardTitle>
            <CardDescription>
              Upload your most recent resume (PDF or Word document, max 5MB)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div
              className={cn(
                'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
                dragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25',
                errors.resumeFile && 'border-red-500'
              )}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {formData.resumeFile ? (
                <div className="flex items-center justify-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium">{formData.resumeFile.name}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setFormData(prev => ({ ...prev, resumeFile: null }))}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="w-12 h-12 text-muted-foreground mx-auto" />
                  <div>
                    <p className="text-lg font-medium">Drop your resume here</p>
                    <p className="text-muted-foreground">or click to browse files</p>
                  </div>
                  <Input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        handleFileUpload(e.target.files[0])
                      }
                    }}
                    className="hidden"
                    id="resume-upload"
                  />
                  <Label htmlFor="resume-upload" className="cursor-pointer">
                    <Button type="button" variant="outline" asChild>
                      <span>Choose File</span>
                    </Button>
                  </Label>
                </div>
              )}
            </div>
            <InlineError message={errors.resumeFile} />
          </CardContent>
        </Card>

        {/* Cover Letter */}
        <Card>
          <CardHeader>
            <CardTitle>Cover Letter *</CardTitle>
            <CardDescription>
              Tell the employer why you're interested in this position and what makes you a great fit
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Dear Hiring Manager,

I am writing to express my interest in the [Job Title] position at [Company Name]. With my background in..."
              value={formData.coverLetter}
              onChange={handleInputChange('coverLetter')}
              className={cn(
                'min-h-32 resize-none',
                errors.coverLetter && 'border-red-500'
              )}
              disabled={applyLoading}
            />
            <div className="flex justify-between items-center mt-2">
              <InlineError message={errors.coverLetter} />
              <span className="text-xs text-muted-foreground">
                {formData.coverLetter.length}/1000 characters
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <CardDescription>
              Optional information that may help your application
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="portfolioUrl">Portfolio URL</Label>
                <Input
                  id="portfolioUrl"
                  type="url"
                  placeholder="https://yourportfolio.com"
                  value={formData.portfolioUrl}
                  onChange={handleInputChange('portfolioUrl')}
                  className={cn(errors.portfolioUrl && 'border-red-500')}
                  disabled={applyLoading}
                />
                <InlineError message={errors.portfolioUrl} />
              </div>

              <div>
                <Label htmlFor="linkedinUrl">LinkedIn Profile</Label>
                <Input
                  id="linkedinUrl"
                  type="url"
                  placeholder="https://linkedin.com/in/yourprofile"
                  value={formData.linkedinUrl}
                  onChange={handleInputChange('linkedinUrl')}
                  className={cn(errors.linkedinUrl && 'border-red-500')}
                  disabled={applyLoading}
                />
                <InlineError message={errors.linkedinUrl} />
              </div>

              <div>
                <Label htmlFor="availableStartDate">Available Start Date *</Label>
                <Input
                  id="availableStartDate"
                  type="date"
                  value={formData.availableStartDate}
                  onChange={handleInputChange('availableStartDate')}
                  className={cn(errors.availableStartDate && 'border-red-500')}
                  disabled={applyLoading}
                  min={new Date().toISOString().split('T')[0]}
                />
                <InlineError message={errors.availableStartDate} />
              </div>

              <div>
                <Label htmlFor="salaryExpectation">Salary Expectation</Label>
                <Input
                  id="salaryExpectation"
                  placeholder="e.g., $80,000 - $100,000"
                  value={formData.salaryExpectation}
                  onChange={handleInputChange('salaryExpectation')}
                  disabled={applyLoading}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="willingToRelocate"
                checked={formData.willingToRelocate}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({ ...prev, willingToRelocate: !!checked }))
                }
                disabled={applyLoading}
              />
              <Label htmlFor="willingToRelocate" className="text-sm">
                I am willing to relocate for this position
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Custom Questions */}
        {customQuestions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Additional Questions</CardTitle>
              <CardDescription>
                Please answer the following questions from the employer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {customQuestions.map((question) => (
                <div key={question.id} className="space-y-2">
                  <Label className="text-sm font-medium">
                    {question.question}
                    {question.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>

                  {question.type === 'select' && question.options ? (
                    <Select
                      value={formData.customAnswers[question.id] || ''}
                      onValueChange={(value) => handleCustomAnswer(question.id, value)}
                      disabled={applyLoading}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select an option" />
                      </SelectTrigger>
                      <SelectContent>
                        {question.options.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : question.type === 'textarea' ? (
                    <Textarea
                      placeholder="Your answer..."
                      value={formData.customAnswers[question.id] || ''}
                      onChange={(e) => handleCustomAnswer(question.id, e.target.value)}
                      className="min-h-24 resize-none"
                      disabled={applyLoading}
                    />
                  ) : (
                    <Input
                      placeholder="Your answer..."
                      value={formData.customAnswers[question.id] || ''}
                      onChange={(e) => handleCustomAnswer(question.id, e.target.value)}
                      disabled={applyLoading}
                    />
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Submit Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4 justify-end">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={applyLoading}
                  className="sm:w-auto w-full"
                >
                  Cancel
                </Button>
              )}

              <ButtonLoading
                type="submit"
                loading={applyLoading}
                loadingText="Submitting Application..."
                disabled={applyLoading}
                className="sm:w-auto w-full"
                size="lg"
              >
                Submit Application
              </ButtonLoading>
            </div>

            <div className="mt-4 text-xs text-muted-foreground">
              By submitting this application, you agree to share your information with {job.company.name}
              and acknowledge that you have read our privacy policy.
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  )
}
