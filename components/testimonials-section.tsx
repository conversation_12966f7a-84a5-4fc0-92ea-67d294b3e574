"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Star, Quote, ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Senior Frontend Developer",
    company: "TechFlow Inc.",
    avatar: "/images/avatars/avatar-1.jpg",
    rating: 5,
    text: "JobPortal's AI matching was incredible. I found my dream job in just 2 weeks! The salary insights helped me negotiate 20% above my initial offer.",
    tags: ["AI Matching", "Salary Insights"],
    outcome: "20% salary increase",
    timeToHire: "2 weeks",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Product Manager",
    company: "InnovateLab",
    avatar: "/images/avatars/avatar-2.jpg",
    rating: 5,
    text: "The skills assessment gave me confidence in my abilities and helped me target the right level positions. The career path visualizer was a game-changer.",
    tags: ["Skills Assessment", "Career Planning"],
    outcome: "Promoted to Senior PM",
    timeToHire: "3 weeks",
  },
  {
    id: 3,
    name: "Emily Johnson",
    role: "UX Designer",
    company: "DesignStudio",
    avatar: "/images/avatars/avatar-3.jpg",
    rating: 5,
    text: "I love how the platform shows real-time job postings. I was able to apply within minutes of jobs being posted and got multiple interviews.",
    tags: ["Live Feed", "Quick Apply"],
    outcome: "3 job offers",
    timeToHire: "1 week",
  },
  {
    id: 4,
    name: "David Kim",
    role: "DevOps Engineer",
    company: "CloudTech",
    avatar: "/images/avatars/avatar-2.jpg",
    rating: 5,
    text: "The company culture insights and detailed job descriptions helped me find a perfect fit. No more guessing about company values and work environment.",
    tags: ["Company Insights", "Culture Fit"],
    outcome: "Perfect culture match",
    timeToHire: "2 weeks",
  },
  {
    id: 5,
    name: "Lisa Wang",
    role: "Data Scientist",
    company: "DataCorp",
    avatar: "/images/avatars/avatar-1.jpg",
    rating: 5,
    text: "The personalized job recommendations were spot-on. Every suggestion was relevant to my skills and career goals. Highly recommend!",
    tags: ["Personalization", "Recommendations"],
    outcome: "Dream job found",
    timeToHire: "10 days",
  },
  {
    id: 6,
    name: "Alex Thompson",
    role: "Full Stack Developer",
    company: "StartupXYZ",
    avatar: "/images/avatars/avatar-3.jpg",
    rating: 5,
    text: "As someone switching careers, the skill development recommendations and learning paths were invaluable. I successfully transitioned to tech!",
    tags: ["Career Switch", "Skill Development"],
    outcome: "Successful career change",
    timeToHire: "6 weeks",
  },
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  return (
    <section className="py-12 md:py-20 bg-gradient-to-br from-primary/5 via-background to-primary/10">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8 md:mb-16"
        >
          <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-primary/10 text-primary text-xs md:text-sm font-medium mb-4 md:mb-6">
            <Star className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2 fill-current" />
            Success Stories
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
            What Our <span className="text-primary">Users Say</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto px-4 md:px-0">
            <span className="hidden md:inline">
              Join thousands of professionals who found their dream careers through our platform.
            </span>
            <span className="md:hidden">
              Real success stories from our users.
            </span>
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          {/* Main Testimonial Display */}
          <div className="relative mb-8 md:mb-12">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="glass relative overflow-hidden">
                  <div className="absolute top-4 left-4 md:top-6 md:left-6 text-primary/20">
                    <Quote className="w-8 h-8 md:w-12 md:h-12" />
                  </div>
                  <CardContent className="p-6 pt-12 md:p-12 md:pt-16">
                    <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-6">
                      <Avatar className="w-16 h-16 md:w-20 md:h-20 border-4 border-primary/20 mx-auto md:mx-0">
                        <AvatarImage src={testimonials[currentIndex].avatar || "/placeholder.svg"} />
                        <AvatarFallback className="text-base md:text-lg font-bold">
                          {testimonials[currentIndex].name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 text-center md:text-left">
                        <div className="flex items-center justify-center md:justify-start space-x-1 md:space-x-2 mb-3 md:mb-4">
                          {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                            <Star key={i} className="w-4 h-4 md:w-5 md:h-5 text-yellow-500 fill-current" />
                          ))}
                        </div>
                        <blockquote className="text-base md:text-xl leading-relaxed mb-4 md:mb-6 text-foreground">
                          "{testimonials[currentIndex].text}"
                        </blockquote>
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-bold text-lg">{testimonials[currentIndex].name}</h4>
                            <p className="text-muted-foreground">
                              {testimonials[currentIndex].role} at {testimonials[currentIndex].company}
                            </p>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {testimonials[currentIndex].tags.map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border/50">
                            <div>
                              <div className="text-sm text-muted-foreground">Outcome</div>
                              <div className="font-semibold text-primary">{testimonials[currentIndex].outcome}</div>
                            </div>
                            <div>
                              <div className="text-sm text-muted-foreground">Time to Hire</div>
                              <div className="font-semibold text-primary">{testimonials[currentIndex].timeToHire}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="absolute top-1/2 -translate-y-1/2 -left-4">
              <Button
                variant="outline"
                size="icon"
                onClick={prevTestimonial}
                className="rounded-full bg-background/80 backdrop-blur-sm border-border/50 hover:bg-background"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
            </div>
            <div className="absolute top-1/2 -translate-y-1/2 -right-4">
              <Button
                variant="outline"
                size="icon"
                onClick={nextTestimonial}
                className="rounded-full bg-background/80 backdrop-blur-sm border-border/50 hover:bg-background"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Testimonial Indicators */}
          <div className="flex justify-center space-x-2 mb-12">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-primary scale-125"
                    : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                }`}
              />
            ))}
          </div>

          {/* Testimonial Grid Preview */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            {testimonials.slice(0, 3).map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                whileHover={{ scale: 1.02 }}
                className="cursor-pointer"
                onClick={() => goToTestimonial(index)}
              >
                <Card className="glass hover:shadow-lg transition-all duration-300 border-border/50 hover:border-primary/20">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={testimonial.avatar || "/placeholder.svg"} />
                        <AvatarFallback>
                          {testimonial.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-semibold text-sm">{testimonial.name}</h4>
                        <p className="text-xs text-muted-foreground">{testimonial.role}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 mb-3">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 text-yellow-500 fill-current" />
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-3">{testimonial.text}</p>
                    <div className="mt-4 pt-4 border-t border-border/50">
                      <div className="text-xs text-primary font-semibold">{testimonial.outcome}</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}
