"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>lider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Search, MapPin, Filter, Briefcase, DollarSign, Clock, Building2, Sparkles, X } from "lucide-react"

const popularSearches = [
  "Frontend Developer",
  "Product Manager",
  "Data Scientist",
  "UX Designer",
  "DevOps Engineer",
  "Marketing Manager",
  "Sales Representative",
  "Backend Developer",
]

const jobTypes = ["Full-time", "Part-time", "Contract", "Freelance", "Internship"]
const experienceLevels = ["Entry Level", "Mid Level", "Senior Level", "Executive"]
const industries = ["Technology", "Healthcare", "Finance", "Education", "Marketing", "Sales"]

export function InteractiveJobSearch() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [location, setLocation] = useState("")
  const [salaryRange, setSalaryRange] = useState([50000, 150000])
  const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>([])
  const [selectedExperience, setSelectedExperience] = useState<string[]>([])
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([])
  const [isRemoteOnly, setIsRemoteOnly] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)

  useEffect(() => {
    if (searchQuery.length > 2) {
      const suggestions = popularSearches.filter((search) => search.toLowerCase().includes(searchQuery.toLowerCase()))
      setSearchSuggestions(suggestions)
      setShowSuggestions(true)
    } else {
      setShowSuggestions(false)
    }
  }, [searchQuery])

  const toggleSelection = (item: string, list: string[], setter: (list: string[]) => void) => {
    if (list.includes(item)) {
      setter(list.filter((i) => i !== item))
    } else {
      setter([...list, item])
    }
  }

  const clearAllFilters = () => {
    setSelectedJobTypes([])
    setSelectedExperience([])
    setSelectedIndustries([])
    setSalaryRange([50000, 150000])
    setIsRemoteOnly(false)
  }

  const handleSearch = () => {
    const params = new URLSearchParams()

    if (searchQuery.trim()) {
      params.set('q', searchQuery.trim())
    }

    if (location.trim()) {
      params.set('location', location.trim())
    }

    if (isRemoteOnly) {
      params.set('remote', 'true')
    }

    if (selectedJobTypes.length > 0) {
      params.set('types', selectedJobTypes.join(','))
    }

    if (selectedExperience.length > 0) {
      params.set('experience', selectedExperience.join(','))
    }

    if (selectedIndustries.length > 0) {
      params.set('industries', selectedIndustries.join(','))
    }

    if (salaryRange[0] > 50000 || salaryRange[1] < 150000) {
      params.set('salary_min', salaryRange[0].toString())
      params.set('salary_max', salaryRange[1].toString())
    }

    const queryString = params.toString()
    router.push(`/jobs${queryString ? `?${queryString}` : ''}`)
  }

  const activeFiltersCount =
    selectedJobTypes.length + selectedExperience.length + selectedIndustries.length + (isRemoteOnly ? 1 : 0)

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Smart Job <span className="text-primary">Discovery</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Advanced search with AI-powered suggestions and real-time filtering
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          {/* Main Search Bar */}
          <Card className="glass mb-8">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                <div className="md:col-span-5 relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                  <Input
                    placeholder="Job title, keywords, or company"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-12 h-12 text-lg border-0 bg-muted/50 focus:bg-background"
                  />

                  {/* Search Suggestions */}
                  <AnimatePresence>
                    {showSuggestions && searchSuggestions.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="absolute top-full left-0 right-0 z-10 bg-background border border-border rounded-lg shadow-lg mt-1"
                      >
                        {searchSuggestions.map((suggestion, index) => (
                          <motion.button
                            key={suggestion}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: index * 0.05 }}
                            onClick={() => {
                              setSearchQuery(suggestion)
                              setShowSuggestions(false)
                            }}
                            className="w-full text-left px-4 py-3 hover:bg-muted transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg"
                          >
                            <div className="flex items-center space-x-2">
                              <Sparkles className="w-4 h-4 text-primary" />
                              <span>{suggestion}</span>
                            </div>
                          </motion.button>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                <div className="md:col-span-4 relative">
                  <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                  <Input
                    placeholder="City, state, or remote"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="pl-12 h-12 text-lg border-0 bg-muted/50 focus:bg-background"
                  />
                </div>

                <div className="md:col-span-2">
                  <Button
                    size="lg"
                    className="w-full h-12 text-lg font-semibold"
                    onClick={handleSearch}
                  >
                    <Search className="w-5 h-5 mr-2" />
                    Search
                  </Button>
                </div>

                <div className="md:col-span-1">
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => setShowFilters(!showFilters)}
                    className="w-full h-12 relative"
                  >
                    <Filter className="w-5 h-5" />
                    {activeFiltersCount > 0 && (
                      <Badge className="absolute -top-2 -right-2 w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">
                        {activeFiltersCount}
                      </Badge>
                    )}
                  </Button>
                </div>
              </div>

              {/* Popular Searches */}
              <div className="mt-6">
                <span className="text-sm text-muted-foreground mb-3 block">Popular searches:</span>
                <div className="flex flex-wrap gap-2">
                  {popularSearches.slice(0, 6).map((search) => (
                    <motion.button
                      key={search}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSearchQuery(search)}
                      className="px-3 py-1 text-sm bg-primary/10 text-primary rounded-full hover:bg-primary/20 transition-colors duration-200"
                    >
                      {search}
                    </motion.button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Advanced Filters */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <Card className="glass mb-8">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-semibold">Advanced Filters</h3>
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                          Clear All
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => setShowFilters(false)}>
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                      {/* Salary Range */}
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-primary" />
                          <span className="font-medium">Salary Range</span>
                        </div>
                        <div className="px-2">
                          <Slider
                            value={salaryRange}
                            onValueChange={setSalaryRange}
                            max={200000}
                            min={30000}
                            step={5000}
                            className="w-full"
                          />
                          <div className="flex justify-between text-sm text-muted-foreground mt-2">
                            <span>${salaryRange[0].toLocaleString()}</span>
                            <span>${salaryRange[1].toLocaleString()}</span>
                          </div>
                        </div>
                      </div>

                      {/* Job Type */}
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Briefcase className="w-4 h-4 text-primary" />
                          <span className="font-medium">Job Type</span>
                        </div>
                        <div className="space-y-2">
                          {jobTypes.map((type) => (
                            <label key={type} className="flex items-center space-x-2 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={selectedJobTypes.includes(type)}
                                onChange={() => toggleSelection(type, selectedJobTypes, setSelectedJobTypes)}
                                className="rounded border-border"
                              />
                              <span className="text-sm">{type}</span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Experience Level */}
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-primary" />
                          <span className="font-medium">Experience Level</span>
                        </div>
                        <div className="space-y-2">
                          {experienceLevels.map((level) => (
                            <label key={level} className="flex items-center space-x-2 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={selectedExperience.includes(level)}
                                onChange={() => toggleSelection(level, selectedExperience, setSelectedExperience)}
                                className="rounded border-border"
                              />
                              <span className="text-sm">{level}</span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Industry */}
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Building2 className="w-4 h-4 text-primary" />
                          <span className="font-medium">Industry</span>
                        </div>
                        <div className="space-y-2">
                          {industries.map((industry) => (
                            <label key={industry} className="flex items-center space-x-2 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={selectedIndustries.includes(industry)}
                                onChange={() => toggleSelection(industry, selectedIndustries, setSelectedIndustries)}
                                className="rounded border-border"
                              />
                              <span className="text-sm">{industry}</span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Remote Work */}
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <MapPin className="w-4 h-4 text-primary" />
                          <span className="font-medium">Work Arrangement</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch checked={isRemoteOnly} onCheckedChange={setIsRemoteOnly} />
                          <span className="text-sm">Remote only</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end mt-6">
                      <Button onClick={handleSearch}>Apply Filters</Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Active Filters Display */}
          {activeFiltersCount > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex flex-wrap gap-2 mb-8"
            >
              {selectedJobTypes.map((type) => (
                <Badge key={type} variant="secondary" className="px-3 py-1">
                  {type}
                  <button
                    onClick={() => toggleSelection(type, selectedJobTypes, setSelectedJobTypes)}
                    className="ml-2 hover:text-destructive"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
              {selectedExperience.map((level) => (
                <Badge key={level} variant="secondary" className="px-3 py-1">
                  {level}
                  <button
                    onClick={() => toggleSelection(level, selectedExperience, setSelectedExperience)}
                    className="ml-2 hover:text-destructive"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
              {selectedIndustries.map((industry) => (
                <Badge key={industry} variant="secondary" className="px-3 py-1">
                  {industry}
                  <button
                    onClick={() => toggleSelection(industry, selectedIndustries, setSelectedIndustries)}
                    className="ml-2 hover:text-destructive"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
              {isRemoteOnly && (
                <Badge variant="secondary" className="px-3 py-1">
                  Remote Only
                  <button onClick={() => setIsRemoteOnly(false)} className="ml-2 hover:text-destructive">
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  )
}
