'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useJobsStore, useApplicationsStore, useCompaniesStore, useAuthStore } from '@/stores'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { JobCard } from '@/components/jobs/job-card'
import { ApplicationCard } from '@/components/applications/application-card'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { 
  Briefcase, 
  Users, 
  TrendingUp, 
  Clock,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
  Calendar,
  Target,
  Award,
  Building
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

interface RecruiterDashboardProps {
  className?: string
}

interface DashboardStats {
  activeJobs: number
  totalApplications: number
  pendingReviews: number
  interviewsScheduled: number
  hiredCandidates: number
  responseRate: number
}

export function RecruiterDashboard({ className }: RecruiterDashboardProps) {
  const router = useRouter()
  const { user } = useAuthStore()
  const { 
    jobs, 
    getJobsByCompany, 
    jobsLoading, 
    error: jobsError,
    clearError: clearJobsError 
  } = useJobsStore()
  
  const { 
    applications, 
    getApplicationsByCompany, 
    applicationsLoading,
    error: applicationsError,
    clearApplicationsError 
  } = useApplicationsStore()
  
  const { 
    currentCompany, 
    getCompanyById, 
    companyLoading 
  } = useCompaniesStore()

  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState<DashboardStats>({
    activeJobs: 0,
    totalApplications: 0,
    pendingReviews: 0,
    interviewsScheduled: 0,
    hiredCandidates: 0,
    responseRate: 0
  })

  // Load data on mount
  useEffect(() => {
    if (user?.companyId) {
      getCompanyById(user.companyId)
      getJobsByCompany(user.companyId)
      getApplicationsByCompany(user.companyId)
    }
  }, [user?.companyId, getCompanyById, getJobsByCompany, getApplicationsByCompany])

  // Calculate stats when data changes
  useEffect(() => {
    const activeJobs = jobs.filter(job => job.isActive).length
    const totalApplications = applications.length
    const pendingReviews = applications.filter(app => 
      ['submitted', 'under_review'].includes(app.status)
    ).length
    const interviewsScheduled = applications.filter(app => 
      app.status === 'interview_scheduled'
    ).length
    const hiredCandidates = applications.filter(app => 
      app.status === 'offer_extended'
    ).length
    const responseRate = totalApplications > 0 
      ? Math.round(((totalApplications - pendingReviews) / totalApplications) * 100)
      : 0

    setStats({
      activeJobs,
      totalApplications,
      pendingReviews,
      interviewsScheduled,
      hiredCandidates,
      responseRate
    })
  }, [jobs, applications])

  // Get recent applications
  const recentApplications = applications
    .sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())
    .slice(0, 5)

  // Get active jobs
  const activeJobs = jobs.filter(job => job.isActive).slice(0, 6)

  // Loading state
  if (companyLoading || (jobsLoading && !jobs.length)) {
    return <PageLoader message="Loading dashboard..." />
  }

  // No company state
  if (!user?.companyId) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <Building className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <CardTitle>No Company Profile</CardTitle>
            <CardDescription>
              You need to create a company profile before accessing the recruiter dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={() => router.push('/company/create')}>
              Create Company Profile
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Recruiter Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your job postings and review applications
          </p>
        </div>
        <Button onClick={() => router.push('/jobs/create')}>
          <Plus className="w-4 h-4 mr-2" />
          Post New Job
        </Button>
      </div>

      {/* Company Info */}
      {currentCompany && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {currentCompany.logo ? (
                  <img
                    src={currentCompany.logo}
                    alt={`${currentCompany.name} logo`}
                    className="w-12 h-12 rounded-lg object-cover border"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center border">
                    <Building className="w-6 h-6 text-muted-foreground" />
                  </div>
                )}
                <div>
                  <CardTitle>{currentCompany.name}</CardTitle>
                  <CardDescription>
                    {currentCompany.industry} • {currentCompany.location.city}, {currentCompany.location.state}
                  </CardDescription>
                </div>
              </div>
              <Button
                variant="outline"
                onClick={() => router.push(`/company/${currentCompany._id}/edit`)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Profile
              </Button>
            </div>
          </CardHeader>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Jobs</p>
                <p className="text-2xl font-bold">{stats.activeJobs}</p>
              </div>
              <Briefcase className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Applications</p>
                <p className="text-2xl font-bold">{stats.totalApplications}</p>
              </div>
              <Users className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Review</p>
                <p className="text-2xl font-bold">{stats.pendingReviews}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Interviews</p>
                <p className="text-2xl font-bold">{stats.interviewsScheduled}</p>
              </div>
              <Calendar className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Offers</p>
                <p className="text-2xl font-bold">{stats.hiredCandidates}</p>
              </div>
              <Award className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Response Rate</p>
                <p className="text-2xl font-bold">{stats.responseRate}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-indigo-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Alerts */}
      {jobsError && (
        <ErrorAlert
          type="error"
          title="Failed to Load Jobs"
          message={jobsError.message}
          dismissible
          onDismiss={clearJobsError}
        />
      )}

      {applicationsError && (
        <ErrorAlert
          type="error"
          title="Failed to Load Applications"
          message={applicationsError.message}
          dismissible
          onDismiss={clearApplicationsError}
        />
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="jobs">
            Jobs ({stats.activeJobs})
          </TabsTrigger>
          <TabsTrigger value="applications">
            Applications ({stats.totalApplications})
          </TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Applications */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Recent Applications</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveTab('applications')}
                  >
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {applicationsLoading ? (
                  <div className="space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-16 bg-muted rounded-lg"></div>
                      </div>
                    ))}
                  </div>
                ) : recentApplications.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground">No applications yet</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentApplications.map((application) => (
                      <div
                        key={application._id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                            <Users className="w-4 h-4 text-primary" />
                          </div>
                          <div>
                            <p className="font-medium">{application.job?.title}</p>
                            <p className="text-sm text-muted-foreground">
                              {new Date(application.submittedAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline">
                          {application.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Active Jobs */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Active Job Postings</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveTab('jobs')}
                  >
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {jobsLoading ? (
                  <div className="space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-16 bg-muted rounded-lg"></div>
                      </div>
                    ))}
                  </div>
                ) : activeJobs.length === 0 ? (
                  <div className="text-center py-8">
                    <Briefcase className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground mb-3">No active jobs</p>
                    <Button
                      size="sm"
                      onClick={() => router.push('/jobs/create')}
                    >
                      Post Your First Job
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {activeJobs.slice(0, 3).map((job) => (
                      <div
                        key={job._id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <Briefcase className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">{job.title}</p>
                            <p className="text-sm text-muted-foreground">
                              {job.applicationsCount} applications
                            </p>
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => router.push(`/jobs/${job._id}`)}>
                              <Eye className="w-4 h-4 mr-2" />
                              View Job
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/jobs/${job._id}/edit`)}>
                              <Edit className="w-4 h-4 mr-2" />
                              Edit Job
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => router.push(`/jobs/${job._id}/applications`)}>
                              <Users className="w-4 h-4 mr-2" />
                              View Applications
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Jobs Tab */}
        <TabsContent value="jobs" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Job Postings</h2>
            <Button onClick={() => router.push('/jobs/create')}>
              <Plus className="w-4 h-4 mr-2" />
              Post New Job
            </Button>
          </div>

          {activeJobs.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Active Jobs</h3>
                <p className="text-muted-foreground mb-4">
                  Start attracting candidates by posting your first job.
                </p>
                <Button onClick={() => router.push('/jobs/create')}>
                  Post Your First Job
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {activeJobs.map((job) => (
                <JobCard
                  key={job._id}
                  job={job}
                  showCompanyLogo={false}
                  actions={
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/jobs/${job._id}/applications`)}
                      >
                        <Users className="w-4 h-4 mr-1" />
                        {job.applicationsCount}
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => router.push(`/jobs/${job._id}`)}>
                            <Eye className="w-4 h-4 mr-2" />
                            View Job
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/jobs/${job._id}/edit`)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit Job
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  }
                />
              ))}
            </div>
          )}
        </TabsContent>

        {/* Applications Tab */}
        <TabsContent value="applications" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Applications</h2>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">
                {stats.pendingReviews} pending review
              </Badge>
            </div>
          </div>

          {applications.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Applications Yet</h3>
                <p className="text-muted-foreground">
                  Applications will appear here once candidates start applying to your jobs.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {applications.map((application) => (
                <ApplicationCard
                  key={application._id}
                  application={application}
                  onView={() => router.push(`/applications/${application._id}`)}
                  showJobTitle
                />
              ))}
            </div>
          )}
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="text-center py-12">
            <TrendingUp className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Analytics Coming Soon</h3>
            <p className="text-muted-foreground">
              Detailed hiring analytics and performance metrics will be available here.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
