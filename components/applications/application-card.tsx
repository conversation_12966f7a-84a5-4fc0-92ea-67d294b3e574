'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useApplicationsStore, type Application } from '@/stores'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ButtonLoading } from '@/components/ui/button-loading'
import { 
  Building, 
  MapPin, 
  Calendar, 
  Clock,
  DollarSign,
  ExternalLink,
  MoreHorizontal,
  Eye,
  Trash2,
  AlertTriangle
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { cn } from '@/lib/utils'

interface ApplicationCardProps {
  application: Application
  onView?: () => void
  className?: string
}

export function ApplicationCard({ application, onView, className }: ApplicationCardProps) {
  const router = useRouter()
  const { withdrawApplication, withdrawLoading } = useApplicationsStore()
  
  const [showWithdrawDialog, setShowWithdrawDialog] = useState(false)

  // Format status display
  const getStatusDisplay = (status: Application['status']) => {
    const statusMap = {
      submitted: { label: 'Submitted', color: 'bg-blue-100 text-blue-800 border-blue-200' },
      under_review: { label: 'Under Review', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      interview_scheduled: { label: 'Interview Scheduled', color: 'bg-purple-100 text-purple-800 border-purple-200' },
      interviewed: { label: 'Interviewed', color: 'bg-indigo-100 text-indigo-800 border-indigo-200' },
      offer_extended: { label: 'Offer Extended', color: 'bg-green-100 text-green-800 border-green-200' },
      rejected: { label: 'Rejected', color: 'bg-red-100 text-red-800 border-red-200' },
      withdrawn: { label: 'Withdrawn', color: 'bg-gray-100 text-gray-800 border-gray-200' }
    }
    return statusMap[status] || statusMap.submitted
  }

  // Format salary display
  const formatSalary = () => {
    if (!application.job.salary?.min && !application.job.salary?.max) return null
    
    const formatAmount = (amount: number) => {
      if (amount >= 1000000) return `${(amount / 1000000).toFixed(1)}M`
      if (amount >= 1000) return `${(amount / 1000).toFixed(0)}K`
      return amount.toString()
    }

    const period = application.job.salary.period === 'yearly' ? '/year' : 
                   application.job.salary.period === 'monthly' ? '/month' : '/hour'

    if (application.job.salary.min && application.job.salary.max) {
      return `$${formatAmount(application.job.salary.min)} - $${formatAmount(application.job.salary.max)}${period}`
    } else if (application.job.salary.min) {
      return `$${formatAmount(application.job.salary.min)}+${period}`
    } else if (application.job.salary.max) {
      return `Up to $${formatAmount(application.job.salary.max)}${period}`
    }
    return null
  }

  // Format date
  const formatDate = (date: Date) => {
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffInDays === 0) return 'Today'
    if (diffInDays === 1) return 'Yesterday'
    if (diffInDays < 7) return `${diffInDays} days ago`
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`
    return new Date(date).toLocaleDateString()
  }

  // Handle withdraw application
  const handleWithdraw = async () => {
    try {
      await withdrawApplication(application._id, 'Withdrawn by user')
      setShowWithdrawDialog(false)
    } catch (error) {
      console.error('Failed to withdraw application:', error)
    }
  }

  // Get latest timeline event
  const getLatestUpdate = () => {
    if (!application.timeline.length) return null
    return application.timeline[application.timeline.length - 1]
  }

  const statusDisplay = getStatusDisplay(application.status)
  const latestUpdate = getLatestUpdate()
  const canWithdraw = !['rejected', 'withdrawn', 'offer_extended'].includes(application.status)

  return (
    <>
      <Card className={cn('hover:shadow-md transition-shadow cursor-pointer', className)}>
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 flex-1" onClick={onView}>
              {/* Company Logo */}
              <div className="flex-shrink-0">
                {application.job.company.logo ? (
                  <img
                    src={application.job.company.logo}
                    alt={`${application.job.company.name} logo`}
                    className="w-12 h-12 rounded-lg object-cover border"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center border">
                    <Building className="w-6 h-6 text-muted-foreground" />
                  </div>
                )}
              </div>

              {/* Job Info */}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg leading-tight mb-1 truncate">
                  {application.job.title}
                </h3>
                <div className="flex items-center space-x-2 text-muted-foreground mb-2">
                  <span className="font-medium">{application.job.company.name}</span>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span className="text-sm">
                      {application.job.location.remote ? 'Remote' : 
                       `${application.job.location.city}, ${application.job.location.state}`}
                    </span>
                  </div>
                </div>
                
                {/* Job Type & Salary */}
                <div className="flex items-center space-x-4 mb-2">
                  <Badge variant="outline" className="text-xs">
                    {application.job.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Badge>
                  {formatSalary() && (
                    <div className="flex items-center space-x-1 text-green-600 text-sm">
                      <DollarSign className="w-3 h-3" />
                      <span>{formatSalary()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Status & Actions */}
            <div className="flex items-start space-x-2 ml-4">
              <Badge className={statusDisplay.color} variant="outline">
                {statusDisplay.label}
              </Badge>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={onView}>
                    <Eye className="w-4 h-4 mr-2" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => router.push(`/jobs/${application.jobId}`)}>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Job Posting
                  </DropdownMenuItem>
                  {canWithdraw && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => setShowWithdrawDialog(true)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Withdraw Application
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0" onClick={onView}>
          {/* Application Timeline */}
          <div className="space-y-3">
            {/* Submitted Date */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2 text-muted-foreground">
                <Calendar className="w-4 h-4" />
                <span>Applied {formatDate(application.submittedAt)}</span>
              </div>
              
              {/* Latest Update */}
              {latestUpdate && latestUpdate.type !== 'submitted' && (
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span>Updated {formatDate(latestUpdate.createdAt)}</span>
                </div>
              )}
            </div>

            {/* Latest Update Message */}
            {latestUpdate && latestUpdate.message && latestUpdate.type !== 'submitted' && (
              <div className="bg-muted/50 rounded-lg p-3">
                <p className="text-sm text-muted-foreground">
                  <strong>Latest Update:</strong> {latestUpdate.message}
                </p>
              </div>
            )}

            {/* Quick Actions */}
            <div className="flex items-center justify-between pt-2 border-t">
              <div className="text-xs text-muted-foreground">
                Application ID: {application._id.slice(-8)}
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    router.push(`/jobs/${application.jobId}`)
                  }}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  View Job
                </Button>
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    if (onView) onView()
                  }}
                >
                  <Eye className="w-3 h-3 mr-1" />
                  Details
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Withdraw Confirmation Dialog */}
      <AlertDialog open={showWithdrawDialog} onOpenChange={setShowWithdrawDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-600" />
              <span>Withdraw Application</span>
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to withdraw your application for{' '}
              <strong>{application.job.title}</strong> at{' '}
              <strong>{application.job.company.name}</strong>?
              <br /><br />
              This action cannot be undone, and you'll need to reapply if you change your mind.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <ButtonLoading
              loading={withdrawLoading}
              loadingText="Withdrawing..."
              onClick={handleWithdraw}
              variant="destructive"
            >
              Withdraw Application
            </ButtonLoading>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
