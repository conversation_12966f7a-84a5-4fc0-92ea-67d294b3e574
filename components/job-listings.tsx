"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"
import { JobCard } from "@/components/job-card"

const featuredJobs = [
  {
    id: 1,
    title: "Senior Frontend Developer",
    company: "TechCorp Inc.",
    location: "San Francisco, CA",
    type: "Full-time",
    salary: "$120k - $160k",
    tags: ["React", "TypeScript", "Next.js"],
    logo: "/placeholder.svg?height=60&width=60",
    posted: "2 days ago",
    featured: true,
  },
  {
    id: 2,
    title: "Product Manager",
    company: "InnovateLab",
    location: "Remote",
    type: "Full-time",
    salary: "$100k - $140k",
    tags: ["Strategy", "Analytics", "Leadership"],
    logo: "/placeholder.svg?height=60&width=60",
    posted: "1 day ago",
    featured: true,
  },
  {
    id: 3,
    title: "UX Designer",
    company: "DesignStudio",
    location: "New York, NY",
    type: "Contract",
    salary: "$80k - $110k",
    tags: ["Figma", "Prototyping", "User Research"],
    logo: "/placeholder.svg?height=60&width=60",
    posted: "3 days ago",
    featured: false,
  },
  {
    id: 4,
    title: "DevOps Engineer",
    company: "CloudTech",
    location: "Austin, TX",
    type: "Full-time",
    salary: "$110k - $150k",
    tags: ["AWS", "Docker", "Kubernetes"],
    logo: "/placeholder.svg?height=60&width=60",
    posted: "1 week ago",
    featured: false,
  },
  {
    id: 5,
    title: "Data Scientist",
    company: "DataCorp",
    location: "Seattle, WA",
    type: "Full-time",
    salary: "$130k - $170k",
    tags: ["Python", "ML", "Statistics"],
    logo: "/placeholder.svg?height=60&width=60",
    posted: "4 days ago",
    featured: true,
  },
  {
    id: 6,
    title: "Mobile Developer",
    company: "AppWorks",
    location: "Los Angeles, CA",
    type: "Full-time",
    salary: "$95k - $125k",
    tags: ["React Native", "iOS", "Android"],
    logo: "/placeholder.svg?height=60&width=60",
    posted: "5 days ago",
    featured: false,
  },
]

export function JobListings() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Featured <span className="text-primary">Opportunities</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover hand-picked positions from top companies looking for talented professionals like you.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
        >
          {featuredJobs.map((job, index) => (
            <JobCard key={job.id} job={job} index={index} />
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center"
        >
          <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
            View All Jobs
            <ExternalLink className="w-5 h-5 ml-2" />
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
