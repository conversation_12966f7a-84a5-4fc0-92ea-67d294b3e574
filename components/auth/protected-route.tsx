'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { Button } from '@/components/ui/button'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'admin' | 'company_admin' | 'recruiter' | 'job_seeker'
  fallbackPath?: string
  showLoader?: boolean
}

export function ProtectedRoute({ 
  children, 
  requiredRole,
  fallbackPath = '/login',
  showLoader = true
}: ProtectedRouteProps) {
  const router = useRouter()
  const { user, isAuthenticated, checkAuthStatus, isLoading } = useAuthStore()
  const [isChecking, setIsChecking] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    const checkAccess = async () => {
      try {
        // Check authentication status if not already authenticated
        if (!isAuthenticated) {
          await checkAuthStatus()
        }

        // Wait a moment for auth state to update
        setTimeout(() => {
          const currentUser = useAuthStore.getState().user
          const currentAuth = useAuthStore.getState().isAuthenticated

          if (!currentAuth || !currentUser) {
            // Not authenticated, redirect to login
            router.push(fallbackPath)
            return
          }

          // Check role-based access if required
          if (requiredRole && currentUser.role !== requiredRole) {
            setHasAccess(false)
            setIsChecking(false)
            return
          }

          // User has access
          setHasAccess(true)
          setIsChecking(false)
        }, 100)

      } catch (error) {
        console.error('Auth check failed:', error)
        router.push(fallbackPath)
      }
    }

    checkAccess()
  }, [isAuthenticated, user, requiredRole, router, fallbackPath, checkAuthStatus])

  // Show loading state
  if (isChecking || isLoading) {
    return showLoader ? (
      <PageLoader 
        message="Checking authentication..." 
        fullScreen 
        size="lg" 
      />
    ) : null
  }

  // Show access denied for role-based restrictions
  if (!hasAccess && requiredRole) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full space-y-4">
          <ErrorAlert
            type="warning"
            title="Access Denied"
            message={`You need ${requiredRole.replace('_', ' ')} privileges to access this page.`}
          />
          <div className="flex space-x-2">
            <Button 
              onClick={() => router.back()}
              variant="outline"
              className="flex-1"
            >
              Go Back
            </Button>
            <Button 
              onClick={() => router.push('/dashboard')}
              className="flex-1"
            >
              Dashboard
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Render protected content
  return <>{children}</>
}

// Higher-order component for protecting pages
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) {
  const AuthenticatedComponent = (props: P) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  )

  AuthenticatedComponent.displayName = `withAuth(${Component.displayName || Component.name})`
  
  return AuthenticatedComponent
}

// Hook for checking authentication in components
export function useRequireAuth(requiredRole?: string) {
  const { user, isAuthenticated } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    if (requiredRole && user?.role !== requiredRole) {
      router.push('/dashboard')
      return
    }
  }, [isAuthenticated, user, requiredRole, router])

  return {
    isAuthenticated,
    user,
    hasRequiredRole: !requiredRole || user?.role === requiredRole
  }
}
