"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { MessageCircle, X, Send, Bot, Sparkles, HelpCircle, Search, User } from "lucide-react"
import { Input } from "@/components/ui/input"

const quickActions = [
  { icon: Search, label: "Find Jobs", action: "search" },
  { icon: User, label: "Profile Help", action: "profile" },
  { icon: HelpCircle, label: "Get Support", action: "support" },
]

const chatMessages = [
  {
    id: 1,
    type: "bot",
    message: "Hi! I'm your AI career assistant. How can I help you today?",
    timestamp: new Date(),
  },
]

export function FloatingActionButton() {
  const [isOpen, setIsOpen] = useState(false)
  const [showChat, setShowChat] = useState(false)
  const [messages, setMessages] = useState(chatMessages)
  const [inputMessage, setInputMessage] = useState("")

  const handleQuickAction = (action: string) => {
    switch (action) {
      case "search":
        // Scroll to search section
        document.getElementById("job-search")?.scrollIntoView({ behavior: "smooth" })
        break
      case "profile":
        // Open profile help
        setShowChat(true)
        addMessage("bot", "I can help you optimize your profile! What specific area would you like to improve?")
        break
      case "support":
        // Open support chat
        setShowChat(true)
        addMessage("bot", "I'm here to help! What questions do you have about using JobPortal?")
        break
    }
    setIsOpen(false)
  }

  const addMessage = (type: "user" | "bot", message: string) => {
    const newMessage = {
      id: messages.length + 1,
      type,
      message,
      timestamp: new Date(),
    }
    setMessages((prev) => [...prev, newMessage])
  }

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return

    addMessage("user", inputMessage)
    setInputMessage("")

    // Simulate bot response
    setTimeout(() => {
      const responses = [
        "That's a great question! Let me help you with that.",
        "I understand what you're looking for. Here are some suggestions...",
        "Based on your profile, I recommend focusing on these areas...",
        "Let me connect you with the right resources for that.",
      ]
      const randomResponse = responses[Math.floor(Math.random() * responses.length)]
      addMessage("bot", randomResponse)
    }, 1000)
  }

  return (
    <>
      {/* Chat Interface */}
      <AnimatePresence>
        {showChat && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="fixed bottom-24 right-6 z-50 w-80 h-96"
          >
            <Card className="glass h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-border/50">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                    <Bot className="w-4 h-4 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-sm">AI Assistant</h3>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-xs text-muted-foreground">Online</span>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="icon" onClick={() => setShowChat(false)} className="h-8 w-8">
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg text-sm ${
                        message.type === "user" ? "bg-primary text-primary-foreground" : "bg-muted"
                      }`}
                    >
                      {message.message}
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="p-4 border-t border-border/50">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Type your message..."
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                    className="flex-1"
                  />
                  <Button size="icon" onClick={handleSendMessage}>
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Actions Menu */}
      <AnimatePresence>
        {isOpen && !showChat && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed bottom-24 right-6 z-50"
          >
            <Card className="glass p-4">
              <CardContent className="p-0 space-y-2">
                {quickActions.map((action, index) => (
                  <motion.button
                    key={action.action}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => handleQuickAction(action.action)}
                    className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-muted transition-colors duration-200 text-left"
                  >
                    <action.icon className="w-5 h-5 text-primary" />
                    <span className="font-medium">{action.label}</span>
                  </motion.button>
                ))}
                <motion.button
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  onClick={() => setShowChat(true)}
                  className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-muted transition-colors duration-200 text-left"
                >
                  <MessageCircle className="w-5 h-5 text-primary" />
                  <span className="font-medium">Chat with AI</span>
                </motion.button>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB */}
      <motion.div className="fixed bottom-6 right-6 z-50" whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
        <Button
          size="icon"
          onClick={() => setIsOpen(!isOpen)}
          className="w-14 h-14 rounded-full shadow-lg bg-primary hover:bg-primary/90 relative overflow-hidden"
        >
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <X className="w-6 h-6" />
              </motion.div>
            ) : (
              <motion.div
                key="sparkles"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="relative"
              >
                <Sparkles className="w-6 h-6" />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                  className="absolute inset-0 bg-primary-foreground/20 rounded-full"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </Button>
      </motion.div>
    </>
  )
}
