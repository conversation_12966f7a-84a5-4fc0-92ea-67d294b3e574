'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import {
  MapPin,
  DollarSign,
  Clock,
  Star,
  Briefcase,
  GraduationCap,
  Code,
  Palette,
  Database,
  Smartphone,
  Globe,
  Shield,
  ChevronDown,
  ChevronUp,
  X,
  Filter,
  Users,
  Calculator,
  TrendingUp,
  Scale,
  Megaphone,
  Settings,
  Heart,
  Building,
  PieChart,
  FileText,
  Zap
} from 'lucide-react'

interface TalentFiltersProps {
  selectedSkills: string[]
  setSelectedSkills: (skills: string[]) => void
  experienceRange: number[]
  setExperienceRange: (range: number[]) => void
  salaryRange: number[]
  setSalaryRange: (range: number[]) => void
  onFiltersChange?: () => void
  showApplyButton?: boolean
}

export function TalentFilters({
  selectedSkills,
  setSelectedSkills,
  experienceRange,
  setExperienceRange,
  salaryRange,
  setSalaryRange,
  onFiltersChange,
  showApplyButton = true
}: TalentFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    skills: true,
    experience: true,
    salary: true,
    location: true,
    availability: true,
    rating: true
  })

  const [selectedLocation, setSelectedLocation] = useState('')
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>([])
  const [minRating, setMinRating] = useState(0)

  const skillCategories = [
    {
      name: 'Frontend Development',
      icon: Code,
      skills: ['React', 'Vue.js', 'Angular', 'TypeScript', 'JavaScript', 'HTML/CSS', 'Tailwind CSS', 'Next.js']
    },
    {
      name: 'Backend Development',
      icon: Database,
      skills: ['Node.js', 'Python', 'Java', 'C#', 'PHP', 'Ruby', 'Go', 'Rust']
    },
    {
      name: 'Design & UX',
      icon: Palette,
      skills: ['Figma', 'Adobe XD', 'Sketch', 'Photoshop', 'Illustrator', 'User Research', 'Prototyping', 'Design Systems']
    },
    {
      name: 'Mobile Development',
      icon: Smartphone,
      skills: ['React Native', 'Flutter', 'iOS', 'Android', 'Swift', 'Kotlin', 'Xamarin', 'Ionic']
    },
    {
      name: 'Data & AI',
      icon: Database,
      skills: ['Python', 'Machine Learning', 'TensorFlow', 'PyTorch', 'SQL', 'R', 'Data Science', 'AI/ML']
    },
    {
      name: 'DevOps & Cloud',
      icon: Globe,
      skills: ['AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'CI/CD', 'Terraform', 'Jenkins']
    },
    {
      name: 'Blockchain & Web3',
      icon: Shield,
      skills: ['Solidity', 'Web3', 'Ethereum', 'Smart Contracts', 'DeFi', 'NFTs', 'Blockchain', 'Crypto']
    },
    {
      name: 'Human Resources',
      icon: Users,
      skills: ['Recruitment', 'Talent Acquisition', 'HRIS', 'Performance Management', 'Employee Relations', 'Compensation', 'Training & Development', 'HR Analytics']
    },
    {
      name: 'Accounting & Finance',
      icon: Calculator,
      skills: ['Financial Analysis', 'Bookkeeping', 'Tax Preparation', 'Auditing', 'QuickBooks', 'Excel', 'Financial Planning', 'Budget Management']
    },
    {
      name: 'Marketing & Sales',
      icon: Megaphone,
      skills: ['Digital Marketing', 'SEO/SEM', 'Social Media', 'Content Marketing', 'Sales Strategy', 'CRM', 'Email Marketing', 'Analytics']
    },
    {
      name: 'Legal & Compliance',
      icon: Scale,
      skills: ['Contract Law', 'Corporate Law', 'Compliance', 'Legal Research', 'Intellectual Property', 'Employment Law', 'Regulatory Affairs', 'Risk Management']
    },
    {
      name: 'Operations & Management',
      icon: Settings,
      skills: ['Project Management', 'Process Improvement', 'Supply Chain', 'Quality Assurance', 'Lean Six Sigma', 'Operations Strategy', 'Vendor Management', 'KPI Management']
    },
    {
      name: 'Healthcare & Medical',
      icon: Heart,
      skills: ['Clinical Research', 'Medical Writing', 'Healthcare Administration', 'Nursing', 'Telemedicine', 'Medical Coding', 'Patient Care', 'Healthcare IT']
    },
    {
      name: 'Business & Strategy',
      icon: TrendingUp,
      skills: ['Business Analysis', 'Strategic Planning', 'Market Research', 'Consulting', 'Business Development', 'Competitive Analysis', 'Financial Modeling', 'Growth Strategy']
    },
    {
      name: 'Real Estate & Construction',
      icon: Building,
      skills: ['Real Estate Sales', 'Property Management', 'Construction Management', 'Architecture', 'Project Planning', 'Building Inspection', 'Real Estate Law', 'Property Valuation']
    },
    {
      name: 'Content & Writing',
      icon: FileText,
      skills: ['Content Writing', 'Copywriting', 'Technical Writing', 'Journalism', 'Editing', 'Proofreading', 'Grant Writing', 'Creative Writing']
    }
  ]

  const locations = [
    'San Francisco, CA', 'New York, NY', 'Los Angeles, CA', 'Seattle, WA', 'Austin, TX',
    'Boston, MA', 'Chicago, IL', 'Denver, CO', 'Miami, FL', 'Toronto, ON',
    'London, UK', 'Berlin, Germany', 'Amsterdam, Netherlands', 'Remote', 'Anywhere'
  ]

  const availabilityOptions = [
    { value: 'available', label: 'Available Now', color: 'bg-green-500' },
    { value: 'busy', label: 'Busy', color: 'bg-yellow-500' },
    { value: 'part-time', label: 'Part-time Only', color: 'bg-blue-500' },
    { value: 'full-time', label: 'Full-time Only', color: 'bg-purple-500' }
  ]

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const toggleSkill = (skill: string) => {
    setSelectedSkills(
      selectedSkills.includes(skill)
        ? selectedSkills.filter(s => s !== skill)
        : [...selectedSkills, skill]
    )
  }

  const toggleAvailability = (availability: string) => {
    setSelectedAvailability(
      selectedAvailability.includes(availability)
        ? selectedAvailability.filter(a => a !== availability)
        : [...selectedAvailability, availability]
    )
  }

  const clearAllFilters = () => {
    setSelectedSkills([])
    setExperienceRange([0, 15])
    setSalaryRange([30000, 200000])
    setSelectedLocation('')
    setSelectedAvailability([])
    setMinRating(0)
  }

  const activeFilterCount = selectedSkills.length + 
    (experienceRange[0] > 0 || experienceRange[1] < 15 ? 1 : 0) +
    (salaryRange[0] > 30000 || salaryRange[1] < 200000 ? 1 : 0) +
    (selectedLocation ? 1 : 0) +
    selectedAvailability.length +
    (minRating > 0 ? 1 : 0)

  const FilterSection = ({ 
    title, 
    icon: Icon, 
    section, 
    children 
  }: { 
    title: string
    icon: any
    section: keyof typeof expandedSections
    children: React.ReactNode 
  }) => (
    <div className="space-y-3">
      <Button
        variant="ghost"
        onClick={() => toggleSection(section)}
        className="w-full justify-between p-0 h-auto font-medium text-left hover:bg-transparent"
      >
        <div className="flex items-center space-x-2">
          <Icon className="w-4 h-4 text-primary" />
          <span>{title}</span>
        </div>
        {expandedSections[section] ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </Button>
      
      <AnimatePresence>
        {expandedSections[section] && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
      
      <Separator />
    </div>
  )

  return (
    <Card className="card-premium">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-5 h-5 text-primary" />
            <CardTitle>Filters</CardTitle>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="theme-glow">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="w-4 h-4 mr-1" />
              Clear
            </Button>
          )}
        </div>
        <CardDescription>
          Find the perfect talent for your project
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Skills Filter */}
        <FilterSection title="Skills & Expertise" icon={Code} section="skills">
          <div className="space-y-4">
            {skillCategories.map((category) => (
              <div key={category.name} className="space-y-3">
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-primary/15 to-primary/10 border border-primary/20">
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <category.icon className="w-4 h-4 text-primary-foreground" />
                  </div>
                  <span className="font-semibold text-primary text-sm">{category.name}</span>
                </div>
                <div className="flex flex-wrap gap-2 pl-2">
                  {category.skills.map((skill) => (
                    <Badge
                      key={skill}
                      variant={selectedSkills.includes(skill) ? "default" : "outline"}
                      className={`cursor-pointer transition-all duration-200 ${
                        selectedSkills.includes(skill)
                          ? 'theme-glow bg-primary text-primary-foreground border-primary'
                          : 'hover:border-primary/50 hover:bg-primary/5'
                      }`}
                      onClick={() => toggleSkill(skill)}
                    >
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Experience Filter */}
        <FilterSection title="Experience Level" icon={Briefcase} section="experience">
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Years of Experience</span>
              <span className="font-medium">
                {experienceRange[0]} - {experienceRange[1] === 15 ? '15+' : experienceRange[1]} years
              </span>
            </div>
            <Slider
              value={experienceRange}
              onValueChange={setExperienceRange}
              max={15}
              min={0}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Entry Level</span>
              <span>Senior</span>
              <span>Expert</span>
            </div>
          </div>
        </FilterSection>

        {/* Salary Filter */}
        <FilterSection title="Hourly Rate" icon={DollarSign} section="salary">
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Rate Range</span>
              <span className="font-medium">
                ${salaryRange[0].toLocaleString()} - ${salaryRange[1].toLocaleString()}/hr
              </span>
            </div>
            <Slider
              value={salaryRange}
              onValueChange={setSalaryRange}
              max={200000}
              min={30000}
              step={5000}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>$30k</span>
              <span>$100k</span>
              <span>$200k+</span>
            </div>
          </div>
        </FilterSection>

        {/* Location Filter */}
        <FilterSection title="Location" icon={MapPin} section="location">
          <Select value={selectedLocation} onValueChange={setSelectedLocation}>
            <SelectTrigger className="input-enhanced">
              <SelectValue placeholder="Select location" />
            </SelectTrigger>
            <SelectContent>
              {locations.map((location) => (
                <SelectItem key={location} value={location}>
                  {location}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FilterSection>

        {/* Availability Filter */}
        <FilterSection title="Availability" icon={Clock} section="availability">
          <div className="space-y-2">
            {availabilityOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={option.value}
                  checked={selectedAvailability.includes(option.value)}
                  onCheckedChange={() => toggleAvailability(option.value)}
                />
                <Label htmlFor={option.value} className="flex items-center space-x-2 cursor-pointer">
                  <div className={`w-2 h-2 rounded-full ${option.color}`} />
                  <span>{option.label}</span>
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Rating Filter */}
        <FilterSection title="Minimum Rating" icon={Star} section="rating">
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Rating</span>
              <span className="font-medium">
                {minRating > 0 ? `${minRating}+ stars` : 'Any rating'}
              </span>
            </div>
            <Slider
              value={[minRating]}
              onValueChange={(value) => setMinRating(value[0])}
              max={5}
              min={0}
              step={0.5}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Any</span>
              <span>3+</span>
              <span>4+</span>
              <span>5 stars</span>
            </div>
          </div>
        </FilterSection>

        {showApplyButton && (
          <Button 
            onClick={onFiltersChange} 
            className="w-full button-premium"
          >
            Apply Filters
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
