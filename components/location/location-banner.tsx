'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { useLocationStore } from '@/stores/location-store'
import {
  MapPin,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  Navigation
} from 'lucide-react'

export function LocationBanner() {
  const {
    showLocationBanner,
    isLocationLoading,
    locationError,
    currentLocation,
    setLocationPermission,
    dismissLocationBanner
  } = useLocationStore()

  if (!showLocationBanner) return null

  const handleAllowLocation = () => {
    setLocationPermission(true)
  }

  const handleDenyLocation = () => {
    dismissLocationBanner()
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -60 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -60 }}
        transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
        className="fixed top-0 left-0 right-0 z-50"
      >
        {/* Sleek Minimalist Design */}
        <div className="bg-gradient-to-r from-primary/98 via-primary to-primary/98 backdrop-blur-xl border-b border-white/10 shadow-2xl">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="flex items-center justify-between py-3 sm:py-4">

              {/* Left Content - Icon & Text */}
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className="flex-shrink-0">
                  <div className="relative">
                    <div className="w-8 h-8 sm:w-10 sm:h-10 bg-white/15 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20">
                      {isLocationLoading ? (
                        <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 text-white animate-spin" />
                      ) : currentLocation ? (
                        <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                      ) : (
                        <Navigation className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                      )}
                    </div>
                    {/* Subtle pulse animation for attention */}
                    {!currentLocation && !isLocationLoading && (
                      <div className="absolute inset-0 w-8 h-8 sm:w-10 sm:h-10 bg-white/20 rounded-xl animate-pulse" />
                    )}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  {/* Mobile: Compact text */}
                  <div className="sm:hidden">
                    <p className="text-white font-semibold text-sm truncate">
                      {currentLocation
                        ? `${currentLocation.city}`
                        : isLocationLoading
                        ? 'Locating...'
                        : 'Enable Location'
                      }
                    </p>
                    {!currentLocation && !isLocationLoading && (
                      <p className="text-white/75 text-xs">
                        Find nearby jobs
                      </p>
                    )}
                  </div>

                  {/* Desktop: Full text */}
                  <div className="hidden sm:block">
                    <h3 className="text-white font-semibold text-base lg:text-lg mb-1">
                      {currentLocation
                        ? `Jobs in ${currentLocation.city}, ${currentLocation.country}`
                        : isLocationLoading
                        ? 'Detecting your location...'
                        : 'Discover Local Opportunities'
                      }
                    </h3>
                    <p className="text-white/80 text-sm">
                      {currentLocation
                        ? 'Showing personalized job recommendations'
                        : isLocationLoading
                        ? 'Please wait while we find your location'
                        : 'Allow location access for personalized job matches'
                      }
                    </p>
                  </div>

                  {/* Error state */}
                  {locationError && (
                    <div className="flex items-center space-x-2 mt-1">
                      <AlertCircle className="w-3 h-3 text-red-200 flex-shrink-0" />
                      <p className="text-red-200 text-xs truncate">
                        {locationError}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Right Actions */}
              <div className="flex items-center space-x-2 flex-shrink-0">
                {!currentLocation && !locationError && (
                  <Button
                    onClick={handleAllowLocation}
                    disabled={isLocationLoading}
                    size="sm"
                    className="bg-white/90 hover:bg-white text-primary hover:text-primary/90 font-medium transition-all duration-200 shadow-lg hover:shadow-xl border-0 px-3 sm:px-4"
                  >
                    {isLocationLoading ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      <>
                        <MapPin className="w-3 h-3 mr-1.5 sm:mr-2" />
                        <span className="hidden sm:inline">Allow</span>
                        <span className="sm:hidden">OK</span>
                      </>
                    )}
                  </Button>
                )}

                <Button
                  onClick={handleDenyLocation}
                  disabled={isLocationLoading}
                  size="sm"
                  variant="ghost"
                  className="text-white/90 hover:text-white hover:bg-white/10 transition-all duration-200 p-2"
                >
                  <X className="w-4 h-4" />
                  <span className="sr-only">Dismiss</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

// Ultra-compact version for embedded use
export function LocationBannerCompact() {
  const {
    showLocationBanner,
    isLocationLoading,
    locationError,
    currentLocation,
    setLocationPermission,
    dismissLocationBanner
  } = useLocationStore()

  if (!showLocationBanner) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -30 }}
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        className="fixed top-0 left-0 right-0 z-50"
      >
        <div className="bg-gradient-to-r from-primary/96 to-primary/96 backdrop-blur-lg border-b border-white/10 shadow-lg">
          <div className="container mx-auto px-4 py-2.5">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2.5 flex-1 min-w-0">
                <div className="w-7 h-7 bg-white/15 rounded-lg flex items-center justify-center backdrop-blur-sm">
                  {isLocationLoading ? (
                    <Loader2 className="w-3.5 h-3.5 text-white animate-spin" />
                  ) : currentLocation ? (
                    <CheckCircle className="w-3.5 h-3.5 text-white" />
                  ) : (
                    <Navigation className="w-3.5 h-3.5 text-white" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <p className="text-white font-medium text-sm truncate">
                    {currentLocation
                      ? `${currentLocation.city}, ${currentLocation.country}`
                      : isLocationLoading
                      ? 'Locating...'
                      : 'Enable location for local jobs'
                    }
                  </p>
                  {locationError && (
                    <p className="text-red-200 text-xs truncate">
                      {locationError}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-1.5 flex-shrink-0">
                {!currentLocation && !locationError && (
                  <Button
                    size="sm"
                    onClick={() => setLocationPermission(true)}
                    disabled={isLocationLoading}
                    className="bg-white/90 hover:bg-white text-primary font-medium text-xs px-2.5 py-1 h-auto"
                  >
                    {isLocationLoading ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      'Allow'
                    )}
                  </Button>
                )}

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={dismissLocationBanner}
                  className="text-white/90 hover:text-white hover:bg-white/10 p-1.5 h-auto"
                >
                  <X className="w-3.5 h-3.5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
