"use client"

import type React from "react"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { <PERSON>ton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { BackgroundPattern } from "@/components/background-pattern"
import {
  Eye,
  EyeOff,
  Mail,
  Lock,
  Briefcase,
  ArrowLeft,
  Github,
  Chrome,
  Linkedin,
  User,
  Building2,
  CheckCircle,
  ArrowRight,
  Sparkles,
  Target,
  TrendingUp,
} from "lucide-react"

type UserType = "jobseeker" | "employer"
type Step = "type" | "details" | "verification"

export default function SignUpPage() {
  const [currentStep, setCurrentStep] = useState<Step>("type")
  const [userType, setUserType] = useState<UserType>("jobseeker")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    company: "",
    jobTitle: "",
    agreeToTerms: false,
    subscribeNewsletter: true,
  })

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setCurrentStep("verification")
    setIsLoading(false)
  }

  const socialProviders = [
    { name: "Google", icon: Chrome, color: "hover:bg-red-50 hover:border-red-200" },
    { name: "LinkedIn", icon: Linkedin, color: "hover:bg-blue-50 hover:border-blue-200" },
    { name: "GitHub", icon: Github, color: "hover:bg-gray-50 hover:border-gray-200" },
  ]

  const userTypeOptions = [
    {
      type: "jobseeker" as UserType,
      title: "Job Seeker",
      description: "Find your dream job with AI-powered matching",
      icon: User,
      features: ["AI Job Matching", "Salary Insights", "Career Guidance", "Skills Assessment"],
      color: "border-blue-200 bg-blue-50/50",
      iconColor: "text-blue-600",
    },
    {
      type: "employer" as UserType,
      title: "Employer",
      description: "Find top talent and build your dream team",
      icon: Building2,
      features: ["Talent Pool Access", "Smart Filtering", "Team Management", "Analytics Dashboard"],
      color: "border-green-200 bg-green-50/50",
      iconColor: "text-green-600",
    },
  ]

  const benefits = [
    {
      icon: Sparkles,
      title: "AI-Powered Matching",
      description: "Get matched with perfect opportunities using advanced AI",
    },
    {
      icon: Target,
      title: "Personalized Experience",
      description: "Tailored job recommendations based on your profile",
    },
    { icon: TrendingUp, title: "Career Growth", description: "Track your progress and plan your career path" },
  ]

  return (
    <div className="min-h-screen bg-background relative">
      <BackgroundPattern />

      {/* Header */}
      <motion.header initial={{ y: -50, opacity: 0 }} animate={{ y: 0, opacity: 1 }} className="relative z-10 p-6">
        <div className="container mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2 group">
            <Button variant="ghost" size="icon" className="group-hover:bg-muted/50">
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Briefcase className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                JobPortal
              </span>
            </div>
          </Link>
          <div className="text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link href="/signin" className="text-primary hover:underline font-medium">
              Sign in
            </Link>
          </div>
        </div>
      </motion.header>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          {/* Progress Indicator */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center mb-12"
          >
            <div className="flex items-center space-x-4">
              {["type", "details", "verification"].map((step, index) => (
                <div key={step} className="flex items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-300 ${
                      currentStep === step
                        ? "bg-primary text-primary-foreground"
                        : index < ["type", "details", "verification"].indexOf(currentStep)
                          ? "bg-green-500 text-white"
                          : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {index < ["type", "details", "verification"].indexOf(currentStep) ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  {index < 2 && (
                    <div
                      className={`w-16 h-1 mx-2 transition-all duration-300 ${
                        index < ["type", "details", "verification"].indexOf(currentStep) ? "bg-green-500" : "bg-muted"
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
          </motion.div>

          <AnimatePresence mode="wait">
            {currentStep === "type" && (
              <motion.div
                key="type"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
              >
                {/* Left Side - Benefits */}
                <div className="space-y-8">
                  <div>
                    <h1 className="text-4xl md:text-5xl font-bold mb-6">
                      Join the future of
                      <span className="block text-primary">career success</span>
                    </h1>
                    <p className="text-xl text-muted-foreground leading-relaxed">
                      Whether you're looking for your next opportunity or building your dream team, we've got you
                      covered.
                    </p>
                  </div>

                  <div className="space-y-6">
                    {benefits.map((benefit, index) => (
                      <motion.div
                        key={benefit.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 + index * 0.1 }}
                        className="flex items-start space-x-4 p-4 rounded-xl bg-muted/30 backdrop-blur-sm border border-border/50"
                      >
                        <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                          <benefit.icon className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold mb-1">{benefit.title}</h3>
                          <p className="text-sm text-muted-foreground">{benefit.description}</p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Right Side - User Type Selection */}
                <div className="w-full max-w-md mx-auto">
                  <Card className="glass border-border/50 shadow-2xl">
                    <CardHeader className="text-center pb-6">
                      <CardTitle className="text-2xl font-bold">Choose Your Path</CardTitle>
                      <p className="text-muted-foreground">Select how you want to use JobPortal</p>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <RadioGroup value={userType} onValueChange={(value) => setUserType(value as UserType)}>
                        {userTypeOptions.map((option) => (
                          <motion.div key={option.type} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                            <Label
                              htmlFor={option.type}
                              className={`flex items-start space-x-4 p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                                userType === option.type
                                  ? `${option.color} border-primary`
                                  : "border-border/50 hover:border-border"
                              }`}
                            >
                              <RadioGroupItem value={option.type} id={option.type} className="mt-1" />
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <option.icon className={`w-6 h-6 ${option.iconColor}`} />
                                  <h3 className="font-semibold text-lg">{option.title}</h3>
                                </div>
                                <p className="text-sm text-muted-foreground mb-3">{option.description}</p>
                                <div className="space-y-1">
                                  {option.features.map((feature) => (
                                    <div
                                      key={feature}
                                      className="flex items-center space-x-2 text-xs text-muted-foreground"
                                    >
                                      <CheckCircle className="w-3 h-3 text-green-500" />
                                      <span>{feature}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </Label>
                          </motion.div>
                        ))}
                      </RadioGroup>

                      <Button onClick={() => setCurrentStep("details")} className="w-full h-12 text-lg font-semibold">
                        Continue
                        <ArrowRight className="w-5 h-5 ml-2" />
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            )}

            {currentStep === "details" && (
              <motion.div
                key="details"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                className="max-w-2xl mx-auto"
              >
                <Card className="glass border-border/50 shadow-2xl">
                  <CardHeader className="text-center pb-6">
                    <CardTitle className="text-2xl font-bold">Create Your Account</CardTitle>
                    <p className="text-muted-foreground">
                      {userType === "jobseeker" ? "Start your career journey" : "Build your dream team"}
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Social Sign Up */}
                    <div className="space-y-3">
                      {socialProviders.map((provider) => (
                        <Button
                          key={provider.name}
                          variant="outline"
                          className={`w-full h-12 transition-all duration-200 ${provider.color}`}
                          disabled={isLoading}
                        >
                          <provider.icon className="w-5 h-5 mr-3" />
                          Continue with {provider.name}
                        </Button>
                      ))}
                    </div>

                    <div className="relative">
                      <Separator />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="bg-background px-4 text-sm text-muted-foreground">or</span>
                      </div>
                    </div>

                    {/* Registration Form */}
                    <form onSubmit={handleSubmit} className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="firstName">First Name</Label>
                          <Input
                            id="firstName"
                            placeholder="John"
                            value={formData.firstName}
                            onChange={(e) => handleInputChange("firstName", e.target.value)}
                            className="h-12"
                            required
                            disabled={isLoading}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName">Last Name</Label>
                          <Input
                            id="lastName"
                            placeholder="Doe"
                            value={formData.lastName}
                            onChange={(e) => handleInputChange("lastName", e.target.value)}
                            className="h-12"
                            required
                            disabled={isLoading}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={formData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            className="pl-10 h-12"
                            required
                            disabled={isLoading}
                          />
                        </div>
                      </div>

                      {userType === "employer" && (
                        <>
                          <div className="space-y-2">
                            <Label htmlFor="company">Company Name</Label>
                            <div className="relative">
                              <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                              <Input
                                id="company"
                                placeholder="Acme Inc."
                                value={formData.company}
                                onChange={(e) => handleInputChange("company", e.target.value)}
                                className="pl-10 h-12"
                                required
                                disabled={isLoading}
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="jobTitle">Your Job Title</Label>
                            <Input
                              id="jobTitle"
                              placeholder="HR Manager"
                              value={formData.jobTitle}
                              onChange={(e) => handleInputChange("jobTitle", e.target.value)}
                              className="h-12"
                              required
                              disabled={isLoading}
                            />
                          </div>
                        </>
                      )}

                      <div className="space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <div className="relative">
                          <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                          <Input
                            id="password"
                            type={showPassword ? "text" : "password"}
                            placeholder="Create a strong password"
                            value={formData.password}
                            onChange={(e) => handleInputChange("password", e.target.value)}
                            className="pl-10 pr-10 h-12"
                            required
                            disabled={isLoading}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 w-10"
                            onClick={() => setShowPassword(!showPassword)}
                            disabled={isLoading}
                          >
                            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">Confirm Password</Label>
                        <div className="relative">
                          <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                          <Input
                            id="confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            placeholder="Confirm your password"
                            value={formData.confirmPassword}
                            onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                            className="pl-10 pr-10 h-12"
                            required
                            disabled={isLoading}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 w-10"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            disabled={isLoading}
                          >
                            {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-start space-x-2">
                          <Checkbox
                            id="terms"
                            checked={formData.agreeToTerms}
                            onCheckedChange={(checked) => handleInputChange("agreeToTerms", checked)}
                            disabled={isLoading}
                            className="mt-1"
                          />
                          <Label htmlFor="terms" className="text-sm leading-relaxed">
                            I agree to the{" "}
                            <Link href="/terms" className="text-primary hover:underline">
                              Terms of Service
                            </Link>{" "}
                            and{" "}
                            <Link href="/privacy" className="text-primary hover:underline">
                              Privacy Policy
                            </Link>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="newsletter"
                            checked={formData.subscribeNewsletter}
                            onCheckedChange={(checked) => handleInputChange("subscribeNewsletter", checked)}
                            disabled={isLoading}
                          />
                          <Label htmlFor="newsletter" className="text-sm">
                            Subscribe to our newsletter for job alerts and career tips
                          </Label>
                        </div>
                      </div>

                      <div className="flex space-x-4">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setCurrentStep("type")}
                          className="flex-1 h-12"
                          disabled={isLoading}
                        >
                          Back
                        </Button>
                        <Button
                          type="submit"
                          className="flex-1 h-12 text-lg font-semibold"
                          disabled={isLoading || !formData.agreeToTerms}
                        >
                          {isLoading ? (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                              className="w-5 h-5 border-2 border-primary-foreground border-t-transparent rounded-full"
                            />
                          ) : (
                            "Create Account"
                          )}
                        </Button>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {currentStep === "verification" && (
              <motion.div
                key="verification"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="max-w-md mx-auto text-center"
              >
                <Card className="glass border-border/50 shadow-2xl">
                  <CardContent className="p-12">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring" }}
                      className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
                    >
                      <CheckCircle className="w-10 h-10 text-green-600" />
                    </motion.div>

                    <h2 className="text-2xl font-bold mb-4">Check Your Email!</h2>
                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      We've sent a verification link to <strong>{formData.email}</strong>. Click the link to activate
                      your account and start your journey.
                    </p>

                    <div className="space-y-4">
                      <Button className="w-full h-12">Open Email App</Button>
                      <Button variant="outline" className="w-full h-12 bg-transparent">
                        Resend Email
                      </Button>
                    </div>

                    <p className="text-sm text-muted-foreground mt-6">
                      Didn't receive the email? Check your spam folder or{" "}
                      <button className="text-primary hover:underline">contact support</button>
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}
