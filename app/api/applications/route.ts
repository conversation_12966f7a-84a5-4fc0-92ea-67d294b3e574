import { NextRequest, NextResponse } from 'next/server'
import { Application } from '@/lib/models/application.model'
import { Job } from '@/lib/models/job.model'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { withValidation, schemas } from '@/lib/middleware/validation.middleware'
import { connectDB } from '@/lib/db'

// GET /api/applications - Get user's applications
async function getApplicationsHandler(
  request: NextRequest,
  { user }: { user: any }
) {
  try {
    await connectDB()

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const status = url.searchParams.getAll('status')
    const jobType = url.searchParams.getAll('jobType')
    const company = url.searchParams.getAll('company')
    const dateFrom = url.searchParams.get('dateFrom')
    const dateTo = url.searchParams.get('dateTo')

    // Build query
    const query: any = { userId: user._id }

    // Status filter
    if (status.length > 0) {
      query.status = { $in: status }
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.submittedAt = {}
      if (dateFrom) query.submittedAt.$gte = new Date(dateFrom)
      if (dateTo) query.submittedAt.$lte = new Date(dateTo)
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Get applications with populated job and company data
    const [applications, total] = await Promise.all([
      Application.find(query)
        .populate({
          path: 'jobId',
          populate: {
            path: 'company',
            select: 'name logo location industry'
          }
        })
        .sort({ submittedAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Application.countDocuments(query)
    ])

    // Filter by job type and company if specified
    let filteredApplications = applications
    
    if (jobType.length > 0) {
      filteredApplications = filteredApplications.filter(app => 
        app.jobId && jobType.includes(app.jobId.type)
      )
    }

    if (company.length > 0) {
      filteredApplications = filteredApplications.filter(app => 
        app.jobId?.company && company.includes(app.jobId.company.name)
      )
    }

    // Transform data for frontend
    const transformedApplications = filteredApplications.map(app => ({
      _id: app._id,
      jobId: app.jobId?._id,
      userId: app.userId,
      job: app.jobId ? {
        _id: app.jobId._id,
        title: app.jobId.title,
        company: app.jobId.company,
        location: app.jobId.location,
        type: app.jobId.type,
        salary: app.jobId.salary
      } : null,
      status: app.status,
      coverLetter: app.coverLetter,
      resumeId: app.resumeId,
      portfolioUrl: app.customFields?.portfolioUrl,
      linkedinUrl: app.customFields?.linkedinUrl,
      availableStartDate: app.customFields?.availableStartDate,
      salaryExpectation: app.customFields?.salaryExpectation,
      willingToRelocate: app.customFields?.willingToRelocate,
      customFields: app.customFields,
      timeline: app.timeline,
      submittedAt: app.submittedAt,
      updatedAt: app.updatedAt
    }))

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      applications: transformedApplications,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    })

  } catch (error) {
    console.error('Get applications error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/applications - Apply to job
async function createApplicationHandler(
  request: NextRequest,
  data: any,
  { user }: { user: any }
) {
  try {
    await connectDB()

    const { jobId, coverLetter, resumeId, customFields } = data

    // Verify job exists and is active
    const job = await Job.findById(jobId)
    if (!job || !job.isActive) {
      return NextResponse.json(
        { error: 'Job not found or no longer active' },
        { status: 404 }
      )
    }

    // Check if user already applied
    const existingApplication = await Application.findOne({
      userId: user._id,
      jobId: jobId
    })

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You have already applied to this job' },
        { status: 409 }
      )
    }

    // Check application deadline
    if (job.applicationDeadline && new Date() > job.applicationDeadline) {
      return NextResponse.json(
        { error: 'Application deadline has passed' },
        { status: 400 }
      )
    }

    // Create application
    const application = new Application({
      userId: user._id,
      jobId: jobId,
      coverLetter,
      resumeId,
      customFields: customFields || {},
      status: 'submitted',
      timeline: [{
        type: 'submitted',
        message: 'Application submitted',
        createdAt: new Date()
      }],
      submittedAt: new Date()
    })

    await application.save()

    // Update job applications count
    await Job.findByIdAndUpdate(jobId, { $inc: { applicationsCount: 1 } })

    // Populate job data for response
    await application.populate({
      path: 'jobId',
      populate: {
        path: 'company',
        select: 'name logo location industry'
      }
    })

    return NextResponse.json({
      message: 'Application submitted successfully',
      application: {
        _id: application._id,
        jobId: application.jobId._id,
        job: {
          _id: application.jobId._id,
          title: application.jobId.title,
          company: application.jobId.company,
          location: application.jobId.location,
          type: application.jobId.type
        },
        status: application.status,
        submittedAt: application.submittedAt
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Create application error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(getApplicationsHandler, { 
  requiredRoles: ['job_seeker'] 
})

export const POST = withAuth(
  withValidation(schemas.jobApplication, createApplicationHandler),
  { requiredRoles: ['job_seeker'] }
)
