import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authService } from '@/lib/services/auth.service'

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const { action, ...data } = body
    
    if (action === 'register') {
      const result = await authService.register(data)
      return NextResponse.json({
        success: true,
        message: 'Registration successful',
        data: result
      })
    }
    
    if (action === 'login') {
      const result = await authService.login(data)
      return NextResponse.json({
        success: true,
        message: 'Login successful',
        data: result
      })
    }
    
    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    
  } catch (error) {
    console.error('Auth test error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        details: error
      },
      { status: 500 }
    )
  }
}
