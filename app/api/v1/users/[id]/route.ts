import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validate<PERSON>ethod, validateRequestBody } from '@/lib/api/route-handler'
import { userService, validationService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import type { UpdateUserRequest } from '@/lib/services'

interface RouteParams {
  params: {
    id: string
  }
}

// Validation function for update user request
function validateUpdateUserRequest(data: any): UpdateUserRequest {
  const errors: string[] = []
  
  // Optional field validations
  if (data.firstName && typeof data.firstName !== 'string') {
    errors.push('First name must be a string')
  }
  
  if (data.lastName && typeof data.lastName !== 'string') {
    errors.push('Last name must be a string')
  }
  
  if (data.phone && !validationService.validatePhone(data.phone)) {
    errors.push('Invalid phone number format')
  }
  
  if (errors.length > 0) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      `Validation failed: ${errors.join(', ')}`,
      undefined,
      { validationErrors: errors }
    )
  }
  
  return {
    firstName: data.firstName?.trim(),
    lastName: data.lastName?.trim(),
    phone: data.phone?.trim(),
    location: data.location,
    preferences: data.preferences
  }
}

// GET /api/v1/users/[id] - Get user profile
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['GET'])
  
  const userId = params.id
  const result = await userService.getUserById(userId)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true
})

// PUT /api/v1/users/[id] - Update user profile
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['PUT'])
  
  const userId = params.id
  const updateData = await validateRequestBody(request, validateUpdateUserRequest)
  
  const result = await userService.updateUser(userId, updateData)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true
})

// DELETE /api/v1/users/[id] - Deactivate user account
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['DELETE'])
  
  const userId = params.id
  await userService.deactivateUser(userId)
  
  return createSuccessResponse({ message: 'User account deactivated successfully' })
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed for individual user resources'
  )
}
