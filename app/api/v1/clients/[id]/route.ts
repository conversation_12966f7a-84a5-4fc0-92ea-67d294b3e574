import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { clientService } from '@/lib/services/client.service'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const clientId = params.id

    if (clientId === 'me') {
      const result = await clientService.getClientByUserId(authResult.user.id)
      return NextResponse.json({
        success: true,
        data: result
      })
    }

    const result = await clientService.getClientById(clientId)
    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Get client error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch client' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const clientId = params.id
    const updateData = await request.json()

    if (clientId === 'me') {
      const result = await clientService.updateClientByUserId(authResult.user.id, updateData)
      return NextResponse.json({
        success: true,
        message: 'Profile updated successfully',
        data: result
      })
    }

    const client = await clientService.getClientById(clientId)
    if (client.user.toString() !== authResult.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    const result = await clientService.updateClientById(clientId, updateData)
    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      data: result
    })

  } catch (error) {
    console.error('Update client error:', error)
    return NextResponse.json(
      { error: 'Failed to update client' },
      { status: 500 }
    )
  }
}
