import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/api/route-handler'
import { validateMethod, validateRequestBody, createSuccessResponse } from '@/lib/api/utils'
import { ClientService } from '@/lib/services/client.service'
import { errorService, ErrorCode } from '@/lib/services/error.service'
import { validateCreateClientRequest } from '@/types/client.types'

const clientService = new ClientService()

// GET /api/v1/clients - Search clients
export const GET = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['GET'])
  
  const { searchParams } = new URL(request.url)
  
  // Parse query parameters
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '20')
  const skills = searchParams.get('skills')?.split(',').filter(Boolean) || []
  const experience = searchParams.get('experience')?.split(',').filter(Boolean) || []
  const industries = searchParams.get('industries')?.split(',').filter(Boolean) || []
  const locations = searchParams.get('locations')?.split(',').filter(Boolean) || []
  const availability = searchParams.get('availability')?.split(',').filter(Boolean) || []
  const salaryMin = searchParams.get('salaryMin') ? parseInt(searchParams.get('salaryMin')!) : undefined
  const salaryMax = searchParams.get('salaryMax') ? parseInt(searchParams.get('salaryMax')!) : undefined
  const profileVisibility = searchParams.get('profileVisibility')?.split(',').filter(Boolean) || []
  
  const filters = {
    skills,
    experience: experience as any[],
    industries,
    locations,
    availability: availability as any[],
    salaryMin,
    salaryMax,
    profileVisibility: profileVisibility as any[]
  }
  
  const result = await clientService.searchClients(filters, page, limit)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: false // Public endpoint for searching clients
})

// POST /api/v1/clients - Create a new client profile
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  const clientData = await validateRequestBody(request, validateCreateClientRequest)
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const result = await clientService.createClient(clientData, userId)
  
  return createSuccessResponse(result, 201)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['job_seeker'] // Only job seekers can create client profiles
})
