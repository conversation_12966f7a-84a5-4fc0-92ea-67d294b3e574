import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validateMethod, validateRequestBody } from '@/lib/api/route-handler'
import { jobService, validationService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import type { CreateJobRequest, JobSearchFilters } from '@/lib/services'

// Validation function for create job request
function validateCreateJobRequest(data: any): CreateJobRequest {
  const errors: string[] = []
  
  // Required fields
  if (!data.title) errors.push('Job title is required')
  if (!data.description) errors.push('Job description is required')
  if (!data.companyId) errors.push('Company ID is required')
  if (!data.type) errors.push('Job type is required')
  if (!data.level) errors.push('Job level is required')
  if (!data.category) errors.push('Job category is required')
  
  // Validate enums
  if (data.type && !['full-time', 'part-time', 'contract', 'internship'].includes(data.type)) {
    errors.push('Invalid job type')
  }
  
  if (data.level && !['entry', 'mid', 'senior', 'executive'].includes(data.level)) {
    errors.push('Invalid job level')
  }
  
  // Validate requirements array
  if (!data.requirements || !Array.isArray(data.requirements) || data.requirements.length === 0) {
    errors.push('At least one requirement is needed')
  }
  
  // Validate salary if provided
  if (data.salary) {
    if (data.salary.min && data.salary.max && data.salary.min > data.salary.max) {
      errors.push('Minimum salary cannot be greater than maximum salary')
    }
  }
  
  // Validate application deadline if provided
  if (data.applicationDeadline) {
    const deadline = new Date(data.applicationDeadline)
    if (deadline <= new Date()) {
      errors.push('Application deadline must be in the future')
    }
  }
  
  if (errors.length > 0) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      `Validation failed: ${errors.join(', ')}`,
      undefined,
      { validationErrors: errors }
    )
  }
  
  return {
    title: data.title.trim(),
    description: data.description.trim(),
    companyId: data.companyId,
    location: data.location || {},
    salary: data.salary,
    requirements: data.requirements.map((req: string) => req.trim()),
    benefits: data.benefits?.map((benefit: string) => benefit.trim()),
    type: data.type,
    level: data.level,
    category: data.category.trim(),
    tags: data.tags?.map((tag: string) => tag.trim().toLowerCase()) || [],
    applicationDeadline: data.applicationDeadline ? new Date(data.applicationDeadline) : undefined
  }
}

// GET /api/v1/jobs - Search and filter jobs
export const GET = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['GET'])
  
  const { searchParams } = new URL(request.url)
  
  // Pagination
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '10')
  
  // Search term
  const searchTerm = searchParams.get('search') || undefined
  
  // Filters
  const filters: JobSearchFilters = {}
  
  if (searchParams.get('location')) filters.location = searchParams.get('location')!
  if (searchParams.get('remote')) filters.remote = searchParams.get('remote') === 'true'
  if (searchParams.get('type')) filters.type = searchParams.get('type')!
  if (searchParams.get('level')) filters.level = searchParams.get('level')!
  if (searchParams.get('category')) filters.category = searchParams.get('category')!
  if (searchParams.get('companyId')) filters.companyId = searchParams.get('companyId')!
  if (searchParams.get('salaryMin')) filters.salaryMin = parseInt(searchParams.get('salaryMin')!)
  if (searchParams.get('salaryMax')) filters.salaryMax = parseInt(searchParams.get('salaryMax')!)
  
  // Tags (comma-separated)
  const tagsParam = searchParams.get('tags')
  if (tagsParam) {
    filters.tags = tagsParam.split(',').map(tag => tag.trim().toLowerCase())
  }
  
  const result = await jobService.searchJobs(searchTerm, filters, page, limit)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true
})

// POST /api/v1/jobs - Create a new job
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  const jobData = await validateRequestBody(request, validateCreateJobRequest)
  
  // Get user ID from auth context (this would be set by auth middleware)
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const result = await jobService.createJob(jobData, userId)
  
  return createSuccessResponse(result, 201)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin']
})

// Method not allowed for other HTTP methods
export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed for jobs collection'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for jobs collection'
  )
}
