import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessR<PERSON>ponse, validate<PERSON><PERSON><PERSON>, validateRequestBody } from '@/lib/api/route-handler'
import { authService, validationService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import type { AuthResponse } from '@/lib/services'

export const POST = withE<PERSON>r<PERSON>andler<AuthResponse>(async (request: NextRequest) => {
  validateMethod(request, ['POST'])

  const loginData = await validateRequestBody(request, validationService.validateLoginRequest)

  const result = await authService.login(loginData)

  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  rateLimit: {
    requests: 10, // 10 login attempts
    windowMs: 15 * 60 * 1000 // per 15 minutes
  }
})

// Method not allowed for other HTTP methods
export async function GET() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'GET method not allowed for login'
  )
}

export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed for login'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for login'
  )
}
