import { NextRequest, NextResponse } from 'next/server'
import { Job } from '@/lib/models/job.model'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { withValidation, schemas, rateLimiters } from '@/lib/middleware/validation.middleware'
import { connectDB } from '@/lib/db'

// GET /api/jobs - Search jobs
async function getJobsHand<PERSON>(request: NextRequest, data: any) {
  try {
    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown'
    const rateLimit = rateLimiters.search(clientIP)
    
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    await connectDB()

    const {
      q,
      location,
      jobTypes,
      experienceLevels,
      salaryRange,
      remote,
      datePosted,
      sortBy = 'relevance',
      page = 1,
      limit = 20
    } = data

    // Build search query
    const searchQuery: any = { isActive: true }

    // Text search
    if (q) {
      searchQuery.$or = [
        { title: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } },
        { 'company.name': { $regex: q, $options: 'i' } },
        { 'requirements.skills': { $in: [new RegExp(q, 'i')] } }
      ]
    }

    // Location search
    if (location && !remote) {
      searchQuery.$or = [
        { 'location.city': { $regex: location, $options: 'i' } },
        { 'location.state': { $regex: location, $options: 'i' } }
      ]
    }

    // Remote filter
    if (remote) {
      searchQuery['location.remote'] = true
    }

    // Job types filter
    if (jobTypes && jobTypes.length > 0) {
      searchQuery.type = { $in: jobTypes }
    }

    // Experience levels filter
    if (experienceLevels && experienceLevels.length > 0) {
      searchQuery['requirements.experience'] = { $in: experienceLevels }
    }

    // Salary range filter
    if (salaryRange && salaryRange.length === 2) {
      const [minSalary, maxSalary] = salaryRange
      searchQuery.$or = [
        { 'salary.min': { $gte: minSalary, $lte: maxSalary } },
        { 'salary.max': { $gte: minSalary, $lte: maxSalary } },
        { 
          $and: [
            { 'salary.min': { $lte: minSalary } },
            { 'salary.max': { $gte: maxSalary } }
          ]
        }
      ]
    }

    // Date posted filter
    if (datePosted && datePosted !== 'any') {
      const now = new Date()
      let dateThreshold: Date

      switch (datePosted) {
        case '24h':
          dateThreshold = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          dateThreshold = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          dateThreshold = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          dateThreshold = new Date(0)
      }

      searchQuery.postedAt = { $gte: dateThreshold }
    }

    // Build sort criteria
    let sortCriteria: any = {}
    switch (sortBy) {
      case 'date':
        sortCriteria = { postedAt: -1 }
        break
      case 'salary':
        sortCriteria = { 'salary.max': -1, 'salary.min': -1 }
        break
      default: // relevance
        if (q) {
          // Text score for relevance when searching
          sortCriteria = { score: { $meta: 'textScore' } }
        } else {
          sortCriteria = { postedAt: -1 }
        }
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Execute search
    const startTime = Date.now()
    
    const [jobs, total] = await Promise.all([
      Job.find(searchQuery)
        .populate('company', 'name logo location industry')
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit)
        .lean(),
      Job.countDocuments(searchQuery)
    ])

    const searchTime = Date.now() - startTime

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    return NextResponse.json({
      jobs,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      },
      searchMeta: {
        query: q,
        location,
        totalResults: total,
        searchTime,
        filters: {
          jobTypes,
          experienceLevels,
          salaryRange,
          remote,
          datePosted
        }
      }
    })

  } catch (error) {
    console.error('Jobs search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/jobs - Create job (company admin/recruiter only)
async function createJobHandler(request: NextRequest, data: any, { user }: { user: any }) {
  try {
    await connectDB()

    // Verify user has permission to create jobs
    if (!['company_admin', 'recruiter', 'admin'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Get user's company
    let companyId = user.companyId
    if (!companyId && user.role === 'admin') {
      // Admin can specify company in request
      companyId = data.companyId
    }

    if (!companyId) {
      return NextResponse.json(
        { error: 'No company associated with user' },
        { status: 400 }
      )
    }

    // Create job
    const job = new Job({
      ...data,
      company: companyId,
      postedBy: user._id,
      postedAt: new Date(),
      isActive: true,
      applicationsCount: 0,
      viewsCount: 0
    })

    await job.save()
    await job.populate('company', 'name logo location industry')

    return NextResponse.json({
      message: 'Job created successfully',
      job
    }, { status: 201 })

  } catch (error) {
    console.error('Create job error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const GET = withValidation(schemas.jobSearch, getJobsHandler)
export const POST = withAuth(
  withValidation(schemas.createJob, createJobHandler),
  { requiredRoles: ['company_admin', 'recruiter', 'admin'] }
)
