import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/db'

async function getMeHandler(request: NextRequest, { user }: { user: any }) {
  try {
    await connectDB()

    // User is already populated by auth middleware
    const userData = {
      _id: user._id,
      email: user.email,
      role: user.role,
      profile: user.profile,
      preferences: user.preferences,
      companyId: user.companyId,
      isEmailVerified: user.isEmailVerified,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }

    return NextResponse.json({
      user: userData
    })

  } catch (error) {
    console.error('Get user error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(getMeHandler)
