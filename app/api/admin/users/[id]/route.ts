import { NextRequest, NextResponse } from 'next/server'
import { adminService } from '@/lib/services/admin.service'
import { withAdminAuth } from '@/lib/middleware/auth.middleware'
import { createSuccessResponse } from '@/lib/api/route-handler'
import { connectDB } from '@/lib/database/connection'

interface RouteParams {
  params: {
    id: string
  }
}

// PUT /api/admin/users/[id] - Update user (status, role, etc.)
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    const userId = params.id
    const body = await request.json()
    const { action, ...data } = body

    switch (action) {
      case 'updateStatus':
        await adminService.updateUserStatus(userId, data.isActive)
        break
      case 'updateRole':
        await adminService.updateUserRole(userId, data.role)
        break
      default:
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'INVALID_ACTION',
              message: 'Invalid action specified'
            },
            meta: {
              timestamp: new Date().toISOString(),
              requestId: crypto.randomUUID()
            }
          },
          { status: 400 }
        )
    }
    
    return createSuccessResponse({ message: 'User updated successfully' })
  } catch (error: any) {
    console.error('Admin user update error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'USER_UPDATE_ERROR',
          message: error.message || 'Failed to update user'
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID()
        }
      },
      { status: error.statusCode || 500 }
    )
  }
}

// DELETE /api/admin/users/[id] - Delete user (soft delete)
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    const userId = params.id
    await adminService.deleteUser(userId)
    
    return createSuccessResponse({ message: 'User deleted successfully' })
  } catch (error: any) {
    console.error('Admin user delete error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'USER_DELETE_ERROR',
          message: error.message || 'Failed to delete user'
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID()
        }
      },
      { status: error.statusCode || 500 }
    )
  }
}

export const dynamic = 'force-dynamic'
