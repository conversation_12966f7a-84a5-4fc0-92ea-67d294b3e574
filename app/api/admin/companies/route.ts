import { NextRequest, NextResponse } from 'next/server'
import { adminService } from '@/lib/services/admin.service'
import { withAdminAuth } from '@/lib/middleware/auth.middleware'
import { createSuccessResponse } from '@/lib/api/route-handler'
import { connectDB } from '@/lib/database/connection'

// GET /api/admin/companies - Get companies with verification status
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const verificationStatus = searchParams.get('verificationStatus') || undefined
    const search = searchParams.get('search') || undefined

    const filters = {
      verificationStatus,
      search
    }

    const result = await adminService.getCompanies(page, limit, filters)
    
    return createSuccessResponse(result)
  } catch (error: any) {
    console.error('Admin companies fetch error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'COMPANIES_FETCH_ERROR',
          message: error.message || 'Failed to fetch companies'
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID()
        }
      },
      { status: error.statusCode || 500 }
    )
  }
}

export const dynamic = 'force-dynamic'
