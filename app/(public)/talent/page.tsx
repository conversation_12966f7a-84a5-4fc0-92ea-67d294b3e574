'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { TalentFilters } from '@/components/talent/talent-filters'
import { TalentCard } from '@/components/talent/talent-card'
import { TalentDetailModal } from '@/components/talent/talent-detail-modal'
import { TalentPagination, TalentSorting, TalentResultsSummary } from '@/components/talent/talent-pagination'
import {
  Search,
  Filter,
  Grid3X3,
  List,
  MapPin,
  Star,
  Briefcase,
  GraduationCap,
  Clock,
  DollarSign,
  Eye,
  Heart,
  MessageCircle,
  X,
  ChevronDown,
  Users,
  TrendingUp,
  Award
} from 'lucide-react'

export default function TalentPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [showDesktopFilters, setShowDesktopFilters] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSkills, setSelectedSkills] = useState<string[]>([])
  const [experienceRange, setExperienceRange] = useState([0, 15])
  const [salaryRange, setSalaryRange] = useState([30000, 200000])
  const [sortBy, setSortBy] = useState('relevance')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(12)
  const [selectedTalent, setSelectedTalent] = useState<any>(null)

  // Mock talent data - in real app this would come from API
  const talents = [
    {
      id: 1,
      name: "Sarah Chen",
      title: "Senior Full Stack Developer",
      location: "San Francisco, CA",
      avatar: "/api/placeholder/80/80",
      rating: 4.9,
      experience: 8,
      hourlyRate: 120,
      skills: ["React", "Node.js", "TypeScript", "AWS", "GraphQL"],
      bio: "Passionate full-stack developer with 8+ years of experience building scalable web applications.",
      availability: "Available",
      projects: 47,
      reviews: 23,
      responseTime: "2 hours",
      successRate: 98
    },
    {
      id: 2,
      name: "Marcus Johnson",
      title: "UI/UX Designer & Product Strategist",
      location: "New York, NY",
      avatar: "/api/placeholder/80/80",
      rating: 4.8,
      experience: 6,
      hourlyRate: 95,
      skills: ["Figma", "Design Systems", "User Research", "Prototyping", "Branding"],
      bio: "Creative designer focused on user-centered design and strategic product thinking.",
      availability: "Available",
      projects: 32,
      reviews: 18,
      responseTime: "1 hour",
      successRate: 96
    },
    {
      id: 3,
      name: "Elena Rodriguez",
      title: "Data Scientist & ML Engineer",
      location: "Austin, TX",
      avatar: "/api/placeholder/80/80",
      rating: 4.9,
      experience: 5,
      hourlyRate: 110,
      skills: ["Python", "TensorFlow", "SQL", "Machine Learning", "Data Visualization"],
      bio: "Data scientist specializing in machine learning and predictive analytics.",
      availability: "Busy",
      projects: 28,
      reviews: 15,
      responseTime: "4 hours",
      successRate: 99
    },
    {
      id: 4,
      name: "David Kim",
      title: "DevOps Engineer & Cloud Architect",
      location: "Seattle, WA",
      avatar: "/api/placeholder/80/80",
      rating: 4.7,
      experience: 10,
      hourlyRate: 130,
      skills: ["AWS", "Kubernetes", "Docker", "Terraform", "CI/CD"],
      bio: "DevOps expert with extensive experience in cloud infrastructure and automation.",
      availability: "Available",
      projects: 41,
      reviews: 22,
      responseTime: "3 hours",
      successRate: 97
    },
    {
      id: 5,
      name: "Priya Patel",
      title: "Mobile App Developer",
      location: "Toronto, ON",
      avatar: "/api/placeholder/80/80",
      rating: 4.8,
      experience: 7,
      hourlyRate: 105,
      skills: ["React Native", "Flutter", "iOS", "Android", "Firebase"],
      bio: "Mobile development specialist creating beautiful, performant cross-platform applications.",
      availability: "Available",
      projects: 35,
      reviews: 19,
      responseTime: "2 hours",
      successRate: 95
    },
    {
      id: 6,
      name: "James Wilson",
      title: "Blockchain Developer",
      location: "London, UK",
      avatar: "/api/placeholder/80/80",
      rating: 4.9,
      experience: 4,
      hourlyRate: 140,
      skills: ["Solidity", "Web3", "Ethereum", "Smart Contracts", "DeFi"],
      bio: "Blockchain developer building the future of decentralized applications.",
      availability: "Available",
      projects: 21,
      reviews: 12,
      responseTime: "1 hour",
      successRate: 100
    },
    {
      id: 7,
      name: "Amanda Foster",
      title: "Senior HR Business Partner",
      location: "Chicago, IL",
      avatar: "/api/placeholder/80/80",
      rating: 4.8,
      experience: 12,
      hourlyRate: 85,
      skills: ["Recruitment", "Talent Acquisition", "HRIS", "Performance Management", "Employee Relations"],
      bio: "Strategic HR professional with expertise in talent management and organizational development.",
      availability: "Available",
      projects: 38,
      reviews: 26,
      responseTime: "3 hours",
      successRate: 94
    },
    {
      id: 8,
      name: "Robert Martinez",
      title: "Certified Public Accountant (CPA)",
      location: "Miami, FL",
      avatar: "/api/placeholder/80/80",
      rating: 4.9,
      experience: 15,
      hourlyRate: 75,
      skills: ["Financial Analysis", "Tax Preparation", "Auditing", "QuickBooks", "Financial Planning"],
      bio: "Experienced CPA specializing in small business accounting and tax optimization strategies.",
      availability: "Available",
      projects: 52,
      reviews: 31,
      responseTime: "2 hours",
      successRate: 98
    },
    {
      id: 9,
      name: "Lisa Thompson",
      title: "Digital Marketing Strategist",
      location: "Los Angeles, CA",
      avatar: "/api/placeholder/80/80",
      rating: 4.7,
      experience: 9,
      hourlyRate: 90,
      skills: ["Digital Marketing", "SEO/SEM", "Social Media", "Content Marketing", "Analytics"],
      bio: "Results-driven marketing professional with proven track record in digital growth strategies.",
      availability: "Available",
      projects: 44,
      reviews: 28,
      responseTime: "1 hour",
      successRate: 96
    },
    {
      id: 10,
      name: "Michael Chang",
      title: "Corporate Legal Counsel",
      location: "Boston, MA",
      avatar: "/api/placeholder/80/80",
      rating: 4.8,
      experience: 11,
      hourlyRate: 150,
      skills: ["Contract Law", "Corporate Law", "Compliance", "Legal Research", "Risk Management"],
      bio: "Corporate attorney with extensive experience in contract negotiation and regulatory compliance.",
      availability: "Busy",
      projects: 29,
      reviews: 18,
      responseTime: "4 hours",
      successRate: 97
    },
    {
      id: 11,
      name: "Jennifer Adams",
      title: "Operations Manager & Process Expert",
      location: "Denver, CO",
      avatar: "/api/placeholder/80/80",
      rating: 4.6,
      experience: 13,
      hourlyRate: 80,
      skills: ["Project Management", "Process Improvement", "Lean Six Sigma", "Operations Strategy", "KPI Management"],
      bio: "Operations specialist focused on streamlining processes and improving organizational efficiency.",
      availability: "Available",
      projects: 36,
      reviews: 24,
      responseTime: "2 hours",
      successRate: 93
    },
    {
      id: 12,
      name: "Dr. Rachel Green",
      title: "Healthcare Consultant & Medical Writer",
      location: "Philadelphia, PA",
      avatar: "/api/placeholder/80/80",
      rating: 4.9,
      experience: 8,
      hourlyRate: 125,
      skills: ["Clinical Research", "Medical Writing", "Healthcare Administration", "Regulatory Affairs", "Patient Care"],
      bio: "Medical professional with expertise in clinical research and healthcare technology implementation.",
      availability: "Available",
      projects: 31,
      reviews: 19,
      responseTime: "3 hours",
      successRate: 99
    }
  ]

  const skillOptions = [
    // Tech Skills
    "React", "Node.js", "TypeScript", "Python", "AWS", "GraphQL", "Figma",
    "Design Systems", "Machine Learning", "Kubernetes", "Docker", "Solidity",
    "React Native", "Flutter", "TensorFlow", "SQL", "CI/CD", "Web3",
    // HR Skills
    "Recruitment", "Talent Acquisition", "HRIS", "Performance Management", "Employee Relations",
    "Compensation", "Training & Development", "HR Analytics",
    // Finance & Accounting
    "Financial Analysis", "Bookkeeping", "Tax Preparation", "Auditing", "QuickBooks",
    "Excel", "Financial Planning", "Budget Management",
    // Marketing & Sales
    "Digital Marketing", "SEO/SEM", "Social Media", "Content Marketing", "Sales Strategy",
    "CRM", "Email Marketing", "Analytics",
    // Legal
    "Contract Law", "Corporate Law", "Compliance", "Legal Research", "Intellectual Property",
    "Employment Law", "Regulatory Affairs", "Risk Management",
    // Operations
    "Project Management", "Process Improvement", "Supply Chain", "Quality Assurance",
    "Lean Six Sigma", "Operations Strategy", "Vendor Management", "KPI Management",
    // Healthcare
    "Clinical Research", "Medical Writing", "Healthcare Administration", "Nursing",
    "Telemedicine", "Medical Coding", "Patient Care", "Healthcare IT",
    // Business & Strategy
    "Business Analysis", "Strategic Planning", "Market Research", "Consulting",
    "Business Development", "Competitive Analysis", "Financial Modeling", "Growth Strategy",
    // Content & Writing
    "Content Writing", "Copywriting", "Technical Writing", "Journalism", "Editing",
    "Proofreading", "Grant Writing", "Creative Writing"
  ]

  const activeFilterCount = selectedSkills.length +
    (experienceRange[0] > 0 || experienceRange[1] < 15 ? 1 : 0) +
    (salaryRange[0] > 30000 || salaryRange[1] < 200000 ? 1 : 0)

  // Pagination calculations
  const totalTalents = talents.length
  const totalPages = Math.ceil(totalTalents / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentTalents = talents.slice(startIndex, endIndex)

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy)
    setSortOrder(newSortOrder)
    setCurrentPage(1) // Reset to first page when sorting changes
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Smooth scroll to top of results
    document.querySelector('#talent-results')?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1) // Reset to first page when items per page changes
  }

  return (
    <div className="pt-16">
      {/* Premium Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-background to-primary/10" />
        <div className="absolute inset-0 opacity-10 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px]" />
        
        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-card/95 backdrop-blur-sm border border-border/50 text-primary text-sm font-medium shadow-lg mb-6">
              <Users className="w-4 h-4 mr-2" />
              <span>Discover Top Talent</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground via-foreground/90 to-foreground/70 bg-clip-text text-transparent">
              Find Exceptional
              <span className="block bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent">
                Talent
              </span>
            </h1>
            
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Connect with world-class professionals across technology, business, healthcare, legal, finance, and more. Find the perfect expert for your project.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-primary" />
                  <span>15,000+ Professionals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Award className="w-4 h-4 text-primary" />
                  <span>50+ Expertise Areas</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-primary" />
                  <span>98% Success Rate</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Search and Filters Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-16 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <h2 className="text-2xl font-bold">Browse Talent</h2>
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="theme-glow">
                  {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied
                </Badge>
              )}
            </div>
            
            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="hidden md:flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>

              {/* Mobile Filter Toggle */}
              <div className="lg:hidden">
                <Sheet open={showMobileFilters} onOpenChange={setShowMobileFilters}>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm" className="button-enhanced">
                      <Filter className="w-4 h-4 mr-2" />
                      Filters
                      {activeFilterCount > 0 && (
                        <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                          {activeFilterCount}
                        </Badge>
                      )}
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-80 overflow-y-auto">
                    <SheetHeader>
                      <SheetTitle>Talent Filters</SheetTitle>
                    </SheetHeader>
                    <div className="mt-6">
                      <TalentFilters
                        selectedSkills={selectedSkills}
                        setSelectedSkills={setSelectedSkills}
                        experienceRange={experienceRange}
                        setExperienceRange={setExperienceRange}
                        salaryRange={salaryRange}
                        setSalaryRange={setSalaryRange}
                        onFiltersChange={() => setShowMobileFilters(false)}
                      />
                    </div>
                  </SheetContent>
                </Sheet>
              </div>

              {/* Desktop Filter Toggle */}
              <div className="hidden lg:block">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDesktopFilters(!showDesktopFilters)}
                  className="button-enhanced"
                >
                  {showDesktopFilters ? (
                    <>
                      <X className="w-4 h-4 mr-2" />
                      Hide Filters
                    </>
                  ) : (
                    <>
                      <Filter className="w-4 h-4 mr-2" />
                      Show Filters
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search by skills, title, or name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-enhanced pl-10"
              />
            </div>
            <TalentSorting
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
            />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="flex gap-8">
          {/* Desktop Filters Sidebar */}
          <aside className={`hidden lg:block transition-all duration-300 ${
            showDesktopFilters ? 'w-80' : 'w-0 overflow-hidden'
          }`}>
            {showDesktopFilters && (
              <div className="sticky top-40">
                <TalentFilters
                  selectedSkills={selectedSkills}
                  setSelectedSkills={setSelectedSkills}
                  experienceRange={experienceRange}
                  setExperienceRange={setExperienceRange}
                  salaryRange={salaryRange}
                  setSalaryRange={setSalaryRange}
                  showApplyButton={false}
                />
              </div>
            )}
          </aside>

          {/* Talent Grid/List */}
          <div className="flex-1" id="talent-results">
            <TalentResultsSummary
              totalResults={totalTalents}
              searchQuery={searchQuery}
              activeFilters={activeFilterCount}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />

            {/* Talent Grid/List */}
            <AnimatePresence mode="wait">
              <motion.div
                key={viewMode}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6' : 'space-y-4'}
              >
                {currentTalents.map((talent, index) => (
                  <motion.div
                    key={talent.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <TalentCard
                      talent={talent}
                      viewMode={viewMode}
                      onViewProfile={setSelectedTalent}
                      onContact={(talent) => {
                        // Handle contact logic
                        console.log('Contact talent:', talent.name)
                      }}
                      onSave={(talent) => {
                        // Handle save logic
                        console.log('Save talent:', talent.name)
                      }}
                    />
                  </motion.div>
                ))}
              </motion.div>
            </AnimatePresence>

            {/* Pagination */}
            {totalPages > 1 && (
              <TalentPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalTalents}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>
      </main>

      {/* Talent Detail Modal */}
      <TalentDetailModal
        talent={selectedTalent}
        isOpen={!!selectedTalent}
        onClose={() => setSelectedTalent(null)}
        onContact={(talent) => {
          // Handle contact logic
          console.log('Contact talent:', talent.name)
          setSelectedTalent(null)
        }}
        onHire={(talent) => {
          // Handle hire logic
          console.log('Hire talent:', talent.name)
          setSelectedTalent(null)
        }}
      />
    </div>
  )
}
