'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { CompanyFilters } from '@/components/companies/company-filters'
import { CompanyCard } from '@/components/companies/company-card'
import { CompanyDetailModal } from '@/components/companies/company-detail-modal'
import { TalentPagination, TalentSorting, TalentResultsSummary } from '@/components/talent/talent-pagination'
import { companies } from '@/lib/company-data'
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  MapPin, 
  Star, 
  Building, 
  Users,
  Calendar,
  TrendingUp,
  Award,
  Target,
  Briefcase,
  Globe,
  ChevronDown
} from 'lucide-react'

export default function CompaniesPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [showDesktopFilters, setShowDesktopFilters] = useState(true)
  const [sortBy, setSortBy] = useState('relevance')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(12)
  const [selectedCompany, setSelectedCompany] = useState<any>(null)

  // Filter states
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([])
  const [companySizeRange, setCompanySizeRange] = useState([1, 10000])
  const [foundedRange, setFoundedRange] = useState([1900, 2024])

  // Company data from service - in real app this would come from API
  // Companies are now imported from lib/company-data

  const activeFilterCount = selectedIndustries.length + 
    (companySizeRange[0] > 1 || companySizeRange[1] < 10000 ? 1 : 0) +
    (foundedRange[0] > 1900 || foundedRange[1] < 2024 ? 1 : 0)

  // Pagination calculations
  const totalCompanies = companies.length
  const totalPages = Math.ceil(totalCompanies / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentCompanies = companies.slice(startIndex, endIndex)

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy)
    setSortOrder(newSortOrder)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    document.querySelector('#companies-results')?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)
  }

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-background to-primary/10" />
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px]" />
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent"
            >
              Discover Amazing Companies
            </motion.h1>
            
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto"
            >
              Explore innovative companies across all industries. Find your next career opportunity with organizations that match your values and goals.
            </motion.p>

            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap items-center justify-center gap-6 text-sm text-muted-foreground"
            >
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-primary" />
                <span>5,000+ Companies</span>
              </div>
              <div className="flex items-center space-x-2">
                <Award className="w-4 h-4 text-primary" />
                <span>50+ Industries</span>
              </div>
              <div className="flex items-center space-x-2">
                <Target className="w-4 h-4 text-primary" />
                <span>10,000+ Open Positions</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-16 z-40">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search companies by name, industry, or location..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 input-enhanced"
              />
            </div>

            {/* Desktop Controls */}
            <div className="hidden lg:flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => setShowDesktopFilters(!showDesktopFilters)}
                className={showDesktopFilters ? 'bg-primary/10 border-primary/50' : ''}
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-2 theme-glow">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>

              <TalentSorting
                sortBy={sortBy}
                sortOrder={sortOrder}
                onSortChange={handleSortChange}
              />

              <div className="flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Mobile Controls */}
            <div className="flex lg:hidden items-center space-x-2">
              <Sheet open={showMobileFilters} onOpenChange={setShowMobileFilters}>
                <SheetTrigger asChild>
                  <Button variant="outline" className="flex-1">
                    <Filter className="w-4 h-4 mr-2" />
                    Filters
                    {activeFilterCount > 0 && (
                      <Badge variant="secondary" className="ml-2 theme-glow">
                        {activeFilterCount}
                      </Badge>
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-80 overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>Company Filters</SheetTitle>
                  </SheetHeader>
                  <div className="mt-6">
                    <CompanyFilters
                      selectedIndustries={selectedIndustries}
                      setSelectedIndustries={setSelectedIndustries}
                      companySizeRange={companySizeRange}
                      setCompanySizeRange={setCompanySizeRange}
                      foundedRange={foundedRange}
                      setFoundedRange={setFoundedRange}
                      onFiltersChange={() => setShowMobileFilters(false)}
                    />
                  </div>
                </SheetContent>
              </Sheet>

              <div className="flex items-center space-x-1 bg-muted/50 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="flex gap-8">
          {/* Desktop Filters Sidebar */}
          <aside className={`hidden lg:block transition-all duration-300 ${
            showDesktopFilters ? 'w-80' : 'w-0 overflow-hidden'
          }`}>
            {showDesktopFilters && (
              <div className="sticky top-40">
                <CompanyFilters
                  selectedIndustries={selectedIndustries}
                  setSelectedIndustries={setSelectedIndustries}
                  companySizeRange={companySizeRange}
                  setCompanySizeRange={setCompanySizeRange}
                  foundedRange={foundedRange}
                  setFoundedRange={setFoundedRange}
                  showApplyButton={false}
                />
              </div>
            )}
          </aside>

          {/* Companies Grid/List */}
          <div className="flex-1" id="companies-results">
            <TalentResultsSummary
              totalResults={totalCompanies}
              searchQuery={searchQuery}
              activeFilters={activeFilterCount}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />

            {/* Companies Grid/List */}
            <AnimatePresence mode="wait">
              <motion.div
                key={viewMode}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6' : 'space-y-4'}
              >
                {currentCompanies.map((company, index) => (
                  <motion.div
                    key={company.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <CompanyCard
                      company={company}
                      viewMode={viewMode}
                      onViewProfile={setSelectedCompany}
                      onFollow={(company) => {
                        console.log('Follow company:', company.name)
                      }}
                      onViewJobs={(company) => {
                        console.log('View jobs for:', company.name)
                      }}
                    />
                  </motion.div>
                ))}
              </motion.div>
            </AnimatePresence>

            {/* Pagination */}
            {totalPages > 1 && (
              <TalentPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalCompanies}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>
      </main>

      {/* Company Detail Modal */}
      <CompanyDetailModal
        company={selectedCompany}
        isOpen={!!selectedCompany}
        onClose={() => setSelectedCompany(null)}
        onFollow={(company) => {
          console.log('Follow company:', company.name)
          setSelectedCompany(null)
        }}
        onViewJobs={(company) => {
          console.log('View jobs for:', company.name)
          setSelectedCompany(null)
        }}
      />
    </div>
  )
}
