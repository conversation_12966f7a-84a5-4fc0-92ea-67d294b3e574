'use client'

import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useJobsStore, useAuthStore } from '@/stores'
import { Navigation } from '@/components/navigation'
import { Footer } from '@/components/footer'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  ArrowLeft, 
  Bookmark, 
  BookmarkCheck, 
  Share2, 
  MapPin, 
  Building, 
  Clock, 
  DollarSign,
  Users,
  Calendar,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Heart,
  Briefcase,
  GraduationCap,
  Award,
  Globe,
  Phone,
  Mail,
  Star,
  TrendingUp,
  Target,
  Shield,
  Zap,
  Coffee,
  Car,
  Home,
  Plane,
  Gift,
  Eye,
  ThumbsUp,
  MessageCircle,
  Send
} from 'lucide-react'
import { cn } from '@/lib/utils'

export default function EnhancedJobDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const jobId = params.id as string
  
  const { isAuthenticated, user } = useAuthStore()
  const { 
    currentJob,
    jobLoading,
    error,
    savedJobs,
    appliedJobs,
    getJobById,
    saveJob,
    unsaveJob,
    saveLoading,
    clearError
  } = useJobsStore()

  const [showFullDescription, setShowFullDescription] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const isSaved = savedJobs.includes(jobId)
  const isApplied = appliedJobs.includes(jobId)

  // Load job details
  useEffect(() => {
    if (jobId) {
      getJobById(jobId)
    }
  }, [jobId, getJobById])

  // Handle save/unsave job
  const handleSaveJob = async () => {
    if (!isAuthenticated) {
      router.push('/signin')
      return
    }

    try {
      if (isSaved) {
        await unsaveJob(jobId)
      } else {
        await saveJob(jobId)
      }
    } catch (error) {
      console.error('Save job error:', error)
    }
  }

  // Handle apply to job
  const handleApplyToJob = () => {
    if (!isAuthenticated) {
      router.push('/signin')
      return
    }

    if (user?.role !== 'job_seeker') {
      // Show error or redirect
      return
    }

    router.push(`/jobs/${jobId}/apply`)
  }

  // Handle share job
  const handleShareJob = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: currentJob?.title,
          text: `Check out this job opportunity: ${currentJob?.title} at ${currentJob?.company.name}`,
          url: window.location.href,
        })
      } catch (error) {
        console.error('Share failed:', error)
      }
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href)
      // Show toast notification
    }
  }

  // Format posted date
  const formatPostedDate = () => {
    if (!currentJob?.postedAt) return 'Recently'
    
    const now = new Date()
    const posted = new Date(currentJob.postedAt)
    const diffTime = Math.abs(now.getTime() - posted.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return '1 day ago'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
    return `${Math.ceil(diffDays / 30)} months ago`
  }

  // Format salary
  const formatSalary = () => {
    if (!currentJob?.salary?.min && !currentJob?.salary?.max) return 'Salary not disclosed'
    
    const { min, max, currency = 'USD', period = 'yearly' } = currentJob.salary
    const formatAmount = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    }

    if (min && max) {
      return `${formatAmount(min)} - ${formatAmount(max)} per ${period.replace('ly', '')}`
    } else if (min) {
      return `From ${formatAmount(min)} per ${period.replace('ly', '')}`
    } else if (max) {
      return `Up to ${formatAmount(max)} per ${period.replace('ly', '')}`
    }
  }

  // Calculate application urgency
  const getApplicationUrgency = () => {
    if (!currentJob?.applicationDeadline) return null
    
    const deadline = new Date(currentJob.applicationDeadline)
    const now = new Date()
    const daysLeft = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysLeft <= 0) return { level: 'expired', message: 'Application deadline has passed' }
    if (daysLeft <= 3) return { level: 'urgent', message: `Only ${daysLeft} days left to apply` }
    if (daysLeft <= 7) return { level: 'soon', message: `${daysLeft} days left to apply` }
    return { level: 'normal', message: `Application deadline: ${deadline.toLocaleDateString()}` }
  }

  // Loading state
  if (jobLoading) {
    return <PageLoader message="Loading job details..." fullScreen />
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <ErrorAlert
            type="error"
            message={error.message || 'Failed to load job details'}
            dismissible
            onDismiss={clearError}
          />
          <div className="mt-6">
            <Button
              variant="outline"
              onClick={() => router.push('/jobs')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Jobs</span>
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  // Job not found
  if (!currentJob) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Job Not Found</h1>
            <p className="text-muted-foreground mb-6">
              The job you're looking for doesn't exist or has been removed.
            </p>
            <Button onClick={() => router.push('/jobs')}>
              Browse All Jobs
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  const urgency = getApplicationUrgency()

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-background to-secondary/5 border-b">
        <div className="container mx-auto px-4 py-8">
          {/* Breadcrumb */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/jobs')}
              className="p-0 h-auto font-normal"
            >
              Jobs
            </Button>
            <span>/</span>
            <span className="text-foreground">{currentJob.title}</span>
          </div>

          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            {/* Job Header */}
            <div className="flex-1">
              <div className="flex items-start space-x-4 mb-4">
                {/* Company Logo */}
                <Avatar className="w-16 h-16 border">
                  <AvatarImage 
                    src={currentJob.company.logo} 
                    alt={currentJob.company.name}
                  />
                  <AvatarFallback className="text-lg font-semibold">
                    {currentJob.company.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>

                {/* Job Info */}
                <div className="flex-1 min-w-0">
                  <h1 className="text-3xl font-bold mb-2">{currentJob.title}</h1>
                  <div className="flex flex-wrap items-center gap-4 text-muted-foreground mb-4">
                    <div className="flex items-center space-x-1">
                      <Building className="w-4 h-4" />
                      <span className="font-medium">{currentJob.company.name}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>
                        {currentJob.location.remote ? 'Remote' : 
                         `${currentJob.location.city}, ${currentJob.location.state}`}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{formatPostedDate()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{currentJob.viewsCount} views</span>
                    </div>
                  </div>

                  {/* Job Meta */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    <Badge variant="secondary">
                      {currentJob.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                    <Badge variant="outline">
                      {currentJob.level.replace(/\b\w/g, l => l.toUpperCase())} Level
                    </Badge>
                    {currentJob.location.remote && (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        Remote
                      </Badge>
                    )}
                  </div>

                  {/* Salary */}
                  {currentJob.salary && (
                    <div className="flex items-center space-x-2 text-lg font-semibold text-green-600 mb-4">
                      <DollarSign className="w-5 h-5" />
                      <span>{formatSalary()}</span>
                    </div>
                  )}

                  {/* Application Urgency */}
                  {urgency && (
                    <div className={cn(
                      'flex items-center space-x-2 text-sm px-3 py-2 rounded-lg',
                      urgency.level === 'urgent' && 'bg-red-50 text-red-700 border border-red-200',
                      urgency.level === 'soon' && 'bg-orange-50 text-orange-700 border border-orange-200',
                      urgency.level === 'normal' && 'bg-blue-50 text-blue-700 border border-blue-200',
                      urgency.level === 'expired' && 'bg-gray-50 text-gray-700 border border-gray-200'
                    )}>
                      <AlertCircle className="w-4 h-4" />
                      <span>{urgency.message}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row lg:flex-col gap-3 lg:w-48">
              {!isApplied ? (
                <Button 
                  onClick={handleApplyToJob}
                  size="lg"
                  className="w-full"
                  disabled={urgency?.level === 'expired'}
                >
                  <Send className="w-4 h-4 mr-2" />
                  Apply Now
                </Button>
              ) : (
                <Button 
                  variant="outline"
                  size="lg"
                  className="w-full"
                  onClick={() => router.push('/applications')}
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Applied
                </Button>
              )}

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleSaveJob}
                  disabled={saveLoading}
                  className="flex-1"
                >
                  {isSaved ? (
                    <BookmarkCheck className="w-4 h-4 mr-2" />
                  ) : (
                    <Bookmark className="w-4 h-4 mr-2" />
                  )}
                  {isSaved ? 'Saved' : 'Save'}
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleShareJob}
                  className="flex-1"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="requirements">Requirements</TabsTrigger>
                <TabsTrigger value="benefits">Benefits</TabsTrigger>
                <TabsTrigger value="company">Company</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                {/* Job Description */}
                <Card>
                  <CardHeader>
                    <CardTitle>Job Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={cn(
                      'prose prose-sm max-w-none',
                      !showFullDescription && 'line-clamp-6'
                    )}>
                      <p className="whitespace-pre-wrap">{currentJob.description}</p>
                    </div>
                    {currentJob.description.length > 500 && (
                      <Button
                        variant="link"
                        onClick={() => setShowFullDescription(!showFullDescription)}
                        className="mt-2 p-0 h-auto"
                      >
                        {showFullDescription ? 'Show less' : 'Show more'}
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="requirements" className="space-y-6">
                {/* Requirements */}
                <Card>
                  <CardHeader>
                    <CardTitle>Requirements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {currentJob.requirements && currentJob.requirements.length > 0 ? (
                      <ul className="space-y-2">
                        {currentJob.requirements.map((requirement, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{requirement}</span>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-muted-foreground">No specific requirements listed.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="benefits" className="space-y-6">
                {/* Benefits */}
                <Card>
                  <CardHeader>
                    <CardTitle>Benefits & Perks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {currentJob.benefits && currentJob.benefits.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {currentJob.benefits.map((benefit, index) => (
                          <div key={index} className="flex items-start space-x-2">
                            <Gift className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{benefit}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">Benefits information not available.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="company" className="space-y-6">
                {/* Company Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>About {currentJob.company.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{currentJob.company.location}</span>
                    </div>
                    
                    <Button variant="outline" className="w-full">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Company Profile
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Job Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Applications</span>
                  <span className="font-medium">{currentJob.applicationsCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Views</span>
                  <span className="font-medium">{currentJob.viewsCount}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Posted</span>
                  <span className="font-medium">
                    {new Date(currentJob.postedAt).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Application Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Application Status</CardTitle>
              </CardHeader>
              <CardContent>
                {isApplied ? (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Application Submitted</span>
                    </div>
                    <Progress value={25} className="h-2" />
                    <p className="text-xs text-muted-foreground">
                      Your application is being reviewed by the hiring team.
                    </p>
                    <Button variant="outline" size="sm" className="w-full">
                      View Application Status
                    </Button>
                  </div>
                ) : (
                  <div className="text-center space-y-3">
                    <p className="text-sm text-muted-foreground">
                      Ready to take the next step in your career?
                    </p>
                    <Button 
                      onClick={handleApplyToJob}
                      className="w-full"
                      disabled={urgency?.level === 'expired'}
                    >
                      Apply Now
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
