import { Metadata } from 'next'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'

export const metadata: Metadata = {
  title: {
    template: '%s | Company Dashboard',
    default: 'Dashboard | JobPortal',
  },
  description: 'Manage your company hiring process, job postings, and applications.',
}

export default function DashboardLayoutWrapper({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ProtectedRoute requiredRole="company_admin" fallbackPath="/login">
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </ProtectedRoute>
  )
}
