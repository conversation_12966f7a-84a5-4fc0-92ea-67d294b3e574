"use client"
import { Navigation } from "@/components/navigation"
import { HeroSection } from "@/components/hero-section"
import { AIMatchingSection } from "@/components/ai-matching-section"
import { InteractiveJobSearch } from "@/components/interactive-job-search"
import { LiveJobFeed } from "@/components/live-job-feed"
import { SalaryInsights } from "@/components/salary-insights"
import { SkillsAssessment } from "@/components/skills-assessment"
import { CompanyShowcase } from "@/components/company-showcase"
import { CareerPathVisualizer } from "@/components/career-path-visualizer"
import { TestimonialsSection } from "@/components/testimonials-section"
import { Footer } from "@/components/footer"
import { BackgroundPattern } from "@/components/background-pattern"
import { FloatingActionButton } from "@/components/floating-action-button"
import { NotificationSystem } from "@/components/notification-system"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      <BackgroundPattern />
      <Navigation />
      <main className="relative">
        <HeroSection />
        <AIMatchingSection />
        <InteractiveJobSearch />
        <LiveJobFeed />
        <SalaryInsights />
        <SkillsAssessment />
        <CareerPathVisualizer />
        <CompanyShowcase />
        <TestimonialsSection />
      </main>
      <Footer />
      <FloatingActionButton />
      <NotificationSystem />
    </div>
  )
}
