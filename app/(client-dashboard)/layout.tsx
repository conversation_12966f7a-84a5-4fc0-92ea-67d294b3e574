"use client"

import React from "react"
import { ClientDashboardTopbar } from "@/components/client/client-dashboard-topbar"
import { ClientDashboardSidebar } from "@/components/client/client-dashboard-sidebar"
import { BackgroundPattern } from "@/components/background-pattern"
import { NotificationSystem } from "@/components/notification-system"
import { SidebarProvider } from "@/components/ui/sidebar"

interface ClientDashboardLayoutProps {
  children: React.ReactNode
}

export default function ClientDashboardLayout({
  children
}: ClientDashboardLayoutProps) {
  return (
    <SidebarProvider>
      <div className="min-h-screen bg-background">
        <BackgroundPattern />
        
        {/* Client Dashboard Layout Structure */}
        <div className="flex h-screen overflow-hidden w-full">
          {/* Sidebar */}
          <ClientDashboardSidebar />

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden min-w-0 w-full">
            {/* Top Navigation */}
            <ClientDashboardTopbar />
            
            {/* Main Content */}
            <main className="flex-1 overflow-auto relative">
              <div className="h-full w-full max-w-none p-4 pr-2 space-y-6">
                {children}
              </div>
            </main>
          </div>
        </div>
        
        <NotificationSystem />
      </div>
    </SidebarProvider>
  )
}
