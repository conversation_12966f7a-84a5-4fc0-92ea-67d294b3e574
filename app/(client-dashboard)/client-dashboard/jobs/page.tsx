"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useClientJobsStore } from "@/stores/client-jobs.store"
import { useClientDashboardStore } from "@/stores/client-dashboard.store"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Search,
  MapPin,
  Clock,
  DollarSign,
  Briefcase,
  Heart,
  Filter,
  SlidersHorizontal,
  Building2,
  Calendar,
  Users,
  TrendingUp,
  Loader2,
  Star,
  ExternalLink
} from "lucide-react"

export default function ClientJobsPage() {
  const {
    jobs,
    loading,
    searchQuery,
    filters,
    currentPage,
    totalPages,
    totalCount,
    setSearchQuery,
    setFilters,
    fetchJobs,
    searchJob<PERSON>,
    saveJob,
    unsaveJob,
    savedJobs,
    setPage
  } = useClientJobsStore()

  const { quickApplyToJob, quickActionsLoading } = useClientDashboardStore()

  const [showFilters, setShowFilters] = useState(false)
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)

  useEffect(() => {
    // Fetch jobs on component mount
    fetchJobs()
  }, [fetchJobs])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    searchJobs(localSearchQuery)
  }

  const handleFilterChange = (key: string, value: any) => {
    setFilters({ [key]: value })
  }

  const isJobSaved = (jobId: string) => {
    return savedJobs.some(saved => saved.jobId === jobId)
  }

  const handleSaveJob = async (jobId: string) => {
    if (isJobSaved(jobId)) {
      await unsaveJob(jobId)
    } else {
      await saveJob(jobId)
    }
  }

  // Loading state
  if (loading && jobs.length === 0) {
    return (
      <div className="space-y-6">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="flex space-x-2">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>

          {/* Search Skeleton */}
          <Card>
            <CardContent className="pt-6">
              <div className="grid gap-4 md:grid-cols-4">
                <Skeleton className="h-10 md:col-span-2" />
                <Skeleton className="h-10" />
                <Skeleton className="h-10" />
              </div>
            </CardContent>
          </Card>

          {/* Jobs Skeleton */}
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-6 w-64" />
                        <Skeleton className="h-4 w-48" />
                        <Skeleton className="h-4 w-96" />
                      </div>
                      <Skeleton className="h-8 w-8" />
                    </div>
                    <div className="flex items-center space-x-4">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
      </div>
    )
  }

  // Mock job data for fallback - in real app, this would come from API/store
  const fallbackJobs = [
    {
      id: 1,
      title: "Senior React Developer",
      company: "TechCorp Inc.",
      location: "San Francisco, CA",
      type: "Full-time",
      salary: "$120,000 - $150,000",
      postedDate: "2 days ago",
      description: "We're looking for a Senior React Developer to join our growing team...",
      requirements: ["React", "TypeScript", "Node.js", "GraphQL"],
      remote: true,
      urgent: false,
      featured: true,
      applicants: 45,
      matchScore: 95
    },
    {
      id: 2,
      title: "Frontend Engineer",
      company: "StartupXYZ",
      location: "Remote",
      type: "Full-time",
      salary: "$90,000 - $120,000",
      postedDate: "1 week ago",
      description: "Join our innovative team building the next generation of web applications...",
      requirements: ["JavaScript", "React", "CSS", "REST APIs"],
      remote: true,
      urgent: true,
      featured: false,
      applicants: 23,
      matchScore: 88
    },
    {
      id: 3,
      title: "Full Stack Developer",
      company: "BigTech Solutions",
      location: "New York, NY",
      type: "Contract",
      salary: "$80 - $100/hour",
      postedDate: "3 days ago",
      description: "We need a skilled Full Stack Developer for a 6-month project...",
      requirements: ["React", "Node.js", "MongoDB", "AWS"],
      remote: false,
      urgent: false,
      featured: false,
      applicants: 67,
      matchScore: 82
    },
    {
      id: 4,
      title: "JavaScript Developer",
      company: "WebSolutions Co.",
      location: "Austin, TX",
      type: "Part-time",
      salary: "$60,000 - $80,000",
      postedDate: "5 days ago",
      description: "Looking for a part-time JavaScript developer to help with various projects...",
      requirements: ["JavaScript", "HTML", "CSS", "jQuery"],
      remote: true,
      urgent: false,
      featured: false,
      applicants: 12,
      matchScore: 75
    }
  ]

  const displayJobs = jobs.length > 0 ? jobs : fallbackJobs

  return (
    <div className="space-y-4 md:space-y-6">
        {/* Header - Mobile Optimized */}
        <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Browse Jobs</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              <span className="hidden md:inline">Discover opportunities that match your skills and preferences</span>
              <span className="md:hidden">Find jobs that match your skills</span>
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => setShowFilters(!showFilters)} className="flex-1 md:flex-none">
              <SlidersHorizontal className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">Filters</span>
              <span className="sm:hidden">Filter</span>
            </Button>
            <Button className="flex-1 md:flex-none">
              <Heart className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">Saved Jobs</span>
              <span className="sm:hidden">Saved</span>
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="pt-6">
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="grid gap-4 md:grid-cols-4">
                <div className="md:col-span-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search jobs, companies, or keywords..."
                      value={localSearchQuery}
                      onChange={(e) => setLocalSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select
                  value={filters.location || ""}
                  onValueChange={(value) => handleFilterChange('location', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Locations</SelectItem>
                    <SelectItem value="remote">Remote</SelectItem>
                    <SelectItem value="san-francisco">San Francisco, CA</SelectItem>
                    <SelectItem value="new-york">New York, NY</SelectItem>
                    <SelectItem value="austin">Austin, TX</SelectItem>
                    <SelectItem value="seattle">Seattle, WA</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={filters.jobType?.[0] || ""}
                  onValueChange={(value) => handleFilterChange('jobType', value ? [value] : [])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Job Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Types</SelectItem>
                    <SelectItem value="full-time">Full-time</SelectItem>
                    <SelectItem value="part-time">Part-time</SelectItem>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="freelance">Freelance</SelectItem>
                    <SelectItem value="internship">Internship</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {showFilters && (
                <div className="grid gap-4 md:grid-cols-3 pt-4 border-t">
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Experience Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="entry">Entry Level</SelectItem>
                      <SelectItem value="mid">Mid Level</SelectItem>
                      <SelectItem value="senior">Senior Level</SelectItem>
                      <SelectItem value="executive">Executive</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Salary Range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0-50k">$0 - $50,000</SelectItem>
                      <SelectItem value="50k-100k">$50,000 - $100,000</SelectItem>
                      <SelectItem value="100k-150k">$100,000 - $150,000</SelectItem>
                      <SelectItem value="150k+">$150,000+</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Company Size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="startup">Startup (1-50)</SelectItem>
                      <SelectItem value="small">Small (51-200)</SelectItem>
                      <SelectItem value="medium">Medium (201-1000)</SelectItem>
                      <SelectItem value="large">Large (1000+)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              <Button type="submit" className="w-full md:w-auto">
                <Search className="w-4 h-4 mr-2" />
                Search Jobs
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <div className="flex items-center justify-between">
          <p className="text-muted-foreground">
            Showing {displayJobs.length} of {totalCount || displayJobs.length} jobs
            {searchQuery && ` for "${searchQuery}"`}
          </p>
          <Select defaultValue="relevance">
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Most Relevant</SelectItem>
              <SelectItem value="date">Most Recent</SelectItem>
              <SelectItem value="salary">Highest Salary</SelectItem>
              <SelectItem value="company">Company A-Z</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Job Listings */}
        <div className="space-y-4">
          {loading && (
            <div className="text-center py-8">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">Loading jobs...</p>
            </div>
          )}

          {!loading && displayJobs.map((job) => (
            <Card key={job.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="text-lg font-semibold hover:text-primary cursor-pointer">
                            {job.title}
                          </h3>
                          {job.featured && (
                            <Badge variant="secondary" className="text-xs">
                              Featured
                            </Badge>
                          )}
                          {job.urgent && (
                            <Badge variant="destructive" className="text-xs">
                              Urgent
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-xs">
                            {job.matchScore}% match
                          </Badge>
                        </div>
                        <p className="text-muted-foreground font-medium">{job.company}</p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSaveJob(job.id)}
                        className={isJobSaved(job.id) ? "text-red-500" : ""}
                      >
                        <Heart className={`w-4 h-4 ${isJobSaved(job.id) ? 'fill-current' : ''}`} />
                      </Button>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {job.location}
                        {job.remote && (
                          <Badge variant="outline" className="ml-2 text-xs">
                            Remote
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center">
                        <Briefcase className="w-4 h-4 mr-1" />
                        {job.type}
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-1" />
                        {job.salary}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {job.postedDate}
                      </div>
                    </div>

                    <p className="text-muted-foreground line-clamp-2">
                      {job.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {job.requirements.slice(0, 4).map((req, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {req}
                          </Badge>
                        ))}
                        {job.requirements.length > 4 && (
                          <Badge variant="outline" className="text-xs">
                            +{job.requirements.length - 4} more
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {job.applicants} applicants
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 pt-2">
                      <Button
                        size="sm"
                        onClick={() => quickApplyToJob(job.id)}
                        disabled={quickActionsLoading}
                      >
                        {quickActionsLoading ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : null}
                        Apply Now
                      </Button>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Building2 className="w-4 h-4 mr-2" />
                        Company
                      </Button>
                      {job.matchScore && (
                        <Badge variant="outline" className="ml-auto">
                          <Star className="w-3 h-3 mr-1" />
                          {job.matchScore}% match
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => setPage(currentPage - 1)}
                disabled={currentPage === 1 || loading}
              >
                Previous
              </Button>
              <span className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setPage(currentPage + 1)}
                disabled={currentPage === totalPages || loading}
              >
                Next
              </Button>
            </div>

            <div className="text-sm text-muted-foreground">
              Showing {((currentPage - 1) * 20) + 1} - {Math.min(currentPage * 20, totalCount)} of {totalCount} jobs
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && displayJobs.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No jobs found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || Object.keys(filters).length > 0
                  ? "Try adjusting your search criteria or filters"
                  : "No jobs are currently available"
                }
              </p>
              {(searchQuery || Object.keys(filters).length > 0) && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setLocalSearchQuery("")
                    setSearchQuery("")
                    setFilters({})
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </CardContent>
          </Card>
        )}
    </div>
  )
}
