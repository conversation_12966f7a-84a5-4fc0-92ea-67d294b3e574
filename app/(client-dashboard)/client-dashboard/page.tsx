// app/(client-dashboard)/client-dashboard/page.tsx
"use client"

import React, { useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { useClientDashboardStore } from "@/stores/client-dashboard.store"
import { useClientStore } from "@/stores/client.store"
import { useAuthStore } from "@/stores/auth.store"
import {
  Briefcase,
  FileText,
  Heart,
  TrendingUp,
  Users,
  Eye,
  Calendar,
  Target,
  Award,
  ArrowRight,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  User,
  Search,
  BookOpen,
  MessageSquare
} from "lucide-react"

export default function ClientDashboardPage() {
  const { user } = useAuthStore()
  const { client, fetchClientProfile, profileLoading } = useClientStore()
  const {
    stats,
    recentApplications,
    recommendedJobs,
    upcomingInterviews,
    statsLoading,
    applicationsLoading,
    recommendationsLoading,
    interviewsLoading,
    fetchAllDashboardData,
    quickApplyToJob,
    quickActionsLoading
  } = useClientDashboardStore()

  useEffect(() => {
    fetchClientProfile()
    fetchAllDashboardData()
  }, [fetchClientProfile, fetchAllDashboardData])

  // Loading state
  if (profileLoading) {
    return (
      <div className="space-y-8">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Content Skeleton */}
        <div className="grid gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-64" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-start justify-between">
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                    <Skeleton className="h-6 w-20" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-64" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-40" />
                        <Skeleton className="h-3 w-28" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full space-y-8">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome back, {client?.fullName || user?.profile?.firstName || 'there'}!
          </h1>
          <p className="text-muted-foreground mt-1">
            Here's what's happening with your job search today.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={() => window.location.href = '/client-dashboard/profile'}>
            <User className="w-4 h-4 mr-2" />
            View Profile
          </Button>
          <Button
            onClick={() => window.location.href = '/client-dashboard/jobs'}
            className="bg-gradient-to-r from-primary to-primary/90"
          >
            <Search className="w-4 h-4 mr-2" />
            Find Jobs
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="w-full grid gap-6 md:grid-cols-2 lg:grid-cols-5">
        <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Profile Views</CardTitle>
            <Eye className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
              {statsLoading ? <Skeleton className="h-8 w-16" /> : stats.profileViews}
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-400">
              {statsLoading ? <Skeleton className="h-3 w-20" /> : `+${stats.profileViewsChange}% from last month`}
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">Applications</CardTitle>
            <FileText className="h-4 w-4 text-green-600 dark:text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900 dark:text-green-100">
              {statsLoading ? <Skeleton className="h-8 w-16" /> : stats.totalApplications}
            </div>
            <p className="text-xs text-green-600 dark:text-green-400">
              {statsLoading ? <Skeleton className="h-3 w-20" /> : `${stats.pendingApplications} pending`}
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Interviews</CardTitle>
            <Calendar className="h-4 w-4 text-purple-600 dark:text-purple-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
              {statsLoading ? <Skeleton className="h-8 w-16" /> : stats.interviews}
            </div>
            <p className="text-xs text-purple-600 dark:text-purple-400">
              {statsLoading ? <Skeleton className="h-3 w-20" /> : 'scheduled this week'}
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-600 dark:text-orange-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
              {statsLoading ? <Skeleton className="h-8 w-16" /> : `${stats.successRate}%`}
            </div>
            <p className="text-xs text-orange-600 dark:text-orange-400">
              {statsLoading ? <Skeleton className="h-3 w-20" /> : 'application to offer'}
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-950/20 dark:to-indigo-900/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-indigo-700 dark:text-indigo-300">Response Time</CardTitle>
            <Clock className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-900 dark:text-indigo-100">
              {statsLoading ? <Skeleton className="h-8 w-16" /> : `${stats.averageResponseTime}d`}
            </div>
            <p className="text-xs text-indigo-600 dark:text-indigo-400">
              {statsLoading ? <Skeleton className="h-3 w-20" /> : 'average response'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="w-full grid gap-8 lg:grid-cols-2">
        {/* Recent Applications */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-primary">Recent Applications</CardTitle>
                <CardDescription>Your latest job applications and their status</CardDescription>
              </div>
              <Button variant="outline" size="sm" onClick={() => window.location.href = '/client-dashboard/applications'}>
                View All
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            {applicationsLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-start justify-between p-4 border rounded-lg">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                    <Skeleton className="h-6 w-20" />
                  </div>
                ))}
              </div>
            ) : recentApplications.length > 0 ? (
              <div className="space-y-4">
                {recentApplications.map((application) => (
                  <div key={application.id} className="flex items-start justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="space-y-1">
                      <h4 className="font-medium">{application.jobTitle}</h4>
                      <p className="text-sm text-muted-foreground">{application.company}</p>
                      <p className="text-xs text-muted-foreground">Applied {application.appliedDate}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary" className="text-xs mb-1">
                        {application.status}
                      </Badge>
                      <p className="text-xs text-muted-foreground">
                        {new Date(application.appliedDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Applications Yet</h3>
                <p className="text-muted-foreground mb-4">Start applying to jobs to see your applications here</p>
                <Button onClick={() => window.location.href = '/client-dashboard/jobs'}>
                  <Search className="w-4 h-4 mr-2" />
                  Browse Jobs
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recommended Jobs */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="bg-gradient-to-r from-green-500/5 to-green-600/10 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-green-700 dark:text-green-300">Recommended Jobs</CardTitle>
                <CardDescription className="text-green-600 dark:text-green-400">Jobs that match your profile and preferences</CardDescription>
              </div>
              <Button variant="outline" size="sm" onClick={() => window.location.href = '/client-dashboard/jobs'}>
                View All
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            {recommendationsLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="space-y-2 p-4 border rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-40" />
                        <Skeleton className="h-3 w-28" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </div>
                ))}
              </div>
            ) : recommendedJobs.length > 0 ? (
              <div className="space-y-4">
                {recommendedJobs.map((job) => (
                  <div key={job.id} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-start justify-between mb-3">
                      <div className="space-y-1">
                        <h4 className="font-medium">{job.title}</h4>
                        <p className="text-sm text-muted-foreground">{job.company}</p>
                        <p className="text-xs text-muted-foreground">{job.location}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {job.matchScore}% match
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-green-600 dark:text-green-400">{job.salary}</p>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Heart className="w-3 h-3" />
                        </Button>
                        <Button 
                          size="sm" 
                          onClick={() => quickApplyToJob(job.id)}
                          disabled={quickActionsLoading}
                        >
                          {quickActionsLoading ? (
                            <Loader2 className="w-3 h-3 animate-spin" />
                          ) : (
                            'Quick Apply'
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Recommendations</h3>
                <p className="text-muted-foreground mb-4">Complete your profile to get personalized job recommendations</p>
                <Button onClick={() => window.location.href = '/client-dashboard/profile'}>
                  <User className="w-4 h-4 mr-2" />
                  Complete Profile
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-indigo-500/5 to-indigo-600/10 rounded-t-lg">
          <CardTitle className="text-indigo-700 dark:text-indigo-300">Quick Actions</CardTitle>
          <CardDescription className="text-indigo-600 dark:text-indigo-400">
            Common tasks to help advance your job search
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="w-full grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button
              variant="outline"
              className="h-24 flex-col space-y-2 border-2 hover:border-primary hover:bg-primary/5 transition-all"
              onClick={() => window.location.href = '/client-dashboard/jobs'}
            >
              <Search className="w-6 h-6" />
              <span className="text-sm font-medium">Search Jobs</span>
            </Button>
            <Button
              variant="outline"
              className="h-24 flex-col space-y-2 border-2 hover:border-primary hover:bg-primary/5 transition-all"
              onClick={() => window.location.href = '/client-dashboard/profile'}
            >
              <User className="w-6 h-6" />
              <span className="text-sm font-medium">Update Profile</span>
            </Button>
            <Button
              variant="outline"
              className="h-24 flex-col space-y-2 border-2 hover:border-primary hover:bg-primary/5 transition-all"
              onClick={() => window.location.href = '/client-dashboard/applications'}
            >
              <FileText className="w-6 h-6" />
              <span className="text-sm font-medium">View Applications</span>
            </Button>
            <Button
              variant="outline"
              className="h-24 flex-col space-y-2 border-2 hover:border-primary hover:bg-primary/5 transition-all"
            >
              <MessageSquare className="w-6 h-6" />
              <span className="text-sm font-medium">Messages</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
