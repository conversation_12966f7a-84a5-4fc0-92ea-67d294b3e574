import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { MinimalLayout } from '@/components/layouts/minimal-layout'
import { ThemeToggle } from '@/components/theme-toggle'
import { Briefcase } from 'lucide-react'

export const metadata: Metadata = {
  title: {
    template: '%s | JobPortal',
    default: 'Authentication | JobPortal',
  },
  description: 'Sign in or create an account to access JobPortal features.',
}

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <MinimalLayout className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header with logo and theme toggle */}
      <header className="absolute top-0 left-0 right-0 z-10 p-6">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Briefcase className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                JobPortal
              </span>
            </Link>

            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <div className="text-sm text-muted-foreground">
                Welcome to JobPortal
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="relative z-0">
        {children}
      </main>

      {/* Footer */}
      <footer className="absolute bottom-0 left-0 right-0 p-6">
        <div className="container mx-auto">
          <div className="text-center text-sm text-muted-foreground">
            © 2025 JobPortal. All rights reserved.
          </div>
        </div>
      </footer>
    </MinimalLayout>
  )
}
