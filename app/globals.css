@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Premium Blue Theme - Deep Ocean Professional */
  :root {
    --background: 220 30% 6%;
    --foreground: 210 20% 88%;
    --card: 220 35% 8%;
    --card-foreground: 210 20% 88%;
    --popover: 220 35% 8%;
    --popover-foreground: 210 20% 88%;
    --primary: 217 91% 65%;
    --primary-foreground: 220 30% 6%;
    --secondary: 220 25% 12%;
    --secondary-foreground: 210 20% 88%;
    --muted: 220 25% 12%;
    --muted-foreground: 210 15% 68%;
    --accent: 220 30% 15%;
    --accent-foreground: 217 91% 65%;
    --destructive: 0 75% 55%;
    --destructive-foreground: 220 30% 6%;
    --border: 220 30% 18%;
    --input: 220 30% 15%;
    --ring: 217 91% 65%;
    --radius: 0.75rem;

    /* Dashboard-specific variables */
    --dashboard-sidebar: 220 35% 8%;
    --dashboard-sidebar-foreground: 210 20% 88%;
    --dashboard-topbar: 220 30% 6%;
    --dashboard-topbar-foreground: 210 20% 88%;
    --dashboard-nav-active: 217 91% 65%;
    --dashboard-nav-active-foreground: 220 30% 6%;
  }

  /* Rich Dark Green Theme - Premium Forest Professional */
  .theme-green {
    --background: 150 25% 8%;
    --foreground: 120 15% 85%;
    --card: 150 30% 10%;
    --card-foreground: 120 15% 85%;
    --popover: 150 30% 10%;
    --popover-foreground: 120 15% 85%;
    --primary: 142 65% 55%;
    --primary-foreground: 150 25% 8%;
    --secondary: 150 20% 15%;
    --secondary-foreground: 120 15% 85%;
    --muted: 150 20% 15%;
    --muted-foreground: 120 10% 65%;
    --accent: 150 25% 18%;
    --accent-foreground: 142 65% 55%;
    --destructive: 0 75% 55%;
    --destructive-foreground: 150 25% 8%;
    --border: 150 25% 20%;
    --input: 150 25% 18%;
    --ring: 142 65% 55%;

    /* Dashboard-specific variables */
    --dashboard-sidebar: 150 25% 10%;
    --dashboard-sidebar-foreground: 120 15% 85%;
    --dashboard-topbar: 150 25% 8%;
    --dashboard-topbar-foreground: 120 15% 85%;
    --dashboard-nav-active: 142 65% 55%;
    --dashboard-nav-active-foreground: 150 25% 8%;
  }

  /* Sophisticated Dark Theme */
  .theme-dark {
    --background: 224 71% 4%;
    --foreground: 210 40% 98%;
    --card: 224 71% 6%;
    --card-foreground: 210 40% 98%;
    --popover: 224 71% 6%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 224 71% 4%;
    --secondary: 215 28% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 215 28% 17%;
    --muted-foreground: 217 11% 65%;
    --accent: 215 28% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 217 91% 60%;

    /* Dashboard-specific variables */
    --dashboard-sidebar: 224 71% 6%;
    --dashboard-sidebar-foreground: 210 40% 98%;
    --dashboard-topbar: 224 71% 4%;
    --dashboard-topbar-foreground: 210 40% 98%;
    --dashboard-nav-active: 217 91% 60%;
    --dashboard-nav-active-foreground: 224 71% 4%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Premium Component Styling */
@layer components {
  .card-enhanced {
    @apply bg-card backdrop-blur-md border border-border shadow-xl hover:shadow-2xl transition-all duration-300;
    background-color: hsl(var(--card) / 0.98);
    border-color: hsl(var(--border) / 0.7);
    box-shadow: 0 20px 25px -5px hsl(var(--primary) / 0.08), 0 10px 10px -5px hsl(var(--primary) / 0.04);
  }

  .card-enhanced:hover {
    border-color: hsl(var(--primary) / 0.3);
    box-shadow: 0 25px 50px -12px hsl(var(--primary) / 0.15), 0 20px 25px -5px hsl(var(--primary) / 0.1);
  }

  .card-professional {
    @apply backdrop-blur-md border transition-all duration-300;
    background: linear-gradient(135deg, hsl(var(--card)), hsl(var(--card) / 0.95), hsl(var(--card) / 0.9));
    border-color: hsl(var(--border) / 0.5);
    box-shadow: 0 20px 25px -5px hsl(var(--primary) / 0.1), 0 10px 10px -5px hsl(var(--primary) / 0.05);
  }

  .card-professional:hover {
    border-color: hsl(var(--primary) / 0.4);
    box-shadow: 0 25px 50px -12px hsl(var(--primary) / 0.2), 0 20px 25px -5px hsl(var(--primary) / 0.15);
  }

  .card-premium {
    @apply backdrop-blur-lg border transition-all duration-500;
    background: linear-gradient(135deg, hsl(var(--card) / 0.95), hsl(var(--background) / 0.8), hsl(var(--card) / 0.9));
    border-color: hsl(var(--primary) / 0.2);
    box-shadow: 0 25px 50px -12px hsl(var(--primary) / 0.15), 0 20px 25px -5px hsl(var(--primary) / 0.1);
  }

  .card-premium:hover {
    border-color: hsl(var(--primary) / 0.5);
    box-shadow: 0 35px 60px -12px hsl(var(--primary) / 0.25), 0 25px 50px -12px hsl(var(--primary) / 0.2);
  }

  .button-enhanced {
    @apply transition-all duration-300 hover:scale-105;
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary) / 0.95), hsl(var(--primary) / 0.9));
    box-shadow: 0 20px 25px -5px hsl(var(--primary) / 0.3), 0 10px 10px -5px hsl(var(--primary) / 0.2);
  }

  .button-enhanced:hover {
    background: linear-gradient(90deg, hsl(var(--primary) / 0.95), hsl(var(--primary)), hsl(var(--primary) / 0.95));
    box-shadow: 0 25px 50px -12px hsl(var(--primary) / 0.4), 0 20px 25px -5px hsl(var(--primary) / 0.3);
  }

  .button-premium {
    @apply transition-all duration-300 hover:scale-110 border;
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary) / 0.9), hsl(var(--primary) / 0.8));
    border-color: hsl(var(--primary) / 0.3);
    box-shadow: 0 25px 50px -12px hsl(var(--primary) / 0.4), 0 20px 25px -5px hsl(var(--primary) / 0.3);
  }

  .button-premium:hover {
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary) / 0.95), hsl(var(--primary) / 0.9));
    border-color: hsl(var(--primary) / 0.5);
    box-shadow: 0 35px 60px -12px hsl(var(--primary) / 0.5), 0 25px 50px -12px hsl(var(--primary) / 0.4);
  }

  .input-enhanced {
    @apply backdrop-blur-md border transition-all duration-300 focus:ring-4;
    background-color: hsl(var(--background) / 0.6);
    border-color: hsl(var(--border) / 0.7);
  }

  .input-enhanced:focus {
    border-color: hsl(var(--primary) / 0.7);
    --tw-ring-color: hsl(var(--primary) / 0.25);
  }

  .input-enhanced:hover {
    border-color: hsl(var(--primary) / 0.5);
  }

  .nav-enhanced {
    @apply backdrop-blur-xl border-b;
    background-color: hsl(var(--background) / 0.95);
    border-color: hsl(var(--border) / 0.6);
    box-shadow: 0 20px 25px -5px hsl(var(--primary) / 0.1), 0 10px 10px -5px hsl(var(--primary) / 0.05);
  }

  .theme-glow {
    @apply transition-all duration-500;
    box-shadow: 0 25px 50px -12px hsl(var(--primary) / 0.2), 0 20px 25px -5px hsl(var(--primary) / 0.15);
  }

  .theme-glow:hover {
    box-shadow: 0 35px 60px -12px hsl(var(--primary) / 0.3), 0 25px 50px -12px hsl(var(--primary) / 0.25);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Background patterns */
.bg-grid-pattern {
  background-image: linear-gradient(to right, hsl(var(--border)) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--border)) 1px, transparent 1px);
  background-size: 4rem 4rem;
}

.bg-dot-pattern {
  background-image: radial-gradient(circle, hsl(var(--border)) 1px, transparent 1px);
  background-size: 2rem 2rem;
}

/* Gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent;
}

/* Glass morphism effect */
.glass {
  @apply bg-background/80 backdrop-blur-sm border border-border/50;
}

/* Hover effects */
.hover-lift {
  @apply transition-transform duration-300 hover:scale-105;
}

.hover-glow {
  @apply transition-shadow duration-300 hover:shadow-xl hover:shadow-primary/20;
}

/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Hero section specific styles */
.hero-text-shadow {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Animated background overlay */
.animated-overlay {
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: -200% -200%; }
  50% { background-position: 200% 200%; }
  100% { background-position: -200% -200%; }
}

/* Dot pattern for subtle texture */
.bg-dot-pattern {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
