'use client'

export default function TestBasicPage() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Basic Image Test</h1>
      
      <div className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Regular img tag:</h3>
          <img 
            src="/images/hero/hero-main.jpg" 
            alt="Test" 
            className="w-64 h-48 object-cover border rounded"
            onLoad={() => console.log('✅ Regular img loaded')}
            onError={(e) => console.error('❌ Regular img failed', e)}
          />
        </div>

        <div>
          <h3 className="font-medium mb-2">Background image with CSS:</h3>
          <div 
            className="w-64 h-48 border rounded bg-cover bg-center"
            style={{ backgroundImage: 'url(/images/hero/hero-main.jpg)' }}
          />
        </div>

        <div>
          <h3 className="font-medium mb-2">Direct file access test:</h3>
          <a 
            href="/images/hero/hero-main.jpg" 
            target="_blank" 
            className="text-blue-500 underline"
          >
            Click to open image directly
          </a>
        </div>
      </div>
    </div>
  )
}
