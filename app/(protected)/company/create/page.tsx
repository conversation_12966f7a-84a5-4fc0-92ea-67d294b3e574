'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores'
import { CompanyProfileForm } from '@/components/company/company-profile-form'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Building, CheckCircle } from 'lucide-react'

function CompanyCreateContent() {
  const router = useRouter()
  const { user } = useAuthStore()

  // Handle successful creation
  const handleSuccess = (company: any) => {
    router.push(`/company/${company._id}`)
  }

  // Handle cancel
  const handleCancel = () => {
    router.push('/dashboard')
  }

  // Check if user already has a company
  if (user?.companyId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <CardTitle>Company Already Exists</CardTitle>
                <CardDescription>
                  You already have a company profile associated with your account.
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-muted-foreground">
                  Each company admin can only be associated with one company. 
                  You can edit your existing company profile or contact support if you need to manage multiple companies.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={() => router.push(`/company/${user.companyId}`)}
                  >
                    View Company Profile
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/dashboard')}
                  >
                    Go to Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Create Company Profile</h1>
              <p className="text-muted-foreground">
                Set up your company profile to start posting jobs and attracting candidates
              </p>
            </div>
            
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
            >
              Cancel
            </Button>
          </div>
        </div>
      </header>

      {/* Welcome Section */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Building className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <CardTitle>Welcome to JobPortal for Employers</CardTitle>
                  <CardDescription>
                    Create your company profile to start attracting top talent
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-blue-600 font-bold text-sm">1</span>
                  </div>
                  <h4 className="font-medium mb-1">Create Profile</h4>
                  <p className="text-sm text-muted-foreground">
                    Set up your company information and branding
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-green-600 font-bold text-sm">2</span>
                  </div>
                  <h4 className="font-medium mb-1">Post Jobs</h4>
                  <p className="text-sm text-muted-foreground">
                    Create and publish job openings to attract candidates
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-purple-600 font-bold text-sm">3</span>
                  </div>
                  <h4 className="font-medium mb-1">Hire Talent</h4>
                  <p className="text-sm text-muted-foreground">
                    Review applications and hire the best candidates
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Company Form */}
          <CompanyProfileForm
            mode="create"
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </div>
    </div>
  )
}

export default function CompanyCreatePage() {
  return (
    <ProtectedRoute requiredRole="company_admin">
      <CompanyCreateContent />
    </ProtectedRoute>
  )
}
