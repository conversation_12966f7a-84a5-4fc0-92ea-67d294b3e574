'use client'

import React from 'react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { AnalyticsDashboard } from '@/components/analytics/analytics-dashboard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { BarChart3, TrendingUp, Target, Award } from 'lucide-react'

function AnalyticsContent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Analytics & Reports</h1>
              <p className="text-muted-foreground">
                Track your hiring performance and optimize your recruitment process
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Welcome Card */}
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <CardTitle>Hiring Analytics</CardTitle>
                  <CardDescription>
                    Comprehensive insights into your recruitment performance
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <TrendingUp className="w-4 h-4 text-blue-600" />
                  </div>
                  <h4 className="font-medium mb-1">Performance Tracking</h4>
                  <p className="text-sm text-muted-foreground">
                    Monitor key hiring metrics and trends
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Target className="w-4 h-4 text-green-600" />
                  </div>
                  <h4 className="font-medium mb-1">Funnel Analysis</h4>
                  <p className="text-sm text-muted-foreground">
                    Track candidates through your hiring process
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <BarChart3 className="w-4 h-4 text-purple-600" />
                  </div>
                  <h4 className="font-medium mb-1">Job Performance</h4>
                  <p className="text-sm text-muted-foreground">
                    See how your job postings are performing
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Award className="w-4 h-4 text-orange-600" />
                  </div>
                  <h4 className="font-medium mb-1">Success Metrics</h4>
                  <p className="text-sm text-muted-foreground">
                    Measure hiring success and ROI
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Analytics Dashboard */}
          <AnalyticsDashboard />
        </div>
      </main>
    </div>
  )
}

export default function AnalyticsPage() {
  return (
    <ProtectedRoute requiredRole="recruiter">
      <AnalyticsContent />
    </ProtectedRoute>
  )
}
