// Zustand Stores - Centralized state management
// Re-export all stores for easy importing

export {
  useAuthStore,
  type User,
  type LoginCredentials,
  type RegisterData,
  type UpdateProfileData
} from './auth.store'

export {
  useJobsStore,
  type Job,
  type JobSearchQuery,
  type JobFilters,
  type ApplicationData,
  type PaginationData,
  type SearchMeta
} from './jobs.store'

export {
  useCompaniesStore,
  type Company,
  type CompanySearchQuery,
  type CompanyFilters
} from './companies.store'

export {
  useApplicationsStore,
  type Application,
  type ApplicationTimelineEvent,
  type ApplicationFilters,
  type ApplicationStats
} from './applications.store'

export {
  useMessagesStore,
  type Message,
  type Conversation,
  type MessageFilters
} from './messages.store'

export {
  useLocationStore,
  type LocationState,
  type JobVisibilityPreferences,
  getLocationBasedJobs
} from './location-store'

// Store utilities and hooks
import { useEffect } from 'react'
import { useAuthStore } from './auth.store'
import { useJobsStore } from './jobs.store'
import { useCompaniesStore } from './companies.store'

// Initialize stores on app startup
export function useStoreInitialization() {
  const checkAuthStatus = useAuthStore(state => state.checkAuthStatus)
  const getSavedJobs = useJobsStore(state => state.getSavedJobs)
  const getAppliedJobs = useJobsStore(state => state.getAppliedJobs)
  const getFollowedCompanies = useCompaniesStore(state => state.getFollowedCompanies)
  const isAuthenticated = useAuthStore(state => state.isAuthenticated)

  useEffect(() => {
    // Check authentication status on app load
    checkAuthStatus()
  }, [checkAuthStatus])

  useEffect(() => {
    // Load user-specific data if authenticated
    if (isAuthenticated) {
      getSavedJobs().catch(console.error)
      getAppliedJobs().catch(console.error)
      getFollowedCompanies().catch(console.error)
    }
  }, [isAuthenticated, getSavedJobs, getAppliedJobs, getFollowedCompanies])
}

// Combined selectors for common use cases
export function useAuthUser() {
  return useAuthStore(state => ({
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading
  }))
}

export function useJobSearch() {
  return useJobsStore(state => ({
    jobs: state.jobs,
    searchQuery: state.searchQuery,
    filters: state.filters,
    searchLoading: state.searchLoading,
    pagination: state.pagination,
    searchMeta: state.searchMeta,
    searchJobs: state.searchJobs,
    updateFilters: state.updateFilters,
    updateSearchQuery: state.updateSearchQuery,
    clearSearch: state.clearSearch
  }))
}

export function useJobActions() {
  return useJobsStore(state => ({
    saveJob: state.saveJob,
    unsaveJob: state.unsaveJob,
    applyToJob: state.applyToJob,
    savedJobs: state.savedJobs,
    appliedJobs: state.appliedJobs,
    saveLoading: state.saveLoading,
    applyLoading: state.applyLoading
  }))
}

export function useCompanySearch() {
  return useCompaniesStore(state => ({
    companies: state.companies,
    searchQuery: state.searchQuery,
    filters: state.filters,
    searchLoading: state.searchLoading,
    pagination: state.pagination,
    searchCompanies: state.searchCompanies,
    updateFilters: state.updateFilters,
    updateSearchQuery: state.updateSearchQuery,
    clearSearch: state.clearSearch
  }))
}

export function useCompanyActions() {
  return useCompaniesStore(state => ({
    followCompany: state.followCompany,
    unfollowCompany: state.unfollowCompany,
    followedCompanies: state.followedCompanies,
    followLoading: state.followLoading
  }))
}

// Error selectors
export function useStoreErrors() {
  const authError = useAuthStore(state => state.error)
  const jobsError = useJobsStore(state => state.error)
  const jobsSearchError = useJobsStore(state => state.searchError)
  const companiesError = useCompaniesStore(state => state.error)
  const companiesSearchError = useCompaniesStore(state => state.searchError)

  const clearAuthError = useAuthStore(state => state.clearError)
  const clearJobsError = useJobsStore(state => state.clearError)
  const clearJobsSearchError = useJobsStore(state => state.clearSearchError)
  const clearCompaniesError = useCompaniesStore(state => state.clearError)
  const clearCompaniesSearchError = useCompaniesStore(state => state.clearSearchError)

  return {
    errors: {
      auth: authError,
      jobs: jobsError,
      jobsSearch: jobsSearchError,
      companies: companiesError,
      companiesSearch: companiesSearchError
    },
    clearErrors: {
      auth: clearAuthError,
      jobs: clearJobsError,
      jobsSearch: clearJobsSearchError,
      companies: clearCompaniesError,
      companiesSearch: clearCompaniesSearchError
    },
    hasErrors: !!(authError || jobsError || jobsSearchError || companiesError || companiesSearchError)
  }
}

// Loading selectors
export function useStoreLoading() {
  const authLoading = useAuthStore(state => state.isLoading)
  const loginLoading = useAuthStore(state => state.loginLoading)
  const registerLoading = useAuthStore(state => state.registerLoading)
  
  const jobsSearchLoading = useJobsStore(state => state.searchLoading)
  const jobLoading = useJobsStore(state => state.jobLoading)
  const applyLoading = useJobsStore(state => state.applyLoading)
  const saveLoading = useJobsStore(state => state.saveLoading)
  
  const companiesSearchLoading = useCompaniesStore(state => state.searchLoading)
  const companyLoading = useCompaniesStore(state => state.companyLoading)
  const followLoading = useCompaniesStore(state => state.followLoading)

  return {
    auth: {
      general: authLoading,
      login: loginLoading,
      register: registerLoading
    },
    jobs: {
      search: jobsSearchLoading,
      job: jobLoading,
      apply: applyLoading,
      save: saveLoading
    },
    companies: {
      search: companiesSearchLoading,
      company: companyLoading,
      follow: followLoading
    },
    isAnyLoading: !!(
      authLoading || loginLoading || registerLoading ||
      jobsSearchLoading || jobLoading || applyLoading || saveLoading ||
      companiesSearchLoading || companyLoading || followLoading
    )
  }
}

// Store reset utility (useful for logout)
export function resetAllStores() {
  useJobsStore.getState().clearSearch()
  useCompaniesStore.getState().clearSearch()
  // Auth store is reset by its own logout action
}
