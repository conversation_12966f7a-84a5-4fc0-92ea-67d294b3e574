import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { errorService, type AppError } from '@/lib/error-service'

// Types
export interface Message {
  _id: string
  conversationId: string
  senderId: string
  receiverId: string
  content: string
  type: 'text' | 'system' | 'status_update'
  metadata?: {
    applicationId?: string
    oldStatus?: string
    newStatus?: string
    interviewDate?: Date
  }
  readAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface Conversation {
  _id: string
  applicationId: string
  participants: {
    candidate: {
      _id: string
      name: string
      email: string
    }
    recruiter: {
      _id: string
      name: string
      email: string
      companyId: string
    }
  }
  job: {
    _id: string
    title: string
    company: {
      name: string
      logo?: string
    }
  }
  lastMessage?: Message
  unreadCount: number
  status: 'active' | 'archived'
  createdAt: Date
  updatedAt: Date
}

export interface MessageFilters {
  conversationId?: string
  unreadOnly?: boolean
  dateRange?: {
    from?: Date
    to?: Date
  }
}

interface MessagesState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  messages: Message[]
  
  // Loading states
  conversationsLoading: boolean
  messagesLoading: boolean
  sendLoading: boolean
  
  // Error states
  error: AppError | null
  conversationsError: AppError | null
  
  // Meta
  unreadCount: number
  pagination: {
    page: number
    limit: number
    total: number
    hasMore: boolean
  }
}

interface MessagesActions {
  getConversations: (filters?: Partial<MessageFilters>) => Promise<void>
  getConversation: (conversationId: string) => Promise<void>
  getMessages: (conversationId: string, page?: number) => Promise<void>
  sendMessage: (conversationId: string, content: string, type?: Message['type'], metadata?: Message['metadata']) => Promise<void>
  markAsRead: (conversationId: string) => Promise<void>
  archiveConversation: (conversationId: string) => Promise<void>
  createConversation: (applicationId: string) => Promise<Conversation>
  clearError: () => void
  clearConversationsError: () => void
  setCurrentConversation: (conversation: Conversation | null) => void
}

// API Service functions
const MessagesAPI = {
  async getConversations(filters?: Partial<MessageFilters>) {
    const params = new URLSearchParams()
    
    if (filters?.unreadOnly) {
      params.append('unreadOnly', 'true')
    }
    if (filters?.dateRange?.from) {
      params.append('dateFrom', filters.dateRange.from.toISOString())
    }
    if (filters?.dateRange?.to) {
      params.append('dateTo', filters.dateRange.to.toISOString())
    }

    const response = await fetch(`/api/messages/conversations?${params}`, {
      headers: { 'Authorization': `Bearer ${localStorage.getItem('auth-token')}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch conversations')
    }
    return response.json()
  },

  async getConversation(conversationId: string) {
    const response = await fetch(`/api/messages/conversations/${conversationId}`, {
      headers: { 'Authorization': `Bearer ${localStorage.getItem('auth-token')}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch conversation')
    }
    return response.json()
  },

  async getMessages(conversationId: string, page = 1, limit = 50) {
    const params = new URLSearchParams()
    params.append('page', page.toString())
    params.append('limit', limit.toString())

    const response = await fetch(`/api/messages/conversations/${conversationId}/messages?${params}`, {
      headers: { 'Authorization': `Bearer ${localStorage.getItem('auth-token')}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch messages')
    }
    return response.json()
  },

  async sendMessage(conversationId: string, content: string, type: Message['type'] = 'text', metadata?: Message['metadata']) {
    const response = await fetch(`/api/messages/conversations/${conversationId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
      },
      body: JSON.stringify({ content, type, metadata })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to send message')
    }
    return response.json()
  },

  async markAsRead(conversationId: string) {
    const response = await fetch(`/api/messages/conversations/${conversationId}/read`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${localStorage.getItem('auth-token')}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to mark as read')
    }
    return response.json()
  },

  async archiveConversation(conversationId: string) {
    const response = await fetch(`/api/messages/conversations/${conversationId}/archive`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${localStorage.getItem('auth-token')}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to archive conversation')
    }
    return response.json()
  },

  async createConversation(applicationId: string) {
    const response = await fetch('/api/messages/conversations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
      },
      body: JSON.stringify({ applicationId })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to create conversation')
    }
    return response.json()
  }
}

export const useMessagesStore = create<MessagesState & MessagesActions>()(
  persist(
    (set, get) => ({
      // State
      conversations: [],
      currentConversation: null,
      messages: [],
      
      // Loading states
      conversationsLoading: false,
      messagesLoading: false,
      sendLoading: false,
      
      // Error states
      error: null,
      conversationsError: null,
      
      // Meta
      unreadCount: 0,
      pagination: { page: 1, limit: 50, total: 0, hasMore: false },

      // Actions
      getConversations: async (filters) => {
        set({ conversationsLoading: true, conversationsError: null })
        
        try {
          const response = await MessagesAPI.getConversations(filters)
          
          set({
            conversations: response.conversations,
            unreadCount: response.unreadCount || 0,
            conversationsLoading: false
          })
        } catch (error) {
          const appError = errorService.logError(error as Error, 'messages_get_conversations')
          set({ 
            conversationsError: appError, 
            conversationsLoading: false 
          })
          throw appError
        }
      },

      getConversation: async (conversationId) => {
        set({ error: null })
        
        try {
          const conversation = await MessagesAPI.getConversation(conversationId)
          set({ currentConversation: conversation })
        } catch (error) {
          const appError = errorService.logError(error as Error, 'messages_get_conversation')
          set({ error: appError })
          throw appError
        }
      },

      getMessages: async (conversationId, page = 1) => {
        set({ messagesLoading: true, error: null })
        
        try {
          const response = await MessagesAPI.getMessages(conversationId, page, get().pagination.limit)
          
          set(state => ({
            messages: page === 1 ? response.messages : [...response.messages, ...state.messages],
            pagination: response.pagination,
            messagesLoading: false
          }))
        } catch (error) {
          const appError = errorService.logError(error as Error, 'messages_get_messages')
          set({
            error: appError,
            messagesLoading: false
          })
          throw appError
        }
      },

      sendMessage: async (conversationId, content, type = 'text', metadata) => {
        set({ sendLoading: true, error: null })
        
        try {
          const message = await MessagesAPI.sendMessage(conversationId, content, type, metadata)
          
          set(state => ({
            messages: [...state.messages, message],
            conversations: state.conversations.map(conv =>
              conv._id === conversationId
                ? { ...conv, lastMessage: message, updatedAt: new Date() }
                : conv
            ),
            sendLoading: false
          }))
        } catch (error) {
          const appError = errorService.logError(error as Error, 'messages_send')
          set({
            error: appError,
            sendLoading: false
          })
          throw appError
        }
      },

      markAsRead: async (conversationId) => {
        try {
          await MessagesAPI.markAsRead(conversationId)
          
          set(state => ({
            conversations: state.conversations.map(conv =>
              conv._id === conversationId
                ? { ...conv, unreadCount: 0 }
                : conv
            ),
            unreadCount: Math.max(0, state.unreadCount - (state.conversations.find(c => c._id === conversationId)?.unreadCount || 0))
          }))
        } catch (error) {
          const appError = errorService.logError(error as Error, 'messages_mark_read')
          set({ error: appError })
          throw appError
        }
      },

      archiveConversation: async (conversationId) => {
        try {
          await MessagesAPI.archiveConversation(conversationId)
          
          set(state => ({
            conversations: state.conversations.filter(conv => conv._id !== conversationId),
            currentConversation: state.currentConversation?._id === conversationId ? null : state.currentConversation
          }))
        } catch (error) {
          const appError = errorService.logError(error as Error, 'messages_archive')
          set({ error: appError })
          throw appError
        }
      },

      createConversation: async (applicationId) => {
        try {
          const conversation = await MessagesAPI.createConversation(applicationId)
          
          set(state => ({
            conversations: [conversation, ...state.conversations]
          }))
          
          return conversation
        } catch (error) {
          const appError = errorService.logError(error as Error, 'messages_create_conversation')
          set({ error: appError })
          throw appError
        }
      },

      setCurrentConversation: (conversation) => {
        set({ currentConversation: conversation })
      },

      clearError: () => set({ error: null }),
      clearConversationsError: () => set({ conversationsError: null })
    }),
    {
      name: 'messages-storage',
      partialize: (state) => ({
        // Only persist unread count, not the actual messages
        unreadCount: state.unreadCount
      })
    }
  )
)
