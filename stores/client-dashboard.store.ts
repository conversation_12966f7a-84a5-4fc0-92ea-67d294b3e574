import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface DashboardStats {
  profileViews: number
  profileViewsChange: number
  applications: number
  applicationsChange: number
  savedJobs: number
  savedJobsChange: number
  interviews: number
  interviewsChange: number
  profileCompleteness: number
  responseRate: number
  averageResponseTime: number
}

interface RecentApplication {
  id: string
  company: string
  position: string
  status: 'applied' | 'under_review' | 'interview_scheduled' | 'offer_received' | 'rejected'
  appliedDate: string
  statusColor: string
  interviewDate?: string
  notes?: string
}

interface RecommendedJob {
  id: string
  title: string
  company: string
  location: string
  salary: string
  type: 'full-time' | 'part-time' | 'contract' | 'freelance'
  matchScore: number
  postedDate: string
  description: string
  requirements: string[]
  remote: boolean
  urgent: boolean
  featured: boolean
}

interface UpcomingInterview {
  id: string
  company: string
  position: string
  date: string
  time: string
  type: 'phone' | 'video' | 'in-person'
  interviewer: string
  notes?: string
  meetingLink?: string
}

interface JobAlert {
  id: string
  title: string
  keywords: string[]
  location: string
  salaryRange?: {
    min: number
    max: number
  }
  jobType: string
  isActive: boolean
  frequency: 'daily' | 'weekly' | 'monthly'
  lastSent?: string
  matchCount: number
}

interface ActivityItem {
  id: string
  type: 'application' | 'profile_view' | 'job_save' | 'company_follow' | 'interview'
  title: string
  description: string
  date: string
  icon: string
  metadata?: Record<string, any>
}

interface ClientDashboardState {
  // Dashboard stats
  stats: DashboardStats
  statsLoading: boolean
  
  // Recent applications
  recentApplications: RecentApplication[]
  applicationsLoading: boolean
  
  // Recommended jobs
  recommendedJobs: RecommendedJob[]
  recommendationsLoading: boolean
  
  // Upcoming interviews
  upcomingInterviews: UpcomingInterview[]
  interviewsLoading: boolean
  
  // Job alerts
  jobAlerts: JobAlert[]
  alertsLoading: boolean
  
  // Recent activity
  recentActivity: ActivityItem[]
  activityLoading: boolean
  
  // Quick actions
  quickActionsLoading: boolean
  
  // Error states
  error: string | null
  
  // Last updated
  lastUpdated: Date | null
}

interface ClientDashboardActions {
  // Fetch dashboard data
  fetchDashboardStats: () => Promise<void>
  fetchRecentApplications: () => Promise<void>
  fetchRecommendedJobs: () => Promise<void>
  fetchUpcomingInterviews: () => Promise<void>
  fetchJobAlerts: () => Promise<void>
  fetchRecentActivity: () => Promise<void>
  fetchAllDashboardData: () => Promise<void>
  
  // Quick actions
  quickApplyToJob: (jobId: string) => Promise<void>
  saveJob: (jobId: string) => Promise<void>
  unsaveJob: (jobId: string) => Promise<void>
  followCompany: (companyId: string) => Promise<void>
  
  // Job alerts
  createJobAlert: (alert: Omit<JobAlert, 'id' | 'lastSent' | 'matchCount'>) => Promise<void>
  updateJobAlert: (alertId: string, updates: Partial<JobAlert>) => Promise<void>
  deleteJobAlert: (alertId: string) => Promise<void>
  toggleJobAlert: (alertId: string) => Promise<void>
  
  // Utility
  setError: (error: string | null) => void
  clearError: () => void
  refreshDashboard: () => Promise<void>
  reset: () => void
}

type ClientDashboardStore = ClientDashboardState & ClientDashboardActions

const initialState: ClientDashboardState = {
  stats: {
    profileViews: 0,
    profileViewsChange: 0,
    applications: 0,
    applicationsChange: 0,
    savedJobs: 0,
    savedJobsChange: 0,
    interviews: 0,
    interviewsChange: 0,
    profileCompleteness: 0,
    responseRate: 0,
    averageResponseTime: 0
  },
  statsLoading: false,
  recentApplications: [],
  applicationsLoading: false,
  recommendedJobs: [],
  recommendationsLoading: false,
  upcomingInterviews: [],
  interviewsLoading: false,
  jobAlerts: [],
  alertsLoading: false,
  recentActivity: [],
  activityLoading: false,
  quickActionsLoading: false,
  error: null,
  lastUpdated: null
}

export const useClientDashboardStore = create<ClientDashboardStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Fetch dashboard stats
      fetchDashboardStats: async () => {
        set({ statsLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/clients/dashboard/stats', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch dashboard stats')
          }

          const data = await response.json()
          
          set({ 
            stats: data.data,
            statsLoading: false,
            lastUpdated: new Date()
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch stats',
            statsLoading: false 
          })
        }
      },

      // Fetch recent applications
      fetchRecentApplications: async () => {
        set({ applicationsLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/clients/applications/recent?limit=5', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch recent applications')
          }

          const data = await response.json()
          
          set({ 
            recentApplications: data.data,
            applicationsLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch applications',
            applicationsLoading: false 
          })
        }
      },

      // Fetch recommended jobs
      fetchRecommendedJobs: async () => {
        set({ recommendationsLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/clients/jobs/recommendations?limit=5', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch job recommendations')
          }

          const data = await response.json()
          
          set({ 
            recommendedJobs: data.data,
            recommendationsLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch recommendations',
            recommendationsLoading: false 
          })
        }
      },

      // Fetch upcoming interviews
      fetchUpcomingInterviews: async () => {
        set({ interviewsLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/clients/interviews/upcoming', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch upcoming interviews')
          }

          const data = await response.json()
          
          set({ 
            upcomingInterviews: data.data,
            interviewsLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch interviews',
            interviewsLoading: false 
          })
        }
      },

      // Fetch all dashboard data
      fetchAllDashboardData: async () => {
        const { 
          fetchDashboardStats, 
          fetchRecentApplications, 
          fetchRecommendedJobs, 
          fetchUpcomingInterviews 
        } = get()
        
        await Promise.all([
          fetchDashboardStats(),
          fetchRecentApplications(),
          fetchRecommendedJobs(),
          fetchUpcomingInterviews()
        ])
      },

      // Quick actions
      quickApplyToJob: async (jobId: string) => {
        set({ quickActionsLoading: true, error: null })
        
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}/apply`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to apply to job')
          }

          // Refresh applications
          await get().fetchRecentApplications()
          await get().fetchDashboardStats()
          
          set({ quickActionsLoading: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to apply to job',
            quickActionsLoading: false 
          })
        }
      },

      // Utility functions
      setError: (error: string | null) => set({ error }),
      
      clearError: () => set({ error: null }),
      
      refreshDashboard: async () => {
        await get().fetchAllDashboardData()
      },
      
      reset: () => set(initialState)
    }),
    {
      name: 'client-dashboard-store',
      partialize: (state) => ({
        stats: state.stats,
        lastUpdated: state.lastUpdated
      })
    }
  )
)
