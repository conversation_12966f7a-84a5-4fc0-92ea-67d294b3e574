import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { errorService, type AppError } from '@/lib/error-service'

// Types
export interface User {
  _id: string
  email: string
  role: 'admin' | 'company_admin' | 'recruiter' | 'job_seeker'
  profile: {
    firstName: string
    lastName: string
    avatar?: string
    phone?: string
    location?: {
      city?: string
      state?: string
      country?: string
    }
  }
  preferences: {
    emailNotifications: boolean
    jobAlerts: boolean
    marketingEmails: boolean
    theme: 'light' | 'dark' | 'system'
  }
  subscription?: {
    plan: 'free' | 'premium' | 'enterprise'
    status: 'active' | 'inactive' | 'cancelled'
  }
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  role?: 'job_seeker' | 'company_admin'
  phone?: string
  location?: {
    city?: string
    state?: string
    country?: string
  }
  company?: {
    name?: string
    website?: string
    industry?: string
    size?: string
    description?: string
    location?: {
      city?: string
      state?: string
      country?: string
    }
  }
}

export interface UpdateProfileData {
  firstName?: string
  lastName?: string
  phone?: string
  location?: {
    city?: string
    state?: string
    country?: string
  }
  preferences?: Partial<User['preferences']>
}

interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: AppError | null

  // Loading states for specific actions
  loginLoading: boolean
  registerLoading: boolean
  profileUpdateLoading: boolean
  logoutLoading: boolean
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  logout: () => Promise<void>
  refreshAuth: () => Promise<void>
  updateProfile: (profileData: UpdateProfileData) => Promise<void>
  clearError: () => void
  setLoading: (action: keyof Pick<AuthState, 'loginLoading' | 'registerLoading' | 'profileUpdateLoading' | 'logoutLoading'>, loading: boolean) => void
  checkAuthStatus: () => Promise<void>
  getRoleBasedRedirectPath: (role: string) => string
}

// API Service functions (these would be implemented in a separate service file)
const AuthAPI = {
  async login(credentials: LoginCredentials) {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Login failed')
    }

    return response.json()
  },

  async register(userData: RegisterData) {
    const response = await fetch('/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Registration failed')
    }

    return response.json()
  },

  async refreshToken(refreshToken: string) {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken })
    })

    if (!response.ok) {
      throw new Error('Token refresh failed')
    }

    return response.json()
  },

  async updateProfile(profileData: UpdateProfileData, token: string) {
    const response = await fetch('/api/auth/profile', {
      method: 'PUT',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(profileData)
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Profile update failed')
    }

    return response.json()
  },

  async getCurrentUser(token: string) {
    const response = await fetch('/api/auth/me', {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      throw new Error('Failed to get current user')
    }

    return response.json()
  }
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      loginLoading: false,
      registerLoading: false,
      profileUpdateLoading: false,
      logoutLoading: false,

      // Actions
      login: async (credentials) => {
        set({ loginLoading: true, error: null })
        try {
          const response = await AuthAPI.login(credentials)
          set({
            user: response.user,
            token: response.token,
            refreshToken: response.refreshToken,
            isAuthenticated: true,
            loginLoading: false
          })
          return response
        } catch (error) {
          const appError = errorService.logError(error as Error, 'auth_login')
          set({
            error: appError,
            loginLoading: false
          })
          throw appError
        }
      },

      register: async (userData) => {
        set({ registerLoading: true, error: null })
        try {
          const response = await AuthAPI.register(userData)
          set({
            user: response.user,
            token: response.token,
            refreshToken: response.refreshToken,
            isAuthenticated: true,
            registerLoading: false
          })
          return response
        } catch (error) {
          const appError = errorService.logError(error as Error, 'auth_register')
          set({
            error: appError,
            registerLoading: false
          })
          throw appError
        }
      },

      logout: async () => {
        set({ logoutLoading: true })
        try {
          // Clear all auth state
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            logoutLoading: false,
            error: null
          })
          
          // Clear persisted storage
          localStorage.removeItem('auth-storage')
        } catch (error) {
          set({ logoutLoading: false })
          console.error('Logout error:', error)
        }
      },

      refreshAuth: async () => {
        const { refreshToken } = get()
        if (!refreshToken) {
          throw new Error('No refresh token available')
        }

        try {
          const response = await AuthAPI.refreshToken(refreshToken)
          set({
            token: response.token,
            refreshToken: response.refreshToken || refreshToken
          })
        } catch (error) {
          // If refresh fails, logout user
          get().logout()
          throw error
        }
      },

      updateProfile: async (profileData) => {
        const { token } = get()
        if (!token) {
          throw new Error('No authentication token')
        }

        set({ profileUpdateLoading: true, error: null })
        try {
          const updatedUser = await AuthAPI.updateProfile(profileData, token)
          set({
            user: updatedUser,
            profileUpdateLoading: false
          })
        } catch (error) {
          const appError = errorService.logError(error as Error, 'auth_update_profile')
          set({
            error: appError,
            profileUpdateLoading: false
          })
          throw appError
        }
      },

      checkAuthStatus: async () => {
        const { token } = get()
        if (!token) {
          return
        }

        try {
          const user = await AuthAPI.getCurrentUser(token)
          set({ user, isAuthenticated: true })
        } catch (error) {
          // Token is invalid, logout user
          get().logout()
        }
      },

      getRoleBasedRedirectPath: (role: string) => {
        switch (role) {
          case 'company_admin':
            return '/dashboard'
          case 'recruiter':
            return '/dashboard'
          case 'job_seeker':
            return '/client-dashboard'
          case 'admin':
            return '/admin/dashboard'
          default:
            return '/client-dashboard'
        }
      },

      clearError: () => set({ error: null }),

      setLoading: (action, loading) => {
        set({ [action]: loading })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
