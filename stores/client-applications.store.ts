import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface Application {
  id: string
  jobId: string
  jobTitle: string
  company: string
  companyLogo?: string
  location: string
  salary?: string
  appliedDate: string
  status: 'applied' | 'under_review' | 'interview_scheduled' | 'offer_received' | 'rejected' | 'withdrawn'
  statusText: string
  statusColor: string
  interviewDate?: string
  interviewType?: 'phone' | 'video' | 'in-person'
  notes?: string
  lastUpdate: string
  timeline: Array<{
    status: string
    date: string
    notes?: string
  }>
  documents: Array<{
    type: 'resume' | 'cover_letter' | 'portfolio'
    filename: string
    url: string
  }>
  feedback?: string
  nextSteps?: string
  contactPerson?: {
    name: string
    email: string
    role: string
  }
}

interface ApplicationFilters {
  status?: string
  company?: string
  dateRange?: {
    start: string
    end: string
  }
  location?: string
  salaryRange?: {
    min: number
    max: number
  }
}

interface ApplicationStats {
  total: number
  pending: number
  interviews: number
  offers: number
  rejected: number
  successRate: number
  averageResponseTime: number
  thisWeek: number
  thisMonth: number
}

interface ClientApplicationsState {
  // Applications data
  applications: Application[]
  filteredApplications: Application[]
  
  // Loading states
  loading: boolean
  submitting: boolean
  updating: boolean
  
  // Filters and search
  filters: ApplicationFilters
  searchQuery: string
  sortBy: 'date' | 'company' | 'status' | 'salary'
  sortOrder: 'asc' | 'desc'
  
  // Pagination
  currentPage: number
  totalPages: number
  totalCount: number
  pageSize: number
  
  // Stats
  stats: ApplicationStats
  statsLoading: boolean
  
  // Selected application
  selectedApplication: Application | null
  
  // Error state
  error: string | null
}

interface ClientApplicationsActions {
  // Fetch applications
  fetchApplications: (page?: number, limit?: number) => Promise<void>
  fetchApplicationById: (applicationId: string) => Promise<void>
  fetchApplicationStats: () => Promise<void>
  
  // Apply to jobs
  applyToJob: (jobId: string, applicationData: {
    coverLetter?: string
    resumeId?: string
    additionalDocuments?: string[]
    customAnswers?: Record<string, string>
  }) => Promise<void>
  
  // Update applications
  updateApplicationStatus: (applicationId: string, status: string, notes?: string) => Promise<void>
  withdrawApplication: (applicationId: string, reason?: string) => Promise<void>
  addApplicationNote: (applicationId: string, note: string) => Promise<void>
  
  // Interview management
  scheduleInterview: (applicationId: string, interviewData: {
    date: string
    time: string
    type: 'phone' | 'video' | 'in-person'
    interviewer?: string
    notes?: string
  }) => Promise<void>
  
  // Filtering and search
  setFilters: (filters: Partial<ApplicationFilters>) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  applyFilters: () => void
  
  // Pagination
  setPage: (page: number) => void
  setPageSize: (size: number) => void
  
  // Selection
  setSelectedApplication: (application: Application | null) => void
  
  // Utility
  setError: (error: string | null) => void
  clearError: () => void
  reset: () => void
}

type ClientApplicationsStore = ClientApplicationsState & ClientApplicationsActions

const initialState: ClientApplicationsState = {
  applications: [],
  filteredApplications: [],
  loading: false,
  submitting: false,
  updating: false,
  filters: {},
  searchQuery: '',
  sortBy: 'date',
  sortOrder: 'desc',
  currentPage: 1,
  totalPages: 1,
  totalCount: 0,
  pageSize: 10,
  stats: {
    total: 0,
    pending: 0,
    interviews: 0,
    offers: 0,
    rejected: 0,
    successRate: 0,
    averageResponseTime: 0,
    thisWeek: 0,
    thisMonth: 0
  },
  statsLoading: false,
  selectedApplication: null,
  error: null
}

export const useClientApplicationsStore = create<ClientApplicationsStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Fetch applications
      fetchApplications: async (page = 1, limit = 10) => {
        set({ loading: true, error: null })
        
        try {
          const { filters, searchQuery, sortBy, sortOrder } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            sortBy,
            sortOrder,
            ...(searchQuery && { search: searchQuery }),
            ...(filters.status && { status: filters.status }),
            ...(filters.company && { company: filters.company })
          })

          const response = await fetch(`/api/v1/clients/applications?${params}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch applications')
          }

          const data = await response.json()
          
          set({ 
            applications: data.data.applications,
            filteredApplications: data.data.applications,
            currentPage: data.data.currentPage,
            totalPages: data.data.totalPages,
            totalCount: data.data.totalCount,
            loading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch applications',
            loading: false 
          })
        }
      },

      // Apply to job
      applyToJob: async (jobId: string, applicationData) => {
        set({ submitting: true, error: null })
        
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}/apply`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(applicationData)
          })

          if (!response.ok) {
            throw new Error('Failed to submit application')
          }

          // Refresh applications and stats
          await get().fetchApplications()
          await get().fetchApplicationStats()
          
          set({ submitting: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to submit application',
            submitting: false 
          })
        }
      },

      // Update application status
      updateApplicationStatus: async (applicationId: string, status: string, notes?: string) => {
        set({ updating: true, error: null })
        
        try {
          const response = await fetch(`/api/v1/applications/${applicationId}/status`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status, notes })
          })

          if (!response.ok) {
            throw new Error('Failed to update application status')
          }

          // Update local state
          const { applications } = get()
          const updatedApplications = applications.map(app => 
            app.id === applicationId 
              ? { ...app, status: status as any, lastUpdate: new Date().toISOString() }
              : app
          )
          
          set({ 
            applications: updatedApplications,
            filteredApplications: updatedApplications,
            updating: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update application',
            updating: false 
          })
        }
      },

      // Fetch application stats
      fetchApplicationStats: async () => {
        set({ statsLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/clients/applications/stats', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch application stats')
          }

          const data = await response.json()
          
          set({ 
            stats: data.data,
            statsLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch stats',
            statsLoading: false 
          })
        }
      },

      // Filtering and search
      setFilters: (filters: Partial<ApplicationFilters>) => {
        set(state => ({ 
          filters: { ...state.filters, ...filters }
        }))
        get().applyFilters()
      },

      clearFilters: () => {
        set({ filters: {} })
        get().applyFilters()
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
        get().applyFilters()
      },

      setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => {
        set({ sortBy: sortBy as any, sortOrder })
        get().applyFilters()
      },

      applyFilters: () => {
        const { applications, filters, searchQuery } = get()
        
        let filtered = [...applications]
        
        // Apply search
        if (searchQuery) {
          filtered = filtered.filter(app => 
            app.jobTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
            app.company.toLowerCase().includes(searchQuery.toLowerCase())
          )
        }
        
        // Apply status filter
        if (filters.status) {
          filtered = filtered.filter(app => app.status === filters.status)
        }
        
        // Apply company filter
        if (filters.company) {
          filtered = filtered.filter(app => 
            app.company.toLowerCase().includes(filters.company!.toLowerCase())
          )
        }
        
        set({ filteredApplications: filtered })
      },

      // Pagination
      setPage: (page: number) => {
        set({ currentPage: page })
        get().fetchApplications(page)
      },

      setPageSize: (size: number) => {
        set({ pageSize: size, currentPage: 1 })
        get().fetchApplications(1, size)
      },

      // Selection
      setSelectedApplication: (application: Application | null) => {
        set({ selectedApplication: application })
      },

      // Utility
      setError: (error: string | null) => set({ error }),
      
      clearError: () => set({ error: null }),
      
      reset: () => set(initialState)
    }),
    {
      name: 'client-applications-store',
      partialize: (state) => ({
        filters: state.filters,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        pageSize: state.pageSize
      })
    }
  )
)
