import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface Job {
  id: string
  title: string
  company: string
  companyLogo?: string
  location: string
  salary?: string
  type: 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship'
  experience: 'entry' | 'mid' | 'senior' | 'executive'
  description: string
  requirements: string[]
  responsibilities: string[]
  benefits?: string[]
  skills: string[]
  postedDate: string
  expiryDate?: string
  remote: boolean
  urgent: boolean
  featured: boolean
  verified: boolean
  applicants: number
  matchScore?: number
  status: 'active' | 'expired' | 'filled' | 'paused'
  applicationDeadline?: string
  contactEmail?: string
  applicationUrl?: string
  companyId: string
  industry: string[]
  workArrangement: string[]
}

interface JobFilters {
  keywords?: string
  location?: string
  remote?: boolean
  jobType?: string[]
  experience?: string[]
  salary?: {
    min: number
    max: number
  }
  industry?: string[]
  company?: string
  postedWithin?: string // '24h', '7d', '30d'
  workArrangement?: string[]
  benefits?: string[]
}

interface SavedJob {
  jobId: string
  savedDate: string
  notes?: string
  tags?: string[]
}

interface JobAlert {
  id: string
  title: string
  filters: JobFilters
  frequency: 'daily' | 'weekly' | 'monthly'
  isActive: boolean
  lastSent?: string
  matchCount: number
  createdDate: string
}

interface ClientJobsState {
  // Jobs data
  jobs: Job[]
  filteredJobs: Job[]
  savedJobs: SavedJob[]
  recommendedJobs: Job[]
  
  // Search and filters
  searchQuery: string
  filters: JobFilters
  sortBy: 'relevance' | 'date' | 'salary' | 'company'
  sortOrder: 'asc' | 'desc'
  
  // Pagination
  currentPage: number
  totalPages: number
  totalCount: number
  pageSize: number
  
  // Loading states
  loading: boolean
  searchLoading: boolean
  recommendationsLoading: boolean
  savedJobsLoading: boolean
  
  // Job alerts
  jobAlerts: JobAlert[]
  alertsLoading: boolean
  
  // Selected job
  selectedJob: Job | null
  
  // Error state
  error: string | null
}

interface ClientJobsActions {
  // Fetch jobs
  fetchJobs: (page?: number, limit?: number) => Promise<void>
  searchJobs: (query: string, filters?: JobFilters) => Promise<void>
  fetchJobById: (jobId: string) => Promise<void>
  fetchRecommendedJobs: () => Promise<void>
  
  // Saved jobs
  fetchSavedJobs: () => Promise<void>
  saveJob: (jobId: string, notes?: string, tags?: string[]) => Promise<void>
  unsaveJob: (jobId: string) => Promise<void>
  updateSavedJobNotes: (jobId: string, notes: string) => Promise<void>
  
  // Job alerts
  fetchJobAlerts: () => Promise<void>
  createJobAlert: (alert: Omit<JobAlert, 'id' | 'createdDate' | 'lastSent' | 'matchCount'>) => Promise<void>
  updateJobAlert: (alertId: string, updates: Partial<JobAlert>) => Promise<void>
  deleteJobAlert: (alertId: string) => Promise<void>
  toggleJobAlert: (alertId: string) => Promise<void>
  
  // Filtering and search
  setSearchQuery: (query: string) => void
  setFilters: (filters: Partial<JobFilters>) => void
  clearFilters: () => void
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  applyFilters: () => void
  
  // Pagination
  setPage: (page: number) => void
  setPageSize: (size: number) => void
  
  // Selection
  setSelectedJob: (job: Job | null) => void
  
  // Utility
  setError: (error: string | null) => void
  clearError: () => void
  reset: () => void
}

type ClientJobsStore = ClientJobsState & ClientJobsActions

const initialState: ClientJobsState = {
  jobs: [],
  filteredJobs: [],
  savedJobs: [],
  recommendedJobs: [],
  searchQuery: '',
  filters: {},
  sortBy: 'relevance',
  sortOrder: 'desc',
  currentPage: 1,
  totalPages: 1,
  totalCount: 0,
  pageSize: 20,
  loading: false,
  searchLoading: false,
  recommendationsLoading: false,
  savedJobsLoading: false,
  jobAlerts: [],
  alertsLoading: false,
  selectedJob: null,
  error: null
}

export const useClientJobsStore = create<ClientJobsStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Fetch jobs
      fetchJobs: async (page = 1, limit = 20) => {
        set({ loading: true, error: null })
        
        try {
          const { filters, searchQuery, sortBy, sortOrder } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            sortBy,
            sortOrder,
            ...(searchQuery && { search: searchQuery }),
            ...(filters.location && { location: filters.location }),
            ...(filters.remote !== undefined && { remote: filters.remote.toString() }),
            ...(filters.jobType && { jobType: filters.jobType.join(',') }),
            ...(filters.experience && { experience: filters.experience.join(',') })
          })

          const response = await fetch(`/api/v1/jobs?${params}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch jobs')
          }

          const data = await response.json()
          
          set({ 
            jobs: data.data.jobs,
            filteredJobs: data.data.jobs,
            currentPage: data.data.currentPage,
            totalPages: data.data.totalPages,
            totalCount: data.data.totalCount,
            loading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch jobs',
            loading: false 
          })
        }
      },

      // Search jobs
      searchJobs: async (query: string, filters?: JobFilters) => {
        set({ searchLoading: true, error: null, searchQuery: query })
        
        if (filters) {
          set(state => ({ filters: { ...state.filters, ...filters } }))
        }
        
        await get().fetchJobs(1)
        set({ searchLoading: false })
      },

      // Fetch recommended jobs
      fetchRecommendedJobs: async () => {
        set({ recommendationsLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/clients/jobs/recommendations', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch recommended jobs')
          }

          const data = await response.json()
          
          set({ 
            recommendedJobs: data.data,
            recommendationsLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch recommendations',
            recommendationsLoading: false 
          })
        }
      },

      // Save job
      saveJob: async (jobId: string, notes?: string, tags?: string[]) => {
        try {
          const response = await fetch('/api/v1/clients/jobs/save', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ jobId, notes, tags })
          })

          if (!response.ok) {
            throw new Error('Failed to save job')
          }

          // Update local state
          const newSavedJob: SavedJob = {
            jobId,
            savedDate: new Date().toISOString(),
            notes,
            tags
          }
          
          set(state => ({ 
            savedJobs: [...state.savedJobs, newSavedJob]
          }))
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to save job'
          })
        }
      },

      // Unsave job
      unsaveJob: async (jobId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/jobs/unsave/${jobId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to unsave job')
          }

          // Update local state
          set(state => ({ 
            savedJobs: state.savedJobs.filter(job => job.jobId !== jobId)
          }))
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to unsave job'
          })
        }
      },

      // Filtering and search
      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },

      setFilters: (filters: Partial<JobFilters>) => {
        set(state => ({ 
          filters: { ...state.filters, ...filters }
        }))
      },

      clearFilters: () => {
        set({ filters: {}, searchQuery: '' })
        get().fetchJobs(1)
      },

      setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => {
        set({ sortBy: sortBy as any, sortOrder })
        get().fetchJobs(get().currentPage)
      },

      applyFilters: () => {
        get().fetchJobs(1)
      },

      // Pagination
      setPage: (page: number) => {
        set({ currentPage: page })
        get().fetchJobs(page)
      },

      setPageSize: (size: number) => {
        set({ pageSize: size, currentPage: 1 })
        get().fetchJobs(1, size)
      },

      // Selection
      setSelectedJob: (job: Job | null) => {
        set({ selectedJob: job })
      },

      // Utility
      setError: (error: string | null) => set({ error }),
      
      clearError: () => set({ error: null }),
      
      reset: () => set(initialState)
    }),
    {
      name: 'client-jobs-store',
      partialize: (state) => ({
        savedJobs: state.savedJobs,
        filters: state.filters,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        pageSize: state.pageSize
      })
    }
  )
)
